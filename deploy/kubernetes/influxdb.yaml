apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: influxdb
  name: influxdb
  namespace: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: influxdb
  serviceName: influxdb
  template:
    metadata:
      labels:
        app: influxdb
    spec:
      containers:
      - image: mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/influxdb:2.0.6
        name: influxdb
        ports:
        - containerPort: 8086
          name: influxdb
        env:
        - name: DOCKER_INFLUXDB_INIT_USERNAME
          value: admin
        - name: DOCKER_INFLUXDB_INIT_PASSWORD
          value: Admin123
        - name: DOCKER_INFLUXDB_INIT_ORG
          value: makeblock-devops
        - name: DOCKER_INFLUXDB_INIT_BUCKET
          value: pipeline
        - name: DOCKER_INFLUXDB_INIT_RETENTION
          value: 1w
        - name: DOCKER_INFLUXDB_INIT_ADMIN_TOKEN
          value: Admin123
        - name: DOCKER_INFLUXDB_INIT_MODE
          value: setup
        volumeMounts:
        - mountPath: /var/lib/influxdb2
          name: data
        resources:
          requests:
            cpu: 5m
            memory: 50Mi
          limits:
            cpu: 200m
            memory: 500Mi
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: pipeline-influxdb-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: influxdb
  namespace: database
spec:
  ports:
    - name: influxdb
      port: 8086
      targetPort: 8086
  selector:
    app: influxdb
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pipeline-influxdb-pv
  namespace: database
  labels:
    alicloud-pvname: d-wz9atssdz3nvx6dki94y
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  csi:
    driver: diskplugin.csi.alibabacloud.com
    volumeHandle: d-wz9atssdz3nvx6dki94y
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
            - key: topology.diskplugin.csi.alibabacloud.com/zone
              operator: In
              values:
                - cn-shenzhen-a
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: pipeline-influxdb-pvc
  namespace: database
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  selector:
    matchLabels:
      alicloud-pvname: d-wz9atssdz3nvx6dki94y
