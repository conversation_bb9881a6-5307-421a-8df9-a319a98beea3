apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config-pipeline
  namespace: prod-common
data:
  host: '*************'
  port: '3306'
  user: 'pipeline'
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config-pipeline
  namespace: prod-common
data:
  host: '*************'
  port: '6379'
  user: ''
---
# influxdb
apiVersion: v1
kind: ConfigMap
metadata:
  name: influxdb-config-pipeline
  namespace: prod-common
data:
  url: 'http://*************:8086'
  org: 'makeblock-devops'
