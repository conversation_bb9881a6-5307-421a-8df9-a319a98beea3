apiVersion: v1
kind: Service
metadata:
  annotations:
    git.commit.id: {{.GIT_COMMIT_SHA}}
  labels:
    app: pipeline
  name: pipeline
  namespace: prod-common
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
    - name: grpc
      port: 8000
      targetPort: 8000
  selector:
    app: pipeline
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    git.commit.id: {{.GIT_COMMIT_SHA}}
  labels:
    app: pipeline
    version: v1
  name: pipeline
  namespace: prod-common
spec:
  minReadySeconds: 1
  selector:
    matchLabels:
      app: pipeline
      version: v1
  template:
    metadata:
      annotations:
        git.commit.id: {{.GIT_COMMIT_SHA}}
      labels:
        app: pipeline
        enableMKScrape: "true"
        version: v1
    spec:
      containers:
        - env:
            - name: PROJECT_ENV
              value: prod
            - name: MYSQL_DBNAME
              value: pipeline
            - name: MYSQL_HOST
              valueFrom:
                configMapKeyRef:
                  key: host
                  name: mysql-config-pipeline
            - name: MYSQL_PORT
              valueFrom:
                configMapKeyRef:
                  key: port
                  name: mysql-config-pipeline
            - name: MYSQL_USER
              valueFrom:
                configMapKeyRef:
                  key: user
                  name: mysql-config-pipeline
            - name: MYSQL_PWD
              valueFrom:
                secretKeyRef:
                  key: pwd
                  name: mysql-secret-pipeline
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  key: host
                  name: redis-config-pipeline
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  key: port
                  name: redis-config-pipeline
            - name: REDIS_PWD
              valueFrom:
                secretKeyRef:
                  key: pwd
                  name: redis-secret-pipeline
            # RPC服务配置
            - name: RPC_ACCOUNT
              value: account-service.prod-passport:8000
            - name: RPC_EFFICACY
              value: efficacy-service.prod-common:8000
            - name: PIPELINE_URL
              value: https://efficacy.makeblock.com/#/cicd/pipeline?execute=
            - name: WEBHOOK_ENDPOINT
              value: https://pipeline.makeblock.com
            # 前端端点配置
            - name: FRONT_END_ENDPOINT
              value: https://devops.makeblock.com
            # API端点配置
            - name: EFFICACY_API_ENDPOINT
              value: https://api.makeblock.com
            # 功能开关配置
            - name: ENABLE_CRON
              value: "true"
            - name: APP_CONFIG_AUTO_APPROVAL
              value: "true"
            - name: RUNNER_OFFLINE_NOTIFY_ENABLE
              value: "true"
            # OSS MinIO配置
            - name: OSS_MINIO_ENDPOINT
              value: minio.makeblock.com:9000
            - name: OSS_MINIO_ACCESSKEY
              value: Pw4qJ0FMyyaTaNwJh4jw
            - name: OSS_MINIO_SECRETKEY
              value: BsJni1goQNT97ttgoGv9GTIpLhlZQRxFXscsoNqO
            - name: SENTRY_DSN
              value: https://<EMAIL>/14
            - name: SUBSCRIBES_MONITOR
              value: https://alertmanager.makeblock.com/service/v1/sync
            # InfluxDB配置
            - name: INFLUXDB_URL
              valueFrom:
                configMapKeyRef:
                  key: url
                  name: influxdb-config-pipeline
            - name: INFLUXDB_RUNNER_ORG
              valueFrom:
                configMapKeyRef:
                  key: org
                  name: influxdb-config-pipeline
            - name: INFLUXDB_TOKEN
              valueFrom:
                secretKeyRef:
                  key: pwd
                  name: influxdb-secret-pipeline
            # 审批通知配置
            - name: APP_CONFIG_APPROVAL_NOTICE_CHAT_ID
              value: oc_7880791cc905b441763e64faeb71994e
            - name: APP_CONFIG_APPROVAL_INFO_BASEURL
              value: https://efficacy.makeblock.com/#/cicd/application?action=config&uuid=%s&plugin=%s
            # LLM配置
            - name: LLM_CONFIG_API_KEY
              value: 258368622242308293_linz4_arkdeeps_cn
            - name: LLM_CONFIG_BASE_URL
              value: https://api.makeblock.com/store-api/v1/compatible-mode/v1
            # RabbitMQ配置
            - name: RABBITMQ_URL
              value: ***************************************************************
            - name: RABBITMQ_QUEUE
              value: pipeline-prod
            # Kubernetes相关配置
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: APP_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['app']
            - name: JAEGER_SERVICE_NAME
              value: $(APP_NAME).$(NAMESPACE)
            - name: TRACING_AUTO_CREATE_SPAN
              value: "0"
          image: registry.cn-hangzhou.aliyuncs.com/makeblock/pipeline
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
          name: pipeline
          ports:
            - containerPort: 8080
              name: http
            - containerPort: 13116
              name: metrics
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 5m
              memory: 50Mi
      imagePullSecrets:
        - name: mb-hangzhou
      restartPolicy: Always
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: pipeline
  namespace: prod-common
spec:
  maxReplicas: 1
  metrics:
    - resource:
        name: memory
        target:
          averageUtilization: 90
          type: Utilization
      type: Resource
    - resource:
        name: cpu
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: pipeline
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: pipeline
  namespace: prod-common
spec:
  gateways:
    - gateway/makeblock-gateway
  hosts:
    - pipeline.makeblock.com
  http:
    - match:
        - port: 443
      route:
        - destination:
            host: pipeline
            port:
              number: 8080
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: pipeline
  namespace: prod-common
spec:
  host: pipeline
  subsets:
    - labels:
        version: v1
      name: v1
  trafficPolicy:
    loadBalancer:
      simple: RANDOM
