apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: pipeline
  namespace: prod-common
spec:
  host: pipeline
  trafficPolicy:
    loadBalancer:
      simple: RANDOM
  subsets:
    - name: v1
      labels:
        version: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: pipeline
  namespace: prod-common
spec:
  gateways:
    - gateway/makeblock-gateway
  hosts:
    - "pipeline.makeblock.com"
  http:
    - match:
        - port: 443
      route:
        - destination:
            host: pipeline
            port:
              number: 8080
