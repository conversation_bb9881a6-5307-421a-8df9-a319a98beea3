#  API_VERSION=$API_VERSION APP_VERSION=$APP_VERSION MYSQL_PASSWORD=$PASSWORD REDIS_PWD=$PASSWORD MINIO_PASSWORD=$PASSWORD MYSQL_ROOT_PASSWORD=$PASSWORD  docker compose up -d
version: '3.7'
services:
  pipeline: # 服务端
    image: registry.cn-hangzhou.aliyuncs.com/makeblock/pipeline:v${APP_VERSION}-prod
    dns:
      - *************
    container_name: pipeline
    ports:
    - "8080:8080"
    environment:
      - PROJECT_ENV=prod
      - API_VERSION=${API_VERSION}
      # mysql
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=pipeline
      - MYSQL_PWD=${MYSQL_PASSWORD}
      - MYSQL_DBNAME=pipeline
      # pipeline
      - WEBHOOK_ENDPOINT=https://pipeline.makeblock.com
      - EFFICACY_API_ENDPOINT=https://api.makeblock.com
      - ENABLE_CRON=true # 是否启用定时任务, 测试时关掉, 等正式环境再开启
      - FRONT_END_ENDPOINT=https://devops.makeblock.com
      - APP_CONFIG_AUTO_APPROVAL=true
      # minio
      - OSS_MINIO_ENDPOINT=minio.makeblock.com:9000
      - OSS_MINIO_ACCESSKEY=Pw4qJ0FMyyaTaNwJh4jw
      - OSS_MINIO_SECRETKEY=BsJni1goQNT97ttgoGv9GTIpLhlZQRxFXscsoNqO
      - SENTRY_DSN=https://<EMAIL>/14
      - SUBSCRIBES_MONITOR=https://alertmanager.makeblock.com/service/v1/sync
      - APP_CONFIG_APPROVAL_NOTICE_CHAT_ID=oc_7880791cc905b441763e64faeb71994e
      # redis
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PWD=${REDIS_PWD}
      - INFLUXDB_URL=http://influxdb:8086
      - RUNNER_OFFLINE_NOTIFY_ENABLE=true
      # llm
      - LLM_CONFIG_API_KEY=258368622242308293_linz4_arkdeeps_cn
      - LLM_CONFIG_BASE_URL=https://api.makeblock.com/store-api/v1/compatible-mode/v1
      # rabbitmq
      - RABBITMQ_URL=***************************************************************
      - RABBITMQ_QUEUE=pipeline-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/healthz"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: always
    pull_policy: always
    depends_on:
      - mysql
      - redis
      - influxdb
  #      - minio
  mysql: # 数据库
    image: mysql:8.4.0-oraclelinux8
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      - LANG=C.UTF-8
      - MYSQL_DATABASE=pipeline
      - MYSQL_USER=pipeline
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
    volumes:
      - ./mysql:/var/lib/mysql
    command: [ '--sql-mode=STRICT_TRANS_TABLES', '--character-set-server=utf8mb4', '--collation-server=utf8mb4_unicode_ci' ]
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: always
  redis: # 缓存
    image: redis:6.0
    container_name: redis
    command: /bin/sh -c "redis-server --requirepass ${REDIS_PWD} --appendonly yes"
    ports:
      - "6379:6379"
    volumes:
      - ./redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: always
  influxdb: # 时序数据库
    image: mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/influxdb:2.0.6
    container_name: influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=F6969B3F-471B-48F9-A878-10AE404942BE
      - DOCKER_INFLUXDB_INIT_ORG=makeblock-devops
      - DOCKER_INFLUXDB_INIT_BUCKET=pipeline
      - DOCKER_INFLUXDB_INIT_RETENTION=1w
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=F6969B3F-471B-48F9-A878-10AE404942BE
      - DOCKER_INFLUXDB_INIT_MODE=setup
    volumes:
      - ./influxdb:/var/lib/influxdb2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/ping"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: always
#  minio: # 对象存储
#    image: minio/minio:RELEASE.2023-09-07T02-05-02Z
#    container_name: minio
#    ports:
#      - "9000:9000"
#      - "9001:9001"
#    environment:
#      - MINIO_ROOT_USER=minio
#      - MINIO_ROOT_PASSWORD=${MINIO_PASSWORD}
#    volumes:
#      - ./minio:/data
#    command: server --console-address ':9001' /data
#    privileged: true
#    healthcheck:
#      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
#      interval: 30s
#      timeout: 20s
#      retries: 3
#    restart: always
#  nginx: # 反向代理
#    image: nginx:1.21.3
#    container_name: nginx
#    ports:
#      - "80:80"
#      - "443:443"
#    volumes:
#      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
#      - ./nginx/conf.d:/etc/nginx/conf.d
#      - ./nginx/ssl:/etc/nginx/ssl
#      - ./nginx/sites-enabled:/etc/nginx/sites-enabled
#    healthcheck:
#      test: ["CMD", "curl", "-f", "http://localhost"]
#      interval: 30s
#      timeout: 20s
#      retries: 3
#    restart: always