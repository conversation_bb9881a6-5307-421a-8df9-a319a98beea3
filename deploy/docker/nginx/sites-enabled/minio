server {
    listen  443; # HTTPS 需要使用的端口
    server_name minio.makeblock.com; # 域名
    ssl on;
    charset utf-8;
    ssl_certificate    /etc/nginx/ssl/makeblock-crt.pem; # 网站新证书路径
    ssl_certificate_key    /etc/nginx/ssl/makeblock-key.pem; # 网站新私钥路径

    # 日志
    access_log /var/log/nginx/minio.access.log;
    error_log /var/log/nginx/minio.error.log;

    location / {
        proxy_pass http://minio:9001/;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}