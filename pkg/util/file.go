package util

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	//nolint:gosec
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

type FileData struct {
	Path    string
	Content string
}

func ReadFiles(inputPath string) ([]FileData, error) {
	var files []FileData
	err := filepath.Walk(inputPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			relativePath := strings.TrimPrefix(path, inputPath+"/")
			files = append(files, FileData{
				Path:    relativePath,
				Content: string(content),
			})
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return files, nil
}

func DeleteFolder(path string) error {
	err := os.RemoveAll(path)
	if err != nil {
		// On Windows, try enhanced deletion if normal deletion fails
		if runtime.GOOS == "windows" {
			return deleteWithWindowsEnhancement(path, err)
		}
		return fmt.Errorf("failed to delete folder: %v", err)
	}
	return nil
}

// CopyFile copy file
func CopyFile(source string, dest string) (err error) {
	// Gather file information to set back later.
	sourceInfo, err := os.Lstat(source)
	if err != nil {
		return err
	}
	// Handle symbolic link.
	if sourceInfo.Mode()&os.ModeSymlink != 0 {
		target, readlinkErr := os.Readlink(source)
		if readlinkErr != nil {
			return readlinkErr
		}
		// NOTE: os.Chmod and os.Chtimes don't recognize symbolic link,
		// which will lead "no such file or directory" error.
		return os.Symlink(target, dest)
	}
	sourcefile, err := os.Open(source)
	if err != nil {
		return err
	}
	defer sourcefile.Close()
	destFile, err := os.Create(dest)
	if err != nil {
		return err
	}
	defer destFile.Close()
	if _, err = io.Copy(destFile, sourcefile); err != nil {
		return err
	}
	// Set back file information.
	if err = os.Chtimes(dest, sourceInfo.ModTime(), sourceInfo.ModTime()); err != nil {
		return err
	}
	// Set back file mode.
	return os.Chmod(dest, sourceInfo.Mode())
}

// CopyDir recursive copy of directory
func CopyDir(source string, dest string) (err error) {
	// get properties of source dir
	sourceInfo, err := os.Stat(source)
	if err != nil {
		return err
	}
	// create dest dir
	err = os.MkdirAll(dest, sourceInfo.Mode())
	if err != nil {
		return err
	}
	objects, err := os.ReadDir(source)
	// read all contents of source dir
	for _, obj := range objects {
		sourceFilePointer := source + "/" + obj.Name()
		destinationFilePointer := dest + "/" + obj.Name()
		if obj.IsDir() {
			// create sub-directories - recursively
			err = CopyDir(sourceFilePointer, destinationFilePointer)
			if err != nil {
				return err
			}
		} else {
			// perform copy
			err = CopyFile(sourceFilePointer, destinationFilePointer)
			if err != nil {
				return err
			}
		}
	}
	return err
}

// CreateFolder 创建文件夹
func CreateFolder(path string) error {
	exist, err := HasDir(path)
	if err != nil {
		return err
	}
	if exist {
		return nil
	} else {
		err := os.MkdirAll(path, os.ModePerm)
		if err != nil {
			return err
		}
	}
	return nil
}

// HasDir 判断文件夹是否存在
func HasDir(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func FileExisted(path string) bool {
	_, err := os.Stat(path)
	return err == nil || os.IsExist(err)
}

func IsDir(path string) bool {
	file, err := os.Stat(path)
	if err != nil {
		return false
	}
	return file.IsDir()
}

// IsFile check if the given path is a file
func IsFile(file string) bool {
	s, err := os.Stat(file)
	if err != nil {
		return false
	}
	return !s.IsDir()
}

// ZipFiles 将文件列表压缩到指定的 zip 文件中
func ZipFiles(destZip string, files []string) error {
	newZipFile, err := os.Create(destZip)
	if err != nil {
		return err
	}
	defer func() {
		_ = newZipFile.Close()
	}()
	zipWriter := zip.NewWriter(newZipFile)
	defer func() {
		_ = zipWriter.Close()
	}()
	for _, file := range files {
		err = func(file string) error {
			zipFile, fileOpenErr := os.Open(file)
			if fileOpenErr != nil {
				return fileOpenErr
			}
			defer zipFile.Close()
			info, statErr := zipFile.Stat()
			if statErr != nil {
				return statErr
			}
			header, fihErr := zip.FileInfoHeader(info)
			if fihErr != nil {
				return fihErr
			}
			header.Name = filepath.Base(file)
			header.Method = zip.Deflate
			writer, chErr := zipWriter.CreateHeader(header)
			if chErr != nil {
				return chErr
			}
			_, err = io.Copy(writer, zipFile)
			return err
		}(file)
		if err != nil {
			return err
		}
	}
	return nil
}

func RemoveHomeDir(path string) (string, error) {
	if strings.HasPrefix(path, "~") {
		home, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}
		path = strings.Replace(path, "~", home, 1)
	}
	return path, nil
}

func ZipFolder(srcFolder string, destZip string) error {
	zipFile, err := os.Create(destZip)
	if err != nil {
		return err
	}
	defer zipFile.Close()
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()
	err = filepath.Walk(srcFolder, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}
		header.Name, err = filepath.Rel(srcFolder, path)
		if err != nil {
			return err
		}
		if info.IsDir() {
			header.Name += "/"
		} else {
			header.Method = zip.Deflate
		}
		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			return err
		}
		if !info.IsDir() {
			file, openErr := os.Open(path)
			if openErr != nil {
				return openErr
			}
			defer file.Close()
			_, err = io.Copy(writer, file)
		}
		return err
	})
	return err
}

func ZipFolderWithOptionalTopLevel(srcFolder, destZip, topLevelDir string) error {
	zipFile, err := os.Create(destZip)
	if err != nil {
		return err
	}
	defer zipFile.Close()
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()
	err = filepath.Walk(srcFolder, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// Create the header with or without the top-level directory
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}
		relativePath, err := filepath.Rel(srcFolder, path)
		if err != nil {
			return err
		}
		if topLevelDir != "" {
			header.Name = filepath.Join(topLevelDir, relativePath)
		} else {
			header.Name = relativePath
		}
		if info.IsDir() {
			header.Name += "/"
		} else {
			header.Method = zip.Deflate
		}
		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			return err
		}
		if !info.IsDir() {
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()
			_, err = io.Copy(writer, file)
			if err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func Unzip(src, dest string) error {
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()
	for _, f := range r.File {
		rc, err := f.Open()
		if err != nil {
			return err
		}
		//nolint:gosec
		path := filepath.Join(dest, f.Name)
		if f.FileInfo().IsDir() {
			_ = os.MkdirAll(path, f.Mode())
		} else {
			dir := filepath.Dir(path)
			if err = os.MkdirAll(dir, f.Mode()); err != nil {
				rc.Close()
				return err
			}

			file, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				rc.Close()
				return err
			}
			//nolint:gosec
			_, err = io.Copy(file, rc)
			if closeErr := file.Close(); err == nil {
				err = closeErr
			}

			if err != nil {
				rc.Close()
				return err
			}
		}

		if err := rc.Close(); err != nil {
			return err
		}
	}
	return nil
}

func UnzipSkipOuterDir(src, dest string, skipOuterDir bool) error {
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	// 找到最外层目录名（如果需要）
	var outerDirName string
	if skipOuterDir {
		for _, f := range r.File {
			parts := strings.Split(f.Name, "/")
			if len(parts) > 0 {
				outerDirName = parts[0]
				break
			}
		}
	}

	for _, f := range r.File {
		rc, err := f.Open()
		if err != nil {
			return err
		}

		// 处理路径，如果需要跳过最外层目录
		var path string
		if skipOuterDir {
			relativePath := strings.TrimPrefix(f.Name, outerDirName+"/")
			path = filepath.Join(dest, relativePath)
		} else {
			// nolint:gosec
			path = filepath.Join(dest, f.Name)
		}

		if f.FileInfo().IsDir() {
			err = os.MkdirAll(path, f.Mode())
			if err != nil {
				rc.Close()
				return err
			}
		} else {
			dir := filepath.Dir(path)
			if err = os.MkdirAll(dir, f.Mode()); err != nil {
				rc.Close()
				return err
			}

			file, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				rc.Close()
				return err
			}
			//nolint:gosec
			_, err = io.Copy(file, rc)
			if closeErr := file.Close(); err == nil {
				err = closeErr
			}

			if err != nil {
				rc.Close()
				return err
			}
		}

		if err := rc.Close(); err != nil {
			return err
		}
	}
	return nil
}

func IsImageFile(fileName string) bool {
	ext := filepath.Ext(fileName)
	return ext == ".png" || ext == ".jpg" || ext == ".jpeg" || ext == ".gif" ||
		ext == ".bmp" || ext == ".webp" || ext == ".svg" || ext == ".ico" ||
		ext == ".tiff" || ext == ".icns" || ext == ".heic" || ext == ".heif" ||
		ext == ".avif" || ext == ".apng" || ext == ".flif" || ext == ".jxl" ||
		ext == ".bpg"
}

// FileSHA1FromPath 计算给定文件路径的SHA1哈希
func FileSHA1FromPath(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()
	return FileSHA1(file)
}

func FileSHA1(file *os.File) (string, error) {
	//nolint:gosec
	hasher := sha1.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}
	hashValue := hasher.Sum(nil)
	return fmt.Sprintf("%x", hashValue), nil
}

func FileSHA256(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()
	//nolint:gosec
	hasher := sha256.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}
	hashValue := hasher.Sum(nil)
	return fmt.Sprintf("%x", hashValue), nil
}

func FormatFileSize(size int64) string {

	const (
		_        = iota
		KB int64 = 1 << (10 * iota)
		MB
		GB
		TB
	)

	var unit string
	var value float64

	switch {
	case size < KB:
		unit = "B"
		value = float64(size)
	case size < MB:
		unit = "KB"
		value = float64(size / KB)
	case size < GB:
		unit = "MB"
		value = float64(size / MB)
	case size < TB:
		unit = "GB"
		value = float64(size / GB)
	default:
		unit = "TB"
		value = float64(size / TB)
	}

	// format it to 2 decimal places
	return fmt.Sprintf("%.2f%s", value, unit)
}

func TarGzFolder(srcFolder string, destTarGz string) error {
	gzFile, err := os.Create(destTarGz)
	if err != nil {
		return err
	}
	defer gzFile.Close()
	gzWriter := gzip.NewWriter(gzFile)
	defer gzWriter.Close()
	tarWriter := tar.NewWriter(gzWriter)
	defer tarWriter.Close()
	err = filepath.Walk(srcFolder, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			return err
		}
		header.Name, err = filepath.Rel(srcFolder, path)
		if err != nil {
			return err
		}
		err = tarWriter.WriteHeader(header)
		if err != nil {
			return err
		}
		if !info.IsDir() {
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()
			_, err = io.Copy(tarWriter, file)
			if err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func UnTarGz(src, dest string) error {
	gzFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer gzFile.Close()
	gzReader, err := gzip.NewReader(gzFile)
	if err != nil {
		return err
	}
	defer gzReader.Close()
	tarReader := tar.NewReader(gzReader)
	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		//nolint:gosec
		path := filepath.Join(dest, header.Name)
		info := header.FileInfo()
		if info.IsDir() {
			if err = os.MkdirAll(path, info.Mode()); err != nil {
				return err
			}
		} else {
			if err := extractFile(path, info.Mode(), tarReader); err != nil {
				return err
			}
		}
	}
	return nil
}

func extractFile(path string, mode os.FileMode, tarReader *tar.Reader) error {
	file, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, mode)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = io.Copy(file, tarReader)
	return err
}

// WriteFile write the content to the desired file
func WriteFile(dstFile string, content []byte, perm os.FileMode) error {
	if err := os.MkdirAll(path.Dir(dstFile), os.ModePerm); err != nil {
		return err
	}
	f, err := os.OpenFile(dstFile, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, perm&os.ModePerm)
	if err != nil {
		return err
	}
	defer f.Close()

	_, err = f.Write(content)
	return err
}

func RemoveFileExtension(fileName string) string {
	extension := filepath.Ext(fileName)
	baseName := filepath.Base(fileName)
	return strings.TrimSuffix(baseName, extension)
}

func WindowsBinSuffix(bin string) string {
	return bin + ".exe"
}

func EmptyDir(dir string) bool {
	f, err := os.Open(dir)
	if err != nil {
		return true
	}
	defer f.Close()
	_, err = f.Readdir(1)
	return err == io.EOF
}

// deleteWithWindowsEnhancement handles Windows-specific file deletion issues
func deleteWithWindowsEnhancement(path string, originalErr error) error {
	// First, try to wait a bit and retry normal deletion
	time.Sleep(100 * time.Millisecond)
	if err := os.RemoveAll(path); err == nil {
		return nil
	}

	// Try to use Windows rmdir command with force flag
	if err := forceDeleteWithCmd(path); err == nil {
		return nil
	}

	// Try to kill processes that might be locking files in the directory
	if err := killProcessesLockingPath(path); err == nil {
		// Wait a bit for processes to terminate
		time.Sleep(500 * time.Millisecond)
		if err := os.RemoveAll(path); err == nil {
			return nil
		}
	}

	// Try one more time with cmd after killing processes
	if err := forceDeleteWithCmd(path); err == nil {
		return nil
	}

	// If all methods fail, return the original error with additional context
	return fmt.Errorf("failed to delete folder (Windows enhanced): %v", originalErr)
}

// forceDeleteWithCmd uses Windows cmd to force delete directory
func forceDeleteWithCmd(path string) error {
	// Use rmdir with /s (remove subdirectories) and /q (quiet mode) flags
	cmd := exec.Command("cmd", "/c", "rmdir", "/s", "/q", path)
	if err := cmd.Run(); err != nil {
		// Try with rd command as alternative
		cmd = exec.Command("cmd", "/c", "rd", "/s", "/q", path)
		return cmd.Run()
	}
	return nil
}

// killProcessesLockingPath attempts to find and kill processes that might be locking files
func killProcessesLockingPath(path string) error {
	// Use handle.exe from Sysinternals if available, or tasklist/taskkill
	// First try to find processes using the path with handle.exe
	if err := killWithHandle(path); err == nil {
		return nil
	}

	// Fallback: try to kill common processes that might lock files
	commonProcesses := []string{"esbuild.exe", "node.exe", "npm.exe", "pnpm.exe", "yarn.exe"}
	for _, process := range commonProcesses {
		killProcess(process)
	}

	return nil
}

// killWithHandle uses handle.exe to find and kill processes locking the path
func killWithHandle(path string) error {
	// Try to use handle.exe from Sysinternals
	cmd := exec.Command("handle.exe", "-p", path)
	output, err := cmd.Output()
	if err != nil {
		return err
	}

	// Parse output to find PIDs and kill them
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "pid:") {
			// Extract PID and kill process
			// This is a simplified implementation
			// In practice, you'd need more robust PID extraction
		}
	}
	return nil
}

// killProcess kills a process by name
func killProcess(processName string) {
	cmd := exec.Command("taskkill", "/F", "/IM", processName)
	cmd.Run() // Ignore errors as process might not exist
}
