#!/bin/bash
SHELL := /bin/bash
BASEDIR = $(shell pwd)

export GO111MODULE=on
export GOPROXY=https://goproxy.cn,direct
export GOPRIVATE=*.gitlab.com,git.makeblock.com
export GOSUMDB=off

# params pass from cmd
APP_BRANCH = "main"
APP_ENV = "dev"
APP_KUBE_CONFIG = "mb-sz-test"

# params stable
APP_NAME=pipeline
APP_VERSION=`grep '^pipeline=' VERSION | cut -d'=' -f2 | tr -d '[[:space:]]'`
RUNNER_VERSION=`grep '^runner=' VERSION | cut -d'=' -f2 | tr -d '[[:space:]]'`
HELPER_VERSION=`grep '^helper=' VERSION | cut -d'=' -f2 | tr -d '[[:space:]]'`
COMMIT_ID=`git rev-parse HEAD`

# docker image
IMAGE_CR="registry.cn-hangzhou.aliyuncs.com"
IMAGE_CR_NAMESPACE="makeblock"
PIPELINE_IMAGE_PREFIX="${IMAGE_CR}/${IMAGE_CR_NAMESPACE}/${APP_NAME}:v${APP_VERSION}"
# open image
OPEN_IMAGE_CR="mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com"
OPEN_IMAGE_CR_NAMESPACE="open"

all: fmt imports mod lint test
install-pre-commit:
	brew install pre-commit
install-git-hooks:
	pre-commit install --hook-type commit-msg
	pre-commit install
run-pre-commit:
	pre-commit run --all-files
fmt:
	gofmt -w .
imports:
ifeq (, $(shell which goimports))
	go install golang.org/x/tools/cmd/goimports@latest
endif
	goimports -w .
mod:
	go mod tidy
lint: mod
ifeq (, $(shell which golangci-lint))
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@v1.61.0
endif
	golangci-lint run -c .golangci.yml
.PHONY: test
test: mod
	go test -gcflags=-l -coverpkg=./... -coverprofile=coverage.data ./...
	echo "exit code: $$?"
.PHONY: build
build:
	IMAGE_NAME="${PIPELINE_IMAGE_PREFIX}-${APP_BRANCH}"; \
	sh build/pipeline/build.sh ${COMMIT_ID} $$IMAGE_NAME
build-main:
	make build APP_BRANCH=main
build-release:
	make build APP_BRANCH=release
build-prod:
	make build APP_BRANCH=prod
cleanup:
	sh scripts/cleanup.sh
kustomize:
	NEW_IMAGE="${APP_NAME}=${PIPELINE_IMAGE_PREFIX}-${APP_BRANCH}"; \
	sh deploy/kubernetes/kustomize.sh $$NEW_IMAGE ${COMMIT_ID} ${APP_ENV}
.PHONY: deploy
deploy:
	sh deploy/kubernetes/deploy.sh ${APP_KUBE_CONFIG} ${APP_ENV}
deploy-direct:
	make kustomize
	make deploy
deploy-dev:
	make deploy-direct APP_BRANCH=main APP_ENV=dev APP_KUBE_CONFIG=mb-sz-test
deploy-test:
	make deploy-direct APP_BRANCH=release APP_ENV=test APP_KUBE_CONFIG=mb-sz-test
deploy-pre:
	make deploy-direct APP_BRANCH=release APP_ENV=pre APP_KUBE_CONFIG=mb-sz-prod
kustomize-prod:
	make kustomize APP_BRANCH=release APP_ENV=prod
deploy-prod:
	make deploy APP_ENV=prod APP_KUBE_CONFIG=mb-sz-prod
kustomize-we-prod:
	make kustomize APP_BRANCH=release APP_ENV=we-prod
deploy-we-prod:
	make deploy APP_ENV=we-prod APP_KUBE_CONFIG=mb-we-prod
kustomize-us-prod:
	make kustomize APP_BRANCH=release APP_ENV=us-prod
deploy-us-prod:
	make deploy APP_ENV=us-prod APP_KUBE_CONFIG=mb-us-sv-prod

# 构建 runner 可执行文件
TARGET_OUT ?= build/runner/target
SCRIPTS_DIR ?= build/runner
NOW_TIME := $(shell TZ=Asia/Shanghai date +'%Y-%m-%d %H:%M:%S')
RELEASE_LDFLAGS ?= "-extldflags -static -s -w -X pipeline/pkg/version.Version=${RUNNER_VERSION} -X pipeline/pkg/version.GitCommit=${COMMIT_ID} -X 'pipeline/pkg/version.BuildTime=${NOW_TIME}'"

# 定义平台列表
RUNNER_PLATFORMS := linux-amd64 linux-armv7 linux-arm64 darwin-amd64 darwin-arm64 windows-amd64 windows-arm64
# 生成各平台目标文件路径
RUNNER_TARGETS := $(foreach p,$(RUNNER_PLATFORMS),$(if $(findstring windows,$(p)),${TARGET_OUT}/runner-$(p)/runner.exe,${TARGET_OUT}/runner-$(p)/runner))

.PHONY: runner runner-build runner-checksum runner-tar runner-clean

runner: runner-build runner-checksum runner-tar runner-release runner-clean runner-version-add

# 构建所有平台的运行文件
runner-build: $(RUNNER_TARGETS)

# 为不同平台定义构建规则
${TARGET_OUT}/runner-linux-amd64/runner:
	GOOS=linux GOARCH=amd64 LDFLAGS=$(RELEASE_LDFLAGS) ${SCRIPTS_DIR}/gobuild.sh $@ ./cmd/runner/runner.go
${TARGET_OUT}/runner-linux-armv7/runner:
	GOOS=linux GOARCH=arm GOARM=7 LDFLAGS=$(RELEASE_LDFLAGS) ${SCRIPTS_DIR}/gobuild.sh $@ ./cmd/runner/runner.go
${TARGET_OUT}/runner-linux-arm64/runner:
	GOOS=linux GOARCH=arm64 LDFLAGS=$(RELEASE_LDFLAGS) ${SCRIPTS_DIR}/gobuild.sh $@ ./cmd/runner/runner.go
${TARGET_OUT}/runner-darwin-amd64/runner:
	GOOS=darwin GOARCH=amd64 LDFLAGS=$(RELEASE_LDFLAGS) ${SCRIPTS_DIR}/gobuild.sh $@ ./cmd/runner/runner.go
${TARGET_OUT}/runner-darwin-arm64/runner:
	GOOS=darwin GOARCH=arm64 LDFLAGS=$(RELEASE_LDFLAGS) ${SCRIPTS_DIR}/gobuild.sh $@ ./cmd/runner/runner.go
${TARGET_OUT}/runner-windows-amd64/runner.exe:
	GOOS=windows GOARCH=amd64 LDFLAGS=$(RELEASE_LDFLAGS) ${SCRIPTS_DIR}/gobuild.sh $@ ./cmd/runner/runner.go
${TARGET_OUT}/runner-windows-arm64/runner.exe:
	GOOS=windows GOARCH=arm64 LDFLAGS=$(RELEASE_LDFLAGS) ${SCRIPTS_DIR}/gobuild.sh $@ ./cmd/runner/runner.go

# 生成校验和
runner-checksum:
	@for target in $(RUNNER_TARGETS); do \
		if [ -f "$${target}" ]; then \
			dir=$$(dirname "$${target}"); \
			base=$$(basename "$${target}" .exe); \
			openssl sha256 "$${target}" | awk '{print $$2}' > "$${dir}/$${base}.sha256"; \
		fi; \
	done

# 给每个平台的文件和sha打包
runner-tar:
	@for platform in $(RUNNER_PLATFORMS); do \
		if [ -d "${TARGET_OUT}/runner-$${platform}" ]; then \
			cd ${TARGET_OUT} && tar -czvf "runner-$${platform}.tar.gz" "runner-$${platform}" && cd -; \
		fi; \
	done

# 发布 runner
RUNNER_RELEASE_TOKEN ?= ""
runner-release:
	sh ${SCRIPTS_DIR}/github.sh ${RUNNER_RELEASE_TOKEN} ${RUNNER_VERSION}

runner-clean:
	rm -rf ${TARGET_OUT}

# 录入runner版本到系统中
runner-version-add:
	@echo "runner version: ${RUNNER_VERSION}"
	@echo "runner targets: ${RUNNER_PLATFORMS}"
	@for platform in $(RUNNER_PLATFORMS); do \
		os=$$(echo $$platform | cut -d'-' -f1); \
		arch=$$(echo $$platform | cut -d'-' -f2); \
		sh ${SCRIPTS_DIR}/version.sh ${RUNNER_VERSION} $$os $$arch; \
	done

.PHONY: runner-step-images runner-image

TARGET_IMAGE ?= ALL
TARGET_IMAGE_TAG ?= latest
runner-step-images:
	sh runner/container/images/build.sh ${TARGET_IMAGE} ${TARGET_IMAGE_TAG}

# 构建 runner 镜像
RUNNER_IMAGE_PREFIX="${IMAGE_CR}/${IMAGE_CR_NAMESPACE}/runner"
runner-image:
	make runner-build
	sh build/runner/image.sh ${RUNNER_IMAGE_PREFIX} ${RUNNER_VERSION} ${RUNNER_HELPER_TARGET_ARCHITECTURE}

# 构建 runner-helper 镜像
RUNNER_HELPER_TARGET_ARCHITECTURE ?= linux/amd64,linux/arm64
RUNNER_HELPER_IMAGE_PREFIX="${OPEN_IMAGE_CR}/${OPEN_IMAGE_CR_NAMESPACE}/runner-helper"
HELPER_RELEASE_LDFLAGS ?= "-extldflags -static -s -w -X pipeline/pkg/version.Version=${HELPER_VERSION} -X pipeline/pkg/version.GitCommit=${COMMIT_ID} -X 'pipeline/pkg/version.BuildTime=${NOW_TIME}'"

runner-helper:
	sh build/helper/build.sh ${RUNNER_HELPER_TARGET_ARCHITECTURE} ${HELPER_RELEASE_LDFLAGS}

runner-helper-image:
	make runner-helper
	sh build/helper/image.sh ${RUNNER_HELPER_IMAGE_PREFIX} latest ${RUNNER_HELPER_TARGET_ARCHITECTURE}

.PHONY: docs
docs: mod
ifeq (, $(shell which swag))
	go install github.com/swaggo/swag/cmd/swag@latest
endif
	swag init -g cmd/server/server.go

help:
	@echo "fmt - format the source code"
	@echo "imports - goimports"
	@echo "mod - go mod tidy"
	@echo "lint - run golangci-lint"
	@echo "test - unit test"
	@echo "build - build docker image"
	@echo "build-main - build docker image for main branch"
	@echo "build-release - build docker image for release branch"
	@echo "cleanup - clean up the build binary"
	@echo "kustomize - kustomize deployment yaml"
	@echo "deploy - deploy to kubernetes"
	@echo "deploy-direct - kustomize & deploy"
	@echo "deploy-dev - deploy to shenzhen develop environment"
	@echo "deploy-test - deploy to shenzhen test environment"
	@echo "deploy-pre - deploy to shenzhen preview environment"
	@echo "deploy-prod - deploy to shenzhen production environment"
	@echo "kustomize-prod - kustomize the yaml of shenzhen production environment"
	@echo "redis - launch a docker redis"
