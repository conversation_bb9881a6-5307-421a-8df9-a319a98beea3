package kubernetes

import (
	"fmt"
	"path/filepath"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/pipeline"
	"strings"
)

type CacheUploaderScriptBuilder struct {
	args   []string
	ctx    *pipeline.StepContext
	logger *logger.Logger
	path   string
}

func NewCacheUploaderScriptBuilder(ctx *pipeline.StepContext, logger *logger.Logger, path string) *CacheUploaderScriptBuilder {
	return &CacheUploaderScriptBuilder{
		ctx:    ctx,
		logger: logger,
		path:   path,
	}
}

func (s *CacheUploaderScriptBuilder) cacheSrcPath() string {
	// absolute path
	if strings.HasPrefix(s.path, "/") {
		return s.path
	}
	// relative path
	path := strings.TrimPrefix(s.path, "./")

	return fmt.Sprintf("%s/%s/%s", common.ContainerWorkspace, s.ctx.App.Identity, path)
}

func (s *CacheUploaderScriptBuilder) cacheDistPath() string {
	return fmt.Sprintf("%s/%s.tar.gz", s.ctx.Pipeline.UUID,
		strings.TrimSuffix(filepath.Base(s.path), "/"))
}

//helper artifacts-uploader --endpoint=127.0.0.1:9000
//--access-key=YIZmLTaucpkX7raXIwMr
//--secret-key=oTxO2Aa75KhErRVm3N9LRZ4gK9rENA5gpESODvYc
//--bucket ${BUCKEt_NAME}
//--src /Users/<USER>/Downloads/pipeline/cmd/helper
//--dist 9571354c610c4c5da9ab41168fe6118b/helper.tar.gz

func (s *CacheUploaderScriptBuilder) Command() (string, error) {

	// get access key and secret key
	accessKey := s.ctx.OSS.AccessKey
	secretKey := s.ctx.OSS.SecretKey

	// build command
	s.args = append(s.args, "helper", "caches-uploader")
	s.args = append(s.args, "--endpoint", s.ctx.OSS.Endpoint)
	s.args = append(s.args, "--access-key", accessKey)
	s.args = append(s.args, "--secret-key", secretKey)
	s.args = append(s.args, "--bucket", s.ctx.OSS.CachesBucketName)

	// artifact info
	s.args = append(s.args, "--src", s.cacheSrcPath())
	s.args = append(s.args, "--dist", s.cacheDistPath())

	// mask access key and secret key
	if accessKey != "" {
		s.logger.AddMask(s.ctx.OSS.AccessKey)
	}
	if secretKey != "" {
		s.logger.AddMask(s.ctx.OSS.SecretKey)
	}

	// join args
	return strings.Join(s.args, " "), nil
}
