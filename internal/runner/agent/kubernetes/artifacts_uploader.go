package kubernetes

import (
	"path/filepath"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/runner/artifact"
	"strings"
)

type ArtifactsUploaderScriptBuilder struct {
	args         []string
	ctx          *pipeline.StepContext
	artifact     *agent.Artifact
	logger       *logger.Logger
	artifactPath string
}

func NewArtifactsUploaderScriptBuilder(ctx *pipeline.StepContext,
	artifact *agent.Artifact, logger *logger.Logger) *ArtifactsUploaderScriptBuilder {
	return &ArtifactsUploaderScriptBuilder{
		ctx:      ctx,
		logger:   logger,
		artifact: artifact,
	}
}

func (s *ArtifactsUploaderScriptBuilder) ArtifactBucketName() string {
	// 制品仓库
	if s.artifact.Type == agent.PACKAGES {
		return s.ctx.OSS.ArtifactsBucketName
	}
	return s.ctx.OSS.LoggerBucketName
}

func (s *ArtifactsUploaderScriptBuilder) getArtifactName() string {
	if s.artifact.UnZip {
		return s.artifact.Name + "/" + filepath.Base(s.artifact.Path)
	}
	return s.artifact.Name
}

func (s *ArtifactsUploaderScriptBuilder) ArtifactDistPath() string {
	if s.artifactPath == "" {
		s.artifactPath = artifact.BuildArtifactPath(s.artifact, s.ctx, s.getArtifactName())
	}
	return s.artifactPath
}

//helper artifacts-uploader --endpoint=127.0.0.1:9000
//--access-key=YIZmLTaucpkX7raXIwMr
//--secret-key=oTxO2Aa75KhErRVm3N9LRZ4gK9rENA5gpESODvYc
//--bucket=${BUCKEt_NAME}
//--file-path=/Users/<USER>/GolandProjects/makeblock/pipeline/build
//--dist-path=52e83f32180a471d9c76ee7030d7ff13/artifacts/build.zip`,

func (s *ArtifactsUploaderScriptBuilder) Command() (string, error) {
	// get access key and secret key
	accessKey := s.ctx.OSS.AccessKey
	secretKey := s.ctx.OSS.SecretKey
	// build command
	s.args = append(s.args, "helper", "artifacts-uploader")
	s.args = append(s.args, "--endpoint", s.ctx.OSS.Endpoint)
	s.args = append(s.args, "--access-key", accessKey)
	s.args = append(s.args, "--secret-key", secretKey)
	s.args = append(s.args, "--bucket", s.ArtifactBucketName())
	// artifact info
	s.args = append(s.args, "--name", s.artifact.Name)
	s.args = append(s.args, "--src", s.artifact.Path)
	s.args = append(s.args, "--dist", s.ArtifactDistPath())
	// disable zip
	if s.artifact.UnZip {
		s.args = append(s.args, "--unzip")
	}
	// mask access key and secret key
	if accessKey != "" {
		s.logger.AddMask(s.ctx.OSS.AccessKey)
	}
	if secretKey != "" {
		s.logger.AddMask(s.ctx.OSS.SecretKey)
	}
	// join args
	return strings.Join(s.args, " "), nil
}
