package kubernetes

import (
	"context"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/runner/agent"
)

func (a *AgentKubernetes) DownloadCache(ctx context.Context, opt agent.CachesOption) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	stepContext := common.GetStepContextFromCtx(ctx)
	for _, path := range opt.Paths {
		command, err := NewCacheDownloaderScriptBuilder(stepContext, logFromCtx, path).Command()
		if err != nil {
			return err
		}
		if err = a.Exec(ctx, agent.ExecOption{
			ContainerName: helperContainerName,
			Command:       command,
			DisablePretty: !stepContext.Debug, // only pretty log in debug mode
		}); err != nil {
			return err
		}
	}
	return nil
}

func (a *AgentKubernetes) UploadCache(ctx context.Context, opt agent.CachesOption) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	stepContext := common.GetStepContextFromCtx(ctx)
	for _, path := range opt.Paths {
		command, err := NewCacheUploaderScriptBuilder(stepContext, logFromCtx, path).Command()
		if err != nil {
			return err
		}
		if err = a.Exec(ctx, agent.ExecOption{
			ContainerName: helperContainerName,
			Command:       command,
			DisablePretty: !stepContext.Debug, // only pretty log in debug mode
		}); err != nil {
			return err
		}
	}
	return nil
}
