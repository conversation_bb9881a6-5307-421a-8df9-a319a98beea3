package kubernetes

import (
	"bytes"
	"fmt"
	"pipeline/pkg/runner"
	"strings"
)

type BashShell struct {
	Shell string
}

type BashWriter struct {
	bytes.Buffer
	indent         int
	UsePosixEscape bool
}

func (b *BashWriter) escape(input string) string {
	if b.UsePosixEscape {
		return runner.PosixShellEscape(input)
	}
	return runner.ShellEscape(input)
}

func (b *BashWriter) Line(text string) {
	b.WriteString(strings.Repeat("  ", b.indent) + text + "\n")
}

func (b *BashShell) GenerateSaveScript(w *BashWriter, dir, scriptPath, content string, mode int64) (string, error) {
	w.Line(fmt.Sprintf("mkdir -p %s", dir)) // ensure dir exists
	w.Line(fmt.Sprintf("touch %s", scriptPath))
	w.Line(fmt.Sprintf("chmod %o %s", mode, scriptPath))
	w.Line(fmt.Sprintf("echo %s > %s", w.escape(content), scriptPath))
	return w.String(), nil
}
