package kubernetes

import (
	"context"
	"io"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/remotecommand"
	"net/url"
)

func Execute(ctx context.Context, method string, url *url.URL,
	config *rest.Config, stdin io.Reader, stdout, stderr io.Writer, tty bool) error {
	// use the default transport
	exec, err := remotecommand.NewSPDYExecutor(config, method, url)
	if err != nil {
		return err
	}
	// execute command
	return exec.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdin:  stdin,
		Stdout: stdout,
		Stderr: stderr,
		Tty:    tty,
	})
}
