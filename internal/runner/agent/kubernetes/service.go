package kubernetes

import (
	"fmt"
	"k8s.io/api/core/v1"
	"pipeline/config"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
)

func init() {
	agent.RegisterService(agent.DockerService, agent.Kubernetes, DinDService{})
}

var _ agent.ServiceAgent = DinDService{}

type DinDService struct{}

func (d DinDService) Name() string {
	return agent.DockerService.String()
}

func (d DinDService) Apply(a agent.Agent) error {

	// a must be k8s agent, cast it to k8s agent, if not, return error
	ak, ok := a.(*AgentKubernetes)
	if !ok {
		return fmt.Errorf("agent is not k8s agent, can not apply dind service")
	}

	// default use tcp://dind.pipeline:2375(multiple dind service in one pod)
	if _, useDInd := ak.prepareOpts.Env["dind"]; !useDInd {
		ak.prepareOpts.Env["DOCKER_TLS_CERTDIR"] = ""
		ak.prepareOpts.Env["DOCKER_HOST"] = fmt.Sprintf("tcp://dind.%s:2375",
			config.GetRunnerConfig().Kubernetes.Namespace)
		return nil
	}

	// dinD service
	dInd := v1.Container{
		Image: config.GetRunnerConfig().Kubernetes.DinDImage,
		Name:  agent.DockerService.String(),
		Ports: []v1.ContainerPort{{ContainerPort: 2375}},
		SecurityContext: &v1.SecurityContext{
			Privileged: util.BoolPtr(true),
		},
		Env: []v1.EnvVar{
			{Name: "DOCKER_TLS_CERTDIR", Value: ""},
			{Name: "DOCKER_HOST", Value: "tcp://localhost:2375"},
		},
		ReadinessProbe: &v1.Probe{
			ProbeHandler: v1.ProbeHandler{
				Exec: &v1.ExecAction{
					Command: []string{"docker", "info"},
				},
			},
			InitialDelaySeconds: 5,
			PeriodSeconds:       2,
			FailureThreshold:    20,
		},
	}
	ak.podConfigPrepareOpts.services = append(ak.podConfigPrepareOpts.services, dInd)
	return nil
}
