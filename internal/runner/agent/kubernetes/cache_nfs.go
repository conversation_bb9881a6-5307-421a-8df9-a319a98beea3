package kubernetes

import (
	"context"
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"path/filepath"
	"pipeline/config"
	"pipeline/internal/runner/common"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"github.com/avast/retry-go"

	"git.makeblock.com/makeblock-go/go-nfs-client/nfs4"
	v1 "k8s.io/api/core/v1"
)

func init() {
	Register("nfs", NewNfsCacheAdapter)
}

type NfsCacheAdapter struct {
	config  *config.CacheConfig
	pathMap map[string]string // key: nfs full path, value: mount path
}

func NewNfsCacheAdapter(c *config.CacheConfig) (CacheAdapter, error) {
	return &NfsCacheAdapter{
		config: c,
	}, nil
}

func (n *NfsCacheAdapter) resolvePath(ctx *pipeline.StepContext, caches []string) error {
	n.pathMap = make(map[string]string)
	nfsRootPath := n.config.NFSCache.Path
	for _, cache := range caches {
		// 处理路径并规范化
		var nfsPath string
		var mountPath string
		// 绝对路径直接追加
		if filepath.IsAbs(cache) {
			mountPath = cache
		} else { // 如果是相对路径，需要拼接工作目录，转成绝对路径
			mountPath = filepath.Clean(fmt.Sprintf("%s/%s",
				common.GetStepContainerWorkspace(ctx.App.Identity), cache))
		}
		// 如果是一些公用的目录可以所有的任务共享，需要拼接到nfs根目录下
		if v, ok := agent.CommonCacheMap[cache]; ok {
			nfsPath = filepath.Clean(fmt.Sprintf("%s/%s", nfsRootPath, v))
		} else { // 如果是私有的目录，需要拼接到流水线唯一ID目录下
			nfsPath = filepath.Clean(fmt.Sprintf("%s/%s/%s/%s",
				nfsRootPath, ctx.App.Identity, ctx.Pipeline.UUID, cache))
		}
		n.pathMap[nfsPath] = mountPath
	}
	return nil
}

func (n *NfsCacheAdapter) ensureNfsDirsExist() error {

	// init nfs client
	nfsServer := fmt.Sprintf("%s:%d", n.config.NFSCache.Server, n.config.NFSCache.Port)
	// create nfs client
	client, err := nfs4.NewNfsClient(context.Background(), nfsServer, nfs4.AuthParams{})
	if err != nil {
		return fmt.Errorf("failed to create NFS client: %w", err)
	}
	defer client.Close()

	// create all nfs sub dir
	for nfsPath := range n.pathMap {
		// must n.config.NFSCache.Path prefix with
		if !strings.HasPrefix(nfsPath, n.config.NFSCache.Path) {
			log.Error("nfs path must be prefix", log.Any("path", nfsPath),
				log.Any("prefix", n.config.NFSCache.Path))
			continue
		}
		// create nfs sub dir
		if err = retry.Do(func() error {
			return client.MakePath(nfsPath)
		}, retry.Attempts(5)); err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}
	}
	return nil
}

func (n *NfsCacheAdapter) Apply(ctx *pipeline.StepContext, caches []string, pod *v1.Pod) error {
	// empty caches
	if len(caches) == 0 {
		return nil
	}
	// resolve path, nfs path, mount path
	if err := n.resolvePath(ctx, caches); err != nil {
		return err
	}
	// create nfs sub dir, if not exist, the pod will be pending
	// we mount nfs to thd runner pod, so we need to create the nfs sub dir
	if err := n.ensureNfsDirsExist(); err != nil {
		return err
	}
	// find the build container
	var bc *v1.Container
	for i, container := range pod.Spec.Containers {
		if container.Name == buildContainerName {
			bc = &pod.Spec.Containers[i]
			break
		}
	}
	if bc == nil {
		return fmt.Errorf("build container not found")
	}
	// create volume and volume mount
	for nfsPath, mountPath := range n.pathMap {
		// because k8s volume name must be valid DNS-1123 subdomain, so we need to replace invalid characters
		name := util.RemoveChars(mountPath, common.CharsToRemove)
		// volume
		pod.Spec.Volumes = append(pod.Spec.Volumes, v1.Volume{
			Name: name,
			VolumeSource: v1.VolumeSource{
				NFS: &v1.NFSVolumeSource{
					Server: n.config.NFSCache.Server,
					Path:   nfsPath,
				},
			}})
		// volume mount
		bc.VolumeMounts = append(bc.VolumeMounts, v1.VolumeMount{
			Name:      name,
			MountPath: mountPath,
		})
	}
	return nil
}
