package kubernetes

import (
	"fmt"
	"k8s.io/api/core/v1"
	"pipeline/config"
	"pipeline/pkg/pipeline"
	"sync"
)

type CacheAdapter interface {
	Apply(*pipeline.StepContext, []string, *v1.Pod) error
}

type Factory func(config *config.CacheConfig) (CacheAdapter, error)

var factories = &FactoriesMap{
	internal: make(map[string]Factory),
}

type FactoriesMap struct {
	internal map[string]Factory
	lock     sync.Mutex
}

func Register(name string, factory Factory) {
	factories.lock.Lock()
	defer factories.lock.Unlock()
	factories.internal[name] = factory
}

func CreateCacheAdapter(config config.CacheConfig) (CacheAdapter, error) {
	factories.lock.Lock()
	defer factories.lock.Unlock()
	factory, ok := factories.internal[config.Type]
	if !ok {
		return nil, fmt.Errorf("cache adapter %s not found", config.Type)
	}
	return factory(&config)
}
