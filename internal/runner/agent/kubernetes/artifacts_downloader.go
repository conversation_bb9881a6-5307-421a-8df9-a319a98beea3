package kubernetes

import (
	"pipeline/internal/runner/logger"
	"pipeline/pkg/common"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/runner/artifact"
	"strings"
)

type ArtifactsDownloaderScriptBuilder struct {
	args   []string
	dist   string
	art    *agent.Artifact
	ctx    *pipeline.StepContext
	logger *logger.Logger
}

func NewArtifactsDownloaderScriptBuilder(ctx *pipeline.StepContext, art *agent.Artifact,
	dist string, logger *logger.Logger) *ArtifactsDownloaderScriptBuilder {
	return &ArtifactsDownloaderScriptBuilder{
		ctx:    ctx,
		art:    art,
		dist:   dist,
		logger: logger,
	}
}

func (s *ArtifactsDownloaderScriptBuilder) ArtifactBucketName() string {
	// 制品仓库
	if s.art.Type == agent.PACKAGES {
		return s.ctx.OSS.ArtifactsBucketName
	}
	return s.ctx.OSS.LoggerBucketName
}

func (s *ArtifactsDownloaderScriptBuilder) buildArtifactPath() string {
	// from artifact repo
	if s.art.Type == agent.PACKAGES {
		// 是否需要渲染latest
		if v, ok := s.ctx.ArtifactLatestVersion[s.art.Name]; ok {
			s.art.Version = strings.Replace(s.art.Version, "latest", v, -1)
		}
	}
	// build artifact path
	return artifact.BuildArtifactPath(s.art, s.ctx, s.art.Name+common.ZipCompressionExtension)
}

func (s *ArtifactsDownloaderScriptBuilder) buildDistPath() string {
	if s.dist == "" {
		s.dist = "./"
	}
	return s.dist
}

func (s *ArtifactsDownloaderScriptBuilder) Command() (string, error) {

	// get access key and secret key
	accessKey := s.ctx.OSS.AccessKey
	secretKey := s.ctx.OSS.SecretKey

	// build command
	s.args = append(s.args, "helper", "artifacts-downloader")
	s.args = append(s.args, "--endpoint", s.ctx.OSS.Endpoint)
	s.args = append(s.args, "--access-key", accessKey)
	s.args = append(s.args, "--secret-key", secretKey)
	s.args = append(s.args, "--bucket", s.ArtifactBucketName())

	// artifact info
	s.args = append(s.args, "--name", s.art.Name)
	s.args = append(s.args, "--object", s.buildArtifactPath())
	s.args = append(s.args, "--dist", s.buildDistPath())

	// mask access key and secret key
	if accessKey != "" {
		s.logger.AddMask(s.ctx.OSS.AccessKey)
	}
	if secretKey != "" {
		s.logger.AddMask(s.ctx.OSS.SecretKey)
	}

	// join args
	return strings.Join(s.args, " "), nil
}
