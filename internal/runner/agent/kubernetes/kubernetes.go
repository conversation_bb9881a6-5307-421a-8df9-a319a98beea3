package kubernetes

import (
	"context"
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"github.com/avast/retry-go"
	"io"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"math/rand"
	"net/http"
	"os"
	"pipeline/config"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/git"
	"pipeline/internal/runner/logger"
	pkgCommon "pipeline/pkg/common"
	"pipeline/pkg/models"
	"pipeline/pkg/oss"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/runner/artifact"
	"pipeline/pkg/util"
	"pipeline/pkg/version"
	"strings"
)

// nolint
const (
	Namespace                    = "pipeline"
	k8sResourcesNameSuffixLength = 8
	k8sResourcesNameMaxLength    = 63

	buildContainerName  = "build"
	helperContainerName = "runner-helper"

	RepoVolumeName = "repo"
	EnvsVolumeName = "envs"

	BuildRootPath = "/root"
)

var (
	_                 agent.Agent = (*AgentKubernetes)(nil)
	PropagationPolicy             = metav1.DeletePropagationBackground
	chars                         = []rune("abcdefghijklmnopqrstuvwxyz0123456789")
)

type AgentKubernetes struct {
	// opts
	prepareOpts          agent.PrepareOption
	podConfigPrepareOpts PodConfigPrepareOpts
	stepContext          *pipeline.StepContext
	// config
	config config.Kubernetes
	// client
	kubeClient *kubernetes.Clientset
	kubeConfig *rest.Config
	pod        *corev1.Pod
}

func NewAgentKubernetes() *AgentKubernetes {
	return &AgentKubernetes{}
}

func (a *AgentKubernetes) Name() string {
	return agent.Kubernetes.String()
}

type PodConfigPrepareOpts struct {
	labels           map[string]string
	annotations      map[string]string
	services         []corev1.Container
	initContainers   []corev1.Container
	Containers       []corev1.Container
	imagePullSecrets []corev1.LocalObjectReference
	hostAliases      []corev1.HostAlias
}

func generateNameForK8sResources(pattern string) string {
	suffix := make([]rune, k8sResourcesNameSuffixLength)
	for i := range suffix {
		// nolint
		suffix[i] = chars[rand.Intn(len(chars))]
	}
	if len(pattern) > (k8sResourcesNameMaxLength - k8sResourcesNameSuffixLength - 1) {
		pattern = pattern[:k8sResourcesNameMaxLength-k8sResourcesNameSuffixLength-1]
	}
	return fmt.Sprintf("%s-%s", pattern, string(suffix))
}

type containerBuildOpts struct {
	name            string
	image           string
	requests        corev1.ResourceList
	limits          corev1.ResourceList
	securityContext *corev1.SecurityContext
	workingDir      string
	command         []string
	envs            map[string]string
	ports           []int
	args            []string
	caches          []string
}

func buildContainer(opts containerBuildOpts) (corev1.Container, error) {
	imagePullPolicy := corev1.PullIfNotPresent
	// dev mode, always pull image
	if version.Version == config.Dev {
		imagePullPolicy = corev1.PullAlways
	}
	if opts.image == "" {
		return corev1.Container{}, fmt.Errorf("image is required")
	}
	// container ports
	containerPorts := make([]corev1.ContainerPort, len(opts.ports))
	for i, port := range opts.ports {
		containerPorts[i] = corev1.ContainerPort{ContainerPort: int32(port)} //nolint:gosec
	}
	// build container
	return corev1.Container{
		Name:            opts.name,
		Image:           opts.image,
		Args:            opts.args,
		Command:         opts.command,
		ImagePullPolicy: imagePullPolicy,
		Resources: corev1.ResourceRequirements{
			Limits:   opts.limits,
			Requests: opts.requests,
		},
		VolumeMounts:    getVolumeMounts(),
		SecurityContext: opts.securityContext,
		WorkingDir:      opts.workingDir,
		Stdin:           true,
		Env:             buildEnvs(opts.envs),
		Ports:           containerPorts,
	}, nil
}

func buildEnvs(envs map[string]string) []corev1.EnvVar {
	var envVars []corev1.EnvVar
	for key, val := range envs {
		envVars = append(envVars, corev1.EnvVar{
			Name:  key,
			Value: val,
		})
	}
	return envVars
}

func getVolumeMounts() []corev1.VolumeMount {
	var mounts []corev1.VolumeMount
	mounts = append(mounts, corev1.VolumeMount{
		Name:      RepoVolumeName,
		MountPath: BuildRootPath,
	})
	mounts = append(mounts, corev1.VolumeMount{
		Name:      EnvsVolumeName,
		MountPath: common.ContainerWorkspace + "/.envs",
	})
	return mounts
}

func mergeServiceEnv(env map[string]string, services []corev1.Container) map[string]string {
	for _, service := range services {
		for _, e := range service.Env {
			env[e.Name] = e.Value
		}
	}
	return env
}

func createBuildAndHelperContainers(prepareOpts agent.PrepareOption, opts PodConfigPrepareOpts) (corev1.Container, corev1.Container, error) {
	// build container
	builderContainer, err := buildContainer(containerBuildOpts{
		name:       buildContainerName,
		image:      prepareOpts.Image,
		command:    runner.GetBuildAndHelperContainersCommand(),
		workingDir: prepareOpts.WorkingDir,
		envs:       mergeServiceEnv(prepareOpts.Env, opts.services),
		requests:   parseResourceConfig(config.GetRunnerConfig().Kubernetes.BuildRequests),
		limits:     parseResourceConfig(config.GetRunnerConfig().Kubernetes.BuildLimits),
		//securityContext: s.Config.Kubernetes.GetContainerSecurityContext(
		//	s.Config.Kubernetes.BuildContainerSecurityContext,
		//	s.defaultCapDrop()...,
		//),
		caches: prepareOpts.Caches,
	})
	if err != nil {
		return corev1.Container{}, corev1.Container{}, fmt.Errorf("building build container: %w", err)
	}
	// helper container
	helperContainer, err := buildContainer(containerBuildOpts{
		name:       helperContainerName,
		image:      config.GetRunnerConfig().Kubernetes.HelperImage,
		command:    runner.GetBuildAndHelperContainersCommand(),
		workingDir: prepareOpts.WorkingDir,
		requests:   parseResourceConfig(config.GetRunnerConfig().Kubernetes.HelperRequests),
		limits:     parseResourceConfig(config.GetRunnerConfig().Kubernetes.HelperLimits),
		//securityContext: s.Config.Kubernetes.GetContainerSecurityContext(
		//	s.Config.Kubernetes.HelperContainerSecurityContext,
		//	s.defaultCapDrop()...,
		//),
	})
	if err != nil {
		return corev1.Container{}, corev1.Container{}, fmt.Errorf("building helper container: %w", err)
	}
	return builderContainer, helperContainer, nil
}

func getVolumes() []corev1.Volume {
	var volumes []corev1.Volume
	// workspace volume
	volumes = append(volumes, corev1.Volume{
		Name: RepoVolumeName,
		VolumeSource: corev1.VolumeSource{
			EmptyDir: &corev1.EmptyDirVolumeSource{},
		},
	})
	// envs volume with default envs.txt file
	volumes = append(volumes, corev1.Volume{
		Name: EnvsVolumeName,
		VolumeSource: corev1.VolumeSource{
			EmptyDir: &corev1.EmptyDirVolumeSource{},
		},
	})
	return volumes
}

func (a *AgentKubernetes) prepareAgentConfig(ctx context.Context) error {

	a.stepContext = common.GetStepContextFromCtx(ctx)

	if a.stepContext == nil {
		return fmt.Errorf("step context not found")
	}

	// default working dir
	if a.prepareOpts.WorkingDir == "" {
		a.prepareOpts.WorkingDir = fmt.Sprintf("%s/%s",
			common.ContainerWorkspace, a.stepContext.App.Identity)
	}

	// service
	for _, service := range a.prepareOpts.Services {
		if svc := agent.GetService(service, agent.Kubernetes); svc != nil {
			if err := svc.Apply(a); err != nil {
				return err
			}
		}
	}

	return nil
}

func (a *AgentKubernetes) prepareBuildCache(pod *corev1.Pod) {
	// create cache adapter
	adapter, err := CreateCacheAdapter(config.GetRunnerConfig().Kubernetes.Cache)
	if err != nil {
		log.ErrorE("create adapter error", err)
		return
	}
	// apply cache
	if err = adapter.Apply(a.stepContext, a.prepareOpts.Caches, pod); err != nil {
		log.ErrorE("apply cache error", err)
	}
}

func buildPodLabels(labels map[string]string) map[string]string {
	podLabels := make(map[string]string)
	// runner config labels
	cls := config.GetRunnerConfig().Kubernetes.PodLabels
	for k, v := range cls {
		podLabels[k] = v
	}
	for k, v := range labels {
		podLabels[k] = v
	}
	// default labels
	podLabels["job.source.io"] = "pipeline"
	return podLabels
}

func (a *AgentKubernetes) buildNodeSelector() map[string]string {
	return config.GetRunnerConfig().Kubernetes.NodeSelector
}

func (a *AgentKubernetes) buildNodeToleration() []corev1.Toleration {
	var tolerationList []corev1.Toleration
	nodeToleration := config.GetRunnerConfig().Kubernetes.NodeToleration
	// key=value: effect
	// "node-role.kubernetes.io/master": "NoSchedule",
	// "custom.toleration=value":        "NoSchedule",
	// "empty.value=":                   "PreferNoSchedule",
	for toleration, effect := range nodeToleration {
		newToleration := corev1.Toleration{
			Effect: corev1.TaintEffect(effect),
		}
		if strings.Contains(toleration, "=") {
			parts := strings.Split(toleration, "=")
			newToleration.Key = parts[0]
			if len(parts) > 1 {
				newToleration.Value = parts[1]
			}
			newToleration.Operator = corev1.TolerationOpEqual
		} else {
			newToleration.Key = toleration
			newToleration.Operator = corev1.TolerationOpExists
		}
		tolerationList = append(tolerationList, newToleration)
	}
	return tolerationList
}

func (a *AgentKubernetes) preparePodConfig() (corev1.Pod, error) {
	// build and helper containers
	builderContainer, helperContainer, err := createBuildAndHelperContainers(a.prepareOpts, a.podConfigPrepareOpts)
	if err != nil {
		return corev1.Pod{}, err
	}
	// build pod
	pod := corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Annotations: a.podConfigPrepareOpts.annotations,
			Labels:      buildPodLabels(a.podConfigPrepareOpts.labels),
			Namespace:   config.GetRunnerConfig().Kubernetes.Namespace,
			Name:        generateNameForK8sResources(a.stepContext.App.Identity),
		},
		Spec: corev1.PodSpec{
			Containers: append([]corev1.Container{
				builderContainer,
				helperContainer,
			}, a.podConfigPrepareOpts.services...),
			RestartPolicy:    corev1.RestartPolicyNever,
			HostAliases:      a.podConfigPrepareOpts.hostAliases,
			InitContainers:   a.podConfigPrepareOpts.initContainers,
			ImagePullSecrets: a.podConfigPrepareOpts.imagePullSecrets,
			Volumes:          getVolumes(),
			Affinity:         a.config.GetAffinity(),
			NodeSelector:     a.buildNodeSelector(),
			Tolerations:      a.buildNodeToleration(),
			//DNSPolicy:                     s.getDNSPolicy(),
			//ActiveDeadlineSeconds:         s.getPodActiveDeadlineSeconds(),
			//ServiceAccountName:            s.configurationOverwrites.serviceAccount,
			//SchedulerName:                 s.Config.Kubernetes.SchedulerName,
			//DNSConfig:                     s.Config.Kubernetes.GetDNSConfig(),
			//RuntimeClassName:              s.Config.Kubernetes.RuntimeClassName,
			//PriorityClassName:             s.Config.Kubernetes.PriorityClassName,
			//SecurityContext:               s.Config.Kubernetes.GetPodSecurityContext(),
			//AutomountServiceAccountToken:  s.Config.Kubernetes.AutomountServiceAccountToken,
			//TerminationGracePeriodSeconds: s.Config.Kubernetes.PodTerminationGracePeriodSeconds,
		},
	}
	// build cache
	a.prepareBuildCache(&pod)
	// return
	return pod, nil
}

func (a *AgentKubernetes) loadKubeClient() error {
	a.config = config.GetRunnerConfig().Kubernetes
	configFilePath, _ := util.RemoveHomeDir(a.config.KubeConfig)
	cfg, err := clientcmd.BuildConfigFromFlags("", configFilePath)
	if err != nil {
		return err
	}
	a.kubeConfig = cfg
	// create kube client
	if a.kubeClient, err = kubernetes.NewForConfig(cfg); err != nil {
		return err
	}
	return nil
}

func (a *AgentKubernetes) createPod(ctx context.Context, pod corev1.Pod) (err error) {
	// from context
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	a.pod, err = a.kubeClient.CoreV1().Pods(Namespace).Create(context.Background(), &pod, metav1.CreateOptions{})
	if err != nil {
		logFromCtx.Errorf("Failed to create pod: %v", err)
		return err
	}
	// log pod info
	for _, c := range pod.Spec.Containers {
		logFromCtx.Infof("container %s: %s", c.Name, c.Image)
	}
	// wait for deployment to be ready
	logFromCtx.Infof("created pod %s", a.pod.GetObjectMeta().GetName())
	// wait for pod to be ready
	if _, err = waitForPodRunning(ctx, a.kubeClient, a.pod, logFromCtx,
		a.config.WaitForPodRunningInterval,
		a.config.WaitForPodRunningAttempts); err != nil {
		return err
	}
	return nil
}

func (a *AgentKubernetes) initContainerEnvironment(ctx context.Context) error {
	// copy file
	for _, file := range a.prepareOpts.PrepareFiles {
		err := a.Copy(ctx, file)
		if err != nil {
			return err
		}
	}
	return nil
}

func (a *AgentKubernetes) Prepare(ctx context.Context, opt agent.PrepareOption) error {
	// prepare pod config opts
	a.prepareOpts = opt
	// set agent opt by input
	if err := a.prepareAgentConfig(ctx); err != nil {
		return err
	}
	// load kube config
	if err := a.loadKubeClient(); err != nil {
		return err
	}
	// prepare pod config
	pod, err := a.preparePodConfig()
	if err != nil {
		return err
	}
	// create pod
	if err = a.createPod(ctx, pod); err != nil {
		return err
	}
	// init container environment
	return a.initContainerEnvironment(ctx)
}

func (a *AgentKubernetes) Checkout(ctx context.Context, opt agent.CheckoutOption) error {
	// ctx
	stepContext := common.GetStepContextFromCtx(ctx)
	// clone to path
	opt.DistDir = common.ContainerWorkspace + "/" + util.Coalesce(opt.DistDir, stepContext.App.Identity)
	// builder
	cmd, err := git.NewCommandManager(stepContext, opt, logger.GetLoggerFromCtx(ctx)).BuildCommand()
	if err != nil {
		return err
	}
	// execute command in helper container
	return a.Exec(ctx, agent.ExecOption{
		Command:       cmd,
		ContainerName: helperContainerName,
	})
}

func (a *AgentKubernetes) Copy(ctx context.Context, opt agent.CopyOption) error {
	bs := BashShell{}
	bw := BashWriter{
		UsePosixEscape: true,
	}
	if opt.DestPath == "" {
		opt.DestPath = a.prepareOpts.WorkingDir
	}

	if !strings.HasSuffix(opt.DestPath, "/") {
		opt.DestPath += "/"
	}
	for _, file := range opt.Files {
		saveScript, err := bs.GenerateSaveScript(&bw, opt.DestPath, opt.DestPath+file.Name, file.Body, file.Mode)
		if err != nil {
			return err
		}
		// Ending with a newline is important to actually run the script
		stdin := strings.NewReader(saveScript)
		// kubeAPI: pods, attach, FF_USE_LEGACY_KUBERNETES_EXECUTION_STRATEGY=false
		req := a.kubeClient.CoreV1().RESTClient().Post().
			Resource("pods").
			Name(a.pod.Name).
			Namespace(a.pod.Namespace).
			SubResource("exec").
			VersionedParams(&corev1.PodExecOptions{
				Container: util.SetDefaultIfEmpty(opt.ContainerName, buildContainerName),
				Command:   []string{"/bin/sh"},
				Stdin:     true,
				Stdout:    true,
				Stderr:    true,
				TTY:       false,
			}, scheme.ParameterCodec)
		// execute command
		logFromCtx := logger.GetLoggerFromCtx(ctx)
		if err = Execute(ctx, http.MethodPost, req.URL(), a.kubeConfig, stdin, logFromCtx, logFromCtx, false); err != nil {
			return fmt.Errorf("couldn't execute command: %w", err)
		}
	}
	return nil
}

// Exec executes a command in the container
func (a *AgentKubernetes) Exec(ctx context.Context, opt agent.ExecOption) error {
	// set default container name
	opt.ContainerName = util.Coalesce(opt.ContainerName, buildContainerName)
	opt.ScriptPath = util.Coalesce(opt.ScriptPath, agent.DefaultContainerPath)
	opt.ScriptName = util.Coalesce(opt.ScriptName, agent.DefaultScriptName)
	// get pod
	pod, err := a.kubeClient.CoreV1().Pods(a.pod.Namespace).Get(ctx, a.pod.Name, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("couldn't get pod details: %w", err)
	}
	// check if pod is running
	if pod.Status.Phase != corev1.PodRunning {
		return fmt.Errorf("pod %q (on namespace %q) is not running and cannot execute commands; current phase is %q",
			a.pod.Name, a.pod.Namespace, pod.Status.Phase,
		)
	}
	// format command
	opt.ExportEnv = true
	stepContext := common.GetStepContextFromCtx(ctx)
	name, script, _, cmd, _, err := runner.SetupShellCommand(stepContext, opt)
	if err != nil {
		return err
	}
	// copy script to container
	if err = a.Copy(ctx, agent.CopyOption{
		ContainerName: opt.ContainerName,
		DestPath:      opt.ScriptPath,
		Files: []*agent.FileEntry{{
			Name: name,
			Body: script,
			Mode: 0o755,
		}}}); err != nil {
		return fmt.Errorf("couldn't copy script to container: %w", err)
	}
	// Ending with a newline is important to actually run the script
	//stdin := strings.NewReader(opt.Command)
	// kubeAPI: pods, attach, FF_USE_LEGACY_KUBERNETES_EXECUTION_STRATEGY=false
	req := a.kubeClient.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(pod.Name).
		Namespace(pod.Namespace).
		SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Container: opt.ContainerName,
			Command:   cmd,
			Stdin:     true,
			Stdout:    true,
			Stderr:    true,
			TTY:       false,
		}, scheme.ParameterCodec)
	// execute command
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	if err = Execute(ctx, http.MethodPost, req.URL(), a.kubeConfig, os.Stdin, logFromCtx, logFromCtx, false); err != nil {
		return fmt.Errorf("couldn't execute command: %w", err)
	}
	return nil
}

// Upload 上传制品到OSS
func (a *AgentKubernetes) Upload(ctx context.Context, opt agent.UploadOption) (*agent.UploadActionResponse, error) {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	stepContext := common.GetStepContextFromCtx(ctx)
	// 获取artifact的工作目录
	artifactsResponse := make([]*models.ArtifactEntry, 0)
	ossConfig := stepContext.OSS
	repo := oss.NewClient(ossConfig.Endpoint, ossConfig.AccessKey, ossConfig.SecretKey)
	// 遍历所有的 artifact
	for _, at := range opt.Artifacts {
		// prevent loop variable capture
		art := at
		// render artifact.Path and artifact.Name by env
		art.Name = util.RenderContentWithShellDelimiters(art.Name, stepContext.Env)
		art.Path = util.RenderContentWithShellDelimiters(art.Path, stepContext.Env)
		// create builder
		builder := NewArtifactsUploaderScriptBuilder(stepContext, &art, logFromCtx)
		uploadScript, err := builder.Command()
		if err != nil {
			return nil, err
		}
		if err = a.Exec(ctx, agent.ExecOption{
			ContainerName: helperContainerName,
			Command:       uploadScript,
			DisablePretty: !stepContext.Debug, // only pretty log in debug mode
			//DisablePretty: false, // only pretty log in debug mode
		}); err != nil {
			return nil, err
		}
		// 如果是上传到制品库,则需要将版本信息传递给server
		if art.Type == agent.PACKAGES {
			//反查artifact的版本信息
			info, err := repo.Info(ctx, builder.ArtifactBucketName(),
				builder.ArtifactDistPath()+pkgCommon.ZipCompressionExtension)
			if err != nil {
				return nil, err
			}
			av, err := artifact.CreateArtifactVersion(stepContext, art, info.Sha1,
				builder.ArtifactDistPath()+pkgCommon.ZipCompressionExtension, info.Size)
			if err != nil {
				logFromCtx.Errorf("create artifact version failed: %v", err)
				return nil, err
			}
			artifactsResponse = append(artifactsResponse, av)
		}
	}
	logFromCtx.Info("all artifacts uploaded task completed")
	ret := &agent.UploadActionResponse{}
	if len(artifactsResponse) > 0 {
		ret.Content = map[string]any{
			agent.ArtifactsKey: artifactsResponse,
		}
		logFromCtx.Infof("upload artifact response: %#v", ret.Content)
	}
	return ret, nil
}

// Download 下载制品到本地
func (a *AgentKubernetes) Download(ctx context.Context, opt agent.DownloadOption) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	// no need to download
	if len(opt.Artifacts) == 0 {
		logFromCtx.Info("no artifacts to download")
		return nil
	}
	stepContext := common.GetStepContextFromCtx(ctx)
	// builder
	for _, at := range opt.Artifacts {
		// prevent loop variable capture
		art := at
		downloadCmd, err := NewArtifactsDownloaderScriptBuilder(stepContext,
			&art, opt.DestPath, logFromCtx).Command()
		if err != nil {
			return err
		}
		// execute
		if err = a.Exec(ctx, agent.ExecOption{
			ContainerName: helperContainerName,
			Command:       downloadCmd,
		}); err != nil {
			return err
		}
	}
	return nil
}

// GetArchive
// reference : https://github.com/kubernetes/kubectl/blob/master/pkg/cmd/cp/cp.go
func (a *AgentKubernetes) GetArchive(ctx context.Context, opt agent.ArchiveOption) (io.ReadCloser, error) {
	// Create tar command
	tarCommand := []string{
		"sh", "-c", fmt.Sprintf("tar -cf - %s", opt.Path),
	}
	log.Info(fmt.Sprintf("tar command: %v", tarCommand))
	// kubeAPI: pods, attach, FF_USE_LEGACY_KUBERNETES_EXECUTION_STRATEGY=false
	req := a.kubeClient.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(a.pod.Name).
		Namespace(a.pod.Namespace).
		SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Container: util.SetDefaultIfEmpty(opt.ContainerName, buildContainerName),
			Command:   tarCommand,
			Stdin:     false,
			Stdout:    true,
			Stderr:    true,
			TTY:       false,
		}, scheme.ParameterCodec)
	// Create a pipe to read the output
	pipReader, pipWriter := io.Pipe()
	// Execute the command
	go func() {
		defer pipWriter.Close()
		err := Execute(ctx, http.MethodPost, req.URL(), a.kubeConfig, nil, pipWriter, os.Stderr, false)
		if err != nil {
			pipWriter.CloseWithError(fmt.Errorf("couldn't execute command: %w", err))
		}
	}()
	return pipReader, nil
}

func (a *AgentKubernetes) Clean(ctx context.Context) error {
	// ctx
	stepContext := common.GetStepContextFromCtx(ctx)
	// clean up, when not in debug mode and pod is not nil
	if a.pod != nil && !stepContext.Debug {
		err := retry.Do(func() error {
			// kubeAPI: pods, delete
			return a.kubeClient.CoreV1().
				Pods(a.pod.Namespace).
				Delete(ctx, a.pod.Name, metav1.DeleteOptions{
					GracePeriodSeconds: int64Ptr(5),
					PropagationPolicy:  &PropagationPolicy,
				})
		})
		if err != nil {
			return fmt.Errorf("couldn't delete pod: %w", err)
		}
		logger.GetLoggerFromCtx(ctx).Infof("deleted pod %s", a.pod.Name)
	}
	return nil
}
