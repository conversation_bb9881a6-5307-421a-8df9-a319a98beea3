package host

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"pipeline/pkg/runner/agent"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/util"
)

// LocalCache 本地文件系统缓存实现
type LocalCache struct{}

// NewLocalCache 创建本地缓存实例
func NewLocalCache() agent.Cache {
	return &LocalCache{}
}

// Load 从本地缓存目录加载缓存到工作目录
func (lc *LocalCache) Load(ctx context.Context, caches []string) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	stepCtx := common.GetStepContextFromCtx(ctx)
	stepWorkspace := common.GetStepWorkspace(stepCtx)                  // 工作目录
	pipelineCacheBasePath := common.GetPipelineCacheBasePath(*stepCtx) // 流水线缓存路径

	// 处理缓存路径
	for _, path := range caches {
		// 工作目录中的缓存路径
		localCachePath := filepath.Join(pipelineCacheBasePath, path)
		// 检查本地缓存是否存在
		if _, err := os.Stat(localCachePath); os.IsNotExist(err) {
			logFromCtx.Infof("Cache not found in host cache: %v", localCachePath)
			continue
		}
		// 检查是目录还是文件
		hostCacheInfo, err := os.Stat(localCachePath)
		if err != nil {
			logFromCtx.Errorf("Failed to stat host cache: %v", err)
			continue
		}
		// 缓存路径
		cacheAbsPath := filepath.Join(stepWorkspace, path)
		// 处理
		if hostCacheInfo.IsDir() {
			// 复制目录但不覆盖已存在的文件
			if err = copyDirNoOverwrite(localCachePath, cacheAbsPath, logFromCtx); err != nil {
				logFromCtx.Errorf("Failed to copy cache directory %s: %v", localCachePath, err)
			} else {
				logFromCtx.Infof("Successfully loaded cache directory: %s", path)
			}
		} else {
			// 复制文件但不覆盖
			if err = copyFileNoOverwrite(localCachePath, cacheAbsPath, logFromCtx); err != nil {
				logFromCtx.Errorf("Failed to copy cache file %s: %v", localCachePath, err)
			} else {
				logFromCtx.Infof("Successfully loaded cache file: %s", path)
			}
		}
	}
	return nil
}

// Update 从工作目录更新缓存到本地缓存目录
func (lc *LocalCache) Update(ctx context.Context, caches []string) error {
	stepCtx := common.GetStepContextFromCtx(ctx)
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	stepWorkspace := common.GetStepWorkspace(stepCtx)                  // 工作目录
	pipelineCacheBasePath := common.GetPipelineCacheBasePath(*stepCtx) // 流水线缓存路径

	// 处理所有缓存路径
	for _, path := range caches {
		localCachePath := filepath.Join(pipelineCacheBasePath, path) // 本地缓存路径
		cacheAbsPath := filepath.Join(stepWorkspace, path)           // 缓存绝对路径
		logFromCtx.Infof("Updating cache: %s", path)
		// 检查工作目录中的缓存是否存在
		workspaceCacheInfo, err := os.Stat(cacheAbsPath)
		if err != nil {
			if os.IsNotExist(err) {
				logFromCtx.Infof("Cache not found in workspace, skipping: %s\n", cacheAbsPath)
			} else {
				logFromCtx.Errorf("Failed to stat workspace cache: %v", err)
			}
			continue
		}
		// 确保本地缓存基础目录存在
		if err = os.MkdirAll(filepath.Dir(localCachePath), 0755); err != nil {
			logFromCtx.Errorf("Failed to create host cache directory: %v", err)
			continue
		}
		if workspaceCacheInfo.IsDir() {
			// 增量复制目录
			if err = copyDirIncremental(cacheAbsPath, localCachePath); err != nil {
				logFromCtx.Errorf("Failed to incrementally update cache directory %s: %v", cacheAbsPath, err)
			}
		} else {
			// 检查文件是否需要更新
			if isFileNewer(cacheAbsPath, localCachePath) {
				logFromCtx.Debugf("Updating cache file: %s", cacheAbsPath)
				if err = util.CopyFile(cacheAbsPath, localCachePath); err != nil {
					logFromCtx.Errorf("Failed to update cache file %s: %v", cacheAbsPath, err)
				} else {
					logFromCtx.Infof("Successfully updated cache file: %s", path)
				}
			} else {
				logFromCtx.Debugf("Cache file is up to date: %s", path)
			}
		}
	}
	return nil
}

// copyFileNoOverwrite 复制文件但不覆盖已存在的文件
func copyFileNoOverwrite(src, dst string, logger *logger.Logger) error {
	// 检查目标文件是否已存在
	if _, err := os.Stat(dst); err == nil {
		logger.Debugf("Target file already exists, skipping: %s", dst)
		return nil
	}

	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return fmt.Errorf("failed to create directory for %s: %w", dst, err)
	}

	return util.CopyFile(src, dst)
}

// copyDirNoOverwrite 复制目录但不覆盖已存在的文件
func copyDirNoOverwrite(src, dst string, logger *logger.Logger) error {
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}

		dstPath := filepath.Join(dst, relPath)

		if info.IsDir() {
			// 创建目录
			return os.MkdirAll(dstPath, info.Mode())
		}

		// 复制文件（不覆盖）
		return copyFileNoOverwrite(path, dstPath, logger)
	})
}

// isFileNewer 检查源文件是否比目标文件更新
func isFileNewer(src, dst string) bool {
	srcInfo, err := os.Stat(src)
	if err != nil {
		return false
	}

	dstInfo, err := os.Stat(dst)
	if err != nil {
		// 目标文件不存在，认为源文件更新
		return true
	}

	return srcInfo.ModTime().After(dstInfo.ModTime())
}

// copySymlinkSafe 安全地复制符号链接，如果目标已存在则先删除
func copySymlinkSafe(src, dst string) error {
	// 读取源符号链接的目标
	target, err := os.Readlink(src)
	if err != nil {
		return fmt.Errorf("failed to read symlink %s: %w", src, err)
	}

	// 检查目标是否已存在
	if _, err = os.Lstat(dst); err == nil {
		// 目标存在，检查是否为符号链接
		var dstInfo os.FileInfo
		if dstInfo, err = os.Lstat(dst); err == nil && dstInfo.Mode()&os.ModeSymlink != 0 {
			// 检查符号链接目标是否相同
			var existingTarget string
			if existingTarget, err = os.Readlink(dst); err == nil && existingTarget == target {
				// 符号链接目标相同，无需更新
				return nil
			}
		}
		// 删除已存在的文件/符号链接
		if err = os.Remove(dst); err != nil {
			return fmt.Errorf("failed to remove existing file %s: %w", dst, err)
		}
	}

	// 确保目标目录存在
	if err = os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return fmt.Errorf("failed to create directory for %s: %w", dst, err)
	}

	// 创建新的符号链接
	if err = os.Symlink(target, dst); err != nil {
		return fmt.Errorf("failed to create symlink %s -> %s: %w", dst, target, err)
	}

	return nil
}

// copyDirIncremental 增量复制目录，只复制更新或新增的文件
func copyDirIncremental(src, dst string) error {
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}

		dstPath := filepath.Join(dst, relPath)

		if info.IsDir() {
			// 创建目录
			return os.MkdirAll(dstPath, info.Mode())
		}

		// 特别处理符号链接
		if info.Mode()&os.ModeSymlink != 0 {
			return copySymlinkSafe(path, dstPath)
		}

		// 检查是否需要复制普通文件
		if isFileNewer(path, dstPath) {
			return util.CopyFile(path, dstPath)
		}

		return nil
	})
}
