package host

import (
	"archive/tar"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	"os/exec"
	"path/filepath"
	pkgCommon "pipeline/pkg/common"
	"runtime"
	"strings"
	"time"

	"pipeline/pkg/runner/agent"
	"pipeline/pkg/runner/artifact"

	"pipeline/config"
	"pipeline/internal/runner/agent/host/lookpath"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/git"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/runner"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"github.com/creack/pty"
	"github.com/go-git/go-billy/v5/helper/polyfill"
	"github.com/go-git/go-billy/v5/osfs"
	"github.com/go-git/go-git/v5/plumbing/format/gitignore"
	"golang.org/x/term"
)

var _ agent.Agent = (*AgentHost)(nil)

type AgentHost struct {
	StdOut     io.Writer
	Caches     []string // 缓存的路径或者是文件
	WorkingDir string
	Env        map[string]string
	cache      agent.Cache // 缓存管理器
}

func NewAgentHost() *AgentHost {
	return &AgentHost{
		cache: NewLocalCache(),
	}
}

func (a *AgentHost) Name() string {
	return agent.HostAgent.String()
}

func (a *AgentHost) Prepare(ctx context.Context, opt agent.PrepareOption) error {

	logFromCtx := logger.GetLoggerFromCtx(ctx)
	a.StdOut = logFromCtx
	stepCtx := common.GetStepContextFromCtx(ctx)

	// 创建工作目录
	workDir := common.GetStepWorkspace(stepCtx)
	if err := os.MkdirAll(workDir, os.ModePerm); err != nil {
		logFromCtx.Errorf("failed to create working directory: %v", err)
		return err
	}
	a.WorkingDir = common.GetStepWorkspace(stepCtx)
	a.Caches = opt.Caches
	a.Env = opt.Env

	// copy file
	for _, file := range opt.PrepareFiles {
		err := a.Copy(ctx, file)
		if err != nil {
			return err
		}
	}

	return nil
}

func (a *AgentHost) Checkout(ctx context.Context, opt agent.CheckoutOption) error {
	// Checkout
	hash, err := git.Checkout(ctx, opt)
	if err != nil {
		return err
	}
	// Checkout hash
	if hash == nil {
		return errors.New("checkout hash is nil")
	}
	// load cache
	a.loadCaches(ctx)
	// return err
	return err
}

func (a *AgentHost) loadCaches(ctx context.Context) {
	if err := a.cache.Load(ctx, a.Caches); err != nil {
		logger.GetLoggerFromCtx(ctx).Errorf("Failed to load caches: %v", err)
	}
}

func getEnvListFromMap(env map[string]string) []string {
	envList := make([]string, 0)
	for k, v := range env {
		envList = append(envList, fmt.Sprintf("%s=%s", k, v))
	}
	return envList
}

type localEnv struct {
	env map[string]string
}

func (l *localEnv) Getenv(name string) string {
	if runtime.GOOS == pkgCommon.Windows {
		for k, v := range l.env {
			if strings.EqualFold(name, k) {
				return v
			}
		}
		return ""
	}
	return l.env[name]
}

func lookupPathHost(cmd string, env map[string]string, writer io.Writer) (string, error) {
	f, err := lookpath.LookPath2(cmd, &localEnv{env: env})
	if err != nil {
		err := "Cannot find: " + fmt.Sprint(cmd) + " in PATH"
		if _, _err := writer.Write([]byte(err + "\n")); _err != nil {
			return "", fmt.Errorf("%v: %w", err, _err)
		}
		return "", errors.New(err)
	}
	return f, nil
}

func setupPty(cmd *exec.Cmd, cmdline string) (*os.File, *os.File, error) {
	ppty, tty, err := openPty()
	if err != nil {
		return nil, nil, err
	}
	if term.IsTerminal(int(tty.Fd())) {
		_, err := term.MakeRaw(int(tty.Fd()))
		if err != nil {
			ppty.Close()
			tty.Close()
			return nil, nil, err
		}
	}
	cmd.Stdin = tty
	cmd.Stdout = tty
	cmd.Stderr = tty
	cmd.SysProcAttr = getSysProcAttr(cmdline, true)
	return ppty, tty, nil
}

type ptyWriter struct {
	Out       io.Writer
	AutoStop  bool
	dirtyLine bool
}

func (w *ptyWriter) Write(buf []byte) (int, error) {
	if w.AutoStop && len(buf) > 0 && buf[len(buf)-1] == 4 {
		n, err := w.Out.Write(buf[:len(buf)-1])
		if err != nil {
			return n, err
		}
		if w.dirtyLine || len(buf) > 1 && buf[len(buf)-2] != '\n' {
			_, _ = w.Out.Write([]byte("\n"))
			return n, io.EOF
		}
		return n, io.EOF
	}
	w.dirtyLine = strings.LastIndex(string(buf), "\n") < len(buf)-1
	return w.Out.Write(buf)
}

func writeKeepAlive(ppty io.Writer) {
	c := 1
	var err error
	for c == 1 && err == nil {
		c, err = ppty.Write([]byte{4})
		<-time.After(time.Second)
	}
}

func copyPtyOutput(writer io.Writer, ppty io.Reader, finishLog context.CancelFunc) {
	defer func() {
		finishLog()
	}()
	if _, err := io.Copy(writer, ppty); err != nil {
		return
	}
}

func (a *AgentHost) Copy(_ context.Context, opt agent.CopyOption) error {
	destPath := opt.DestPath
	files := opt.Files
	for _, f := range files {
		if err := os.MkdirAll(filepath.Dir(filepath.Join(destPath, f.Name)), 0o777); err != nil {
			return err
		}
		//nolint:gosec
		if err := os.WriteFile(filepath.Join(destPath, f.Name), []byte(f.Body), fs.FileMode(f.Mode)); err != nil {
			return err
		}
	}
	return nil
}

func (a *AgentHost) setupShellCommandExecutor(ctx context.Context, opt agent.ExecOption) ([]string, string, error) {
	// host: step workspace/host executor
	stepCtx := common.GetStepContextFromCtx(ctx)
	opt.ScriptPath = common.HostExecutorPath(stepCtx)
	opt.ScriptName = agent.DefaultScriptName
	// setup shell command
	scriptName, script, _, cmd, cmdline, err := runner.SetupShellCommand(stepCtx, opt)
	if err != nil {
		return nil, "", err
	}
	//复制
	err = a.Copy(ctx, agent.CopyOption{
		DestPath: opt.ScriptPath,
		Files: []*agent.FileEntry{
			{
				Name: scriptName,
				Mode: 0o755,
				Body: script,
			},
		},
	})
	return cmd, cmdline, err
}

func (a *AgentHost) GetPathVariableName() string {
	if runtime.GOOS == "plan9" {
		return "path"
	} else if runtime.GOOS == pkgCommon.Windows {
		return "Path"
	}
	return "PATH"
}

func (a *AgentHost) DefaultPathVariable() string {
	v, _ := os.LookupEnv(a.GetPathVariableName())
	return v
}

func (a *AgentHost) JoinPathVariable(paths ...string) string {
	return strings.Join(paths, string(filepath.ListSeparator))
}

func (a *AgentHost) Exec(ctx context.Context, opt agent.ExecOption) error {

	logFromCtx := logger.GetLoggerFromCtx(ctx)
	stepCtx := common.GetStepContextFromCtx(ctx)

	command, cmdline, err := a.setupShellCommandExecutor(ctx, opt)
	if err != nil {
		logFromCtx.Errorf("failed to setup exec: %v", err)
		return err
	}

	// 设置环境变量
	opt.Env = util.MergeMap(a.Env, opt.Env)
	for _, env := range os.Environ() {
		if k, v, ok := strings.Cut(env, "="); ok {
			// don't override
			if _, ok := opt.Env[k]; !ok {
				opt.Env[k] = v
			}
		}
	}
	//设置PATH
	defaultPath := a.DefaultPathVariable()
	path := a.JoinPathVariable(defaultPath, opt.Env["PATH"])
	// use config path
	if opt.PATH != nil {
		path = a.JoinPathVariable(append(opt.PATH, path)...)
	}
	opt.Env[a.GetPathVariableName()] = path

	envList := getEnvListFromMap(opt.Env)

	var wd string
	var workdir = common.GetStepWorkspace(stepCtx)
	if workdir != "" {
		if filepath.IsAbs(workdir) {
			wd = workdir
		} else {
			wd = filepath.Join(config.GetRunnerConfig().Workspace, workdir)
		}
	} else {
		wd = config.GetRunnerConfig().Workspace
	}
	f, err := lookupPathHost(command[0], opt.Env, a.StdOut)
	if err != nil {
		return err
	}
	// build command
	cmd := exec.CommandContext(ctx, f)
	cmd.Path = f
	cmd.Args = command
	cmd.Stdin = nil
	cmd.Stdout = a.StdOut
	cmd.Env = envList
	cmd.Stderr = a.StdOut
	cmd.Dir = wd
	cmd.SysProcAttr = getSysProcAttr(cmdline, false)
	// adapt to non-unix platform, when main process cancel, the sub process is not released, e.g. windows
	// see:  https://github.com/golang/go/issues/6720
	cmd.WaitDelay = 5 * time.Second
	var ppty *os.File
	var tty *os.File
	defer func() {
		if ppty != nil {
			ppty.Close()
		}
		if tty != nil {
			tty.Close()
		}
	}()
	if true /* allocate Terminal */ {
		//var err error
		ppty, tty, err = setupPty(cmd, cmdline)
		if err != nil {
			if !errors.Is(err, pty.ErrUnsupported) {
				logFromCtx.Errorf("Failed to setup Pty %v", err.Error())
			}
		}
	}
	writer := &ptyWriter{Out: a.StdOut}
	logCtx, finishLog := context.WithCancel(context.Background())
	if ppty != nil {
		go copyPtyOutput(writer, ppty, finishLog)
	} else {
		finishLog()
	}
	if ppty != nil {
		go writeKeepAlive(ppty)
	}
	err = cmd.Run()
	// when ctx cancel it expect return context.Canceled, but os/exec will return 'signal: killed'
	// so we need to check the context error
	if errors.Is(ctx.Err(), context.Canceled) {
		// print the error, maybe it's not 'signal: killed' error
		log.Printf("ctx canceled, cmd run err: %v", err.Error())
		err = ctx.Err()
	}
	if err != nil {
		return err
	}
	if tty != nil {
		writer.AutoStop = true
		//nolint:errcheck,govet
		if _, err := tty.Write([]byte("\x04")); err != nil {
			log.Printf("Failed to write EOT: %v", err)
		}
	}
	<-logCtx.Done()

	if ppty != nil {
		ppty.Close()
		ppty = nil
	}
	return err
}

// CopyArchive 复制一个归档文件
func (a *AgentHost) CopyArchive(ctx context.Context, relativePath, distPath string, logger *logger.Logger) (err error) {
	//需要将所有的path文件复制出容器外部且置于artifactName文件夹中
	filePath := relativePath
	// 如果是相对路径则需要拼接到当前工作目录
	if !filepath.IsAbs(relativePath) {
		filePath = filepath.Join(a.WorkingDir, relativePath)
	}
	// 复制到 artifact 目录下
	if util.IsDir(filePath) {
		err = util.CopyDir(filePath, distPath)
	} else {
		err = util.CopyFile(filePath, filepath.Join(distPath, filepath.Base(filePath)))
	}
	if err != nil {
		logger.Errorf("failed to copy artifact %s", distPath)
	}
	return err
}

func (a *AgentHost) Upload(ctx context.Context, opt agent.UploadOption) (*agent.UploadActionResponse, error) {
	return artifact.Upload(ctx, opt, a)
}

func (a *AgentHost) CopyTarStream(ctx context.Context, destPath string, tarStream io.Reader) error {
	if err := os.RemoveAll(destPath); err != nil {
		return err
	}
	tr := tar.NewReader(tarStream)
	cp := &agent.CopyCollector{
		DstDir: destPath,
	}
	for {
		ti, err := tr.Next()
		if errors.Is(err, io.EOF) {
			return nil
		} else if err != nil {
			return err
		}
		if ti.FileInfo().IsDir() {
			continue
		}
		if ctx.Err() != nil {
			return fmt.Errorf("CopyTarStream has been cancelled")
		}
		if err = cp.WriteFile(ti.Name, ti.FileInfo(), ti.Linkname, tr); err != nil {
			return err
		}
	}
}

func (a *AgentHost) CopyDir(ctx context.Context, destPath string, srcPath string, useGitIgnore bool) {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	srcPrefix := filepath.Dir(srcPath)
	if !strings.HasSuffix(srcPrefix, string(filepath.Separator)) {
		srcPrefix += string(filepath.Separator)
	}
	logFromCtx.Debugf("Stripping prefix:%s src:%s", srcPrefix, srcPath)
	var ignorer gitignore.Matcher
	if useGitIgnore {
		ps, err := gitignore.ReadPatterns(polyfill.New(osfs.New(srcPath)), nil)
		if err != nil {
			logFromCtx.Debugf("Error loading .gitignore: %v", err)
		}
		ignorer = gitignore.NewMatcher(ps)
	}
	fc := &agent.FileCollector{
		Fs:        &agent.DefaultFs{},
		Ignorer:   ignorer,
		SrcPath:   srcPath,
		SrcPrefix: srcPrefix,
		Handler: &agent.CopyCollector{
			DstDir: destPath,
		},
	}
	_ = filepath.Walk(srcPath, fc.CollectFiles(ctx, []string{}))
}

func (a *AgentHost) GetArchive(ctx context.Context, opt agent.ArchiveOption) (io.ReadCloser, error) {
	buf := &bytes.Buffer{}
	tw := tar.NewWriter(buf)
	defer tw.Close()
	srcPath := filepath.Clean(opt.Path)
	fi, err := os.Lstat(srcPath)
	if err != nil {
		return nil, err
	}
	tc := &agent.TarCollector{
		TarWriter: tw,
	}
	if fi.IsDir() {
		srcPrefix := filepath.Dir(srcPath)
		if !strings.HasSuffix(srcPrefix, string(filepath.Separator)) {
			srcPrefix += string(filepath.Separator)
		}
		fc := &agent.FileCollector{
			Fs:        &agent.DefaultFs{},
			SrcPath:   srcPath,
			SrcPrefix: srcPrefix,
			Handler:   tc,
		}
		err = filepath.Walk(srcPath, fc.CollectFiles(ctx, []string{}))
		if err != nil {
			return nil, err
		}
	} else {
		var f io.ReadCloser
		var linkname string
		if fi.Mode()&fs.ModeSymlink != 0 {
			linkname, err = os.Readlink(srcPath)
			if err != nil {
				return nil, err
			}
		} else {
			f, err = os.Open(srcPath)
			if err != nil {
				return nil, err
			}
			defer f.Close()
		}
		err = tc.WriteFile(fi.Name(), fi, linkname, f)
		if err != nil {
			return nil, err
		}
	}
	return io.NopCloser(buf), nil
}

func (a *AgentHost) Download(ctx context.Context, opt agent.DownloadOption) error {
	return artifact.Download(ctx, opt)
}

func (a *AgentHost) Clean(ctx context.Context) error {
	// update caches
	a.updateCaches(ctx)
	// because action will clean workspace, so we don't need to clean workspace
	logger.GetLoggerFromCtx(ctx).Info("clean workspace")
	// return
	return nil
}

func (a *AgentHost) updateCaches(ctx context.Context) {
	if err := a.cache.Update(ctx, a.Caches); err != nil {
		logger.GetLoggerFromCtx(ctx).Errorf("Failed to update caches: %v", err)
	}
}
