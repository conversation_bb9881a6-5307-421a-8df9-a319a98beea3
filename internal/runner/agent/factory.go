package agent

import (
	"pipeline/internal/runner/agent/container"
	"pipeline/internal/runner/agent/host"
	"pipeline/internal/runner/agent/kubernetes"
	"pipeline/pkg/runner/agent"
)

// NewAgent 创建 Agent 代理
func NewAgent(name string) agent.Agent {
	switch name {
	case agent.HostAgent.String():
		return host.NewAgentHost()
	case agent.Kubernetes.String():
		return kubernetes.NewAgentKubernetes()
	case agent.Container.String():
		return container.NewDockerContainer()
	default:
		return container.NewDockerContainer()
	}
}
