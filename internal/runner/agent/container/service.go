package container

import (
	"fmt"
	"pipeline/pkg/runner/agent"
)

func init() {
	agent.RegisterService(agent.DockerService, agent.Container, DockerService{})
}

var _ agent.ServiceAgent = DockerService{}

type DockerService struct{}

func (d DockerService) Name() string {
	return agent.DockerService.String()
}

func (d DockerService) Apply(a agent.Agent) error {
	// a must be k8s agent, cast it to k8s agent, if not, return error
	dc, ok := a.(*DockerContainer)
	if !ok {
		return fmt.Errorf("agent is not docker agent, can not apply dind service")
	}
	// bind docker socket to container, because we need to run docker in container
	dc.agentOpts.Binds = append(dc.agentOpts.Binds, "/var/run/docker.sock:/var/run/docker.sock")
	return nil
}
