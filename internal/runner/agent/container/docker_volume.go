//go:build !(WITHOUT_DOCKER || !(linux || darwin || windows || netbsd))

package container

import (
	"context"
	"git.makeblock.com/makeblock-go/log"
	"github.com/docker/docker/api/types/filters"
	"github.com/docker/docker/api/types/volume"
	"github.com/docker/docker/client"
)

// DockerVolumeRemove 检查是否存在指定的docker volume，如果存在则删除
func DockerVolumeRemove(ctx context.Context, volumeName string, force bool) error {
	cli, err := GetDockerClient(ctx)
	if err != nil {
		return err
	}
	defer func(cli client.APIClient) {
		err = cli.Close()
		if err != nil {
			log.ErrorE("docker cli close error", err)
		}
	}(cli)
	list, err := cli.VolumeList(ctx, volume.ListOptions{Filters: filters.NewArgs()})
	if err != nil {
		return err
	}
	for _, vol := range list.Volumes {
		if vol.Name == volumeName {
			if err = cli.VolumeRemove(ctx, volumeName, force); err != nil {
				return err
			}
		}
	}
	return nil
}
