package container

import (
	"context"
	"git.makeblock.com/makeblock-go/log"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/client"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
)

type LinuxContainerEnvironmentExtensions struct {
}

func (*LinuxContainerEnvironmentExtensions) ToContainerPath(path string) string {
	if runtime.GOOS == "windows" && strings.Contains(path, "/") {
		log.Error("You cannot specify linux style local paths (/mnt/etc) on Windows as it does not understand them.")
		return ""
	}
	absPath, err := filepath.Abs(path)
	if err != nil {
		log.ErrorE("", err)
		return ""
	}
	windowsPathRegex := regexp.MustCompile(`^([a-zA-Z]):\\(.+)$`)
	windowsPathComponents := windowsPathRegex.FindStringSubmatch(absPath)
	if windowsPathComponents == nil {
		return absPath
	}
	driveLetter := strings.ToLower(windowsPathComponents[1])
	translatedPath := strings.ReplaceAll(windowsPathComponents[2], `\`, `/`)
	result := strings.Join([]string{"/mnt", driveLetter, translatedPath}, `/`)
	return result
}

func (*LinuxContainerEnvironmentExtensions) GetPathVariableName() string {
	return "PATH"
}

func (*LinuxContainerEnvironmentExtensions) DefaultPathVariable() string {
	return "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
}

func (*LinuxContainerEnvironmentExtensions) JoinPathVariable(paths ...string) string {
	return strings.Join(paths, ":")
}

func (*LinuxContainerEnvironmentExtensions) GetRunnerContext(ctx context.Context) map[string]any {
	return map[string]any{
		"os":         "Linux",
		"arch":       RunnerArch(ctx),
		"temp":       "/tmp",
		"tool_cache": "/opt/hostedtoolcache",
	}
}

func GetHostInfo(ctx context.Context) (info types.Info, err error) {
	var cli client.APIClient
	cli, err = GetDockerClient(ctx)
	if err != nil {
		return info, err
	}
	defer func(cli client.APIClient) {
		_ = cli.Close()
	}(cli)

	info, err = cli.Info(ctx)
	if err != nil {
		return info, err
	}

	return info, nil
}

func RunnerArch(ctx context.Context) string {
	info, err := GetHostInfo(ctx)
	if err != nil {
		return ""
	}
	archMapper := map[string]string{
		"x86_64":  "X64",
		"386":     "X86",
		"aarch64": "ARM64",
	}
	if arch, ok := archMapper[info.Architecture]; ok {
		return arch
	}
	return info.Architecture
}

func (*LinuxContainerEnvironmentExtensions) IsEnvironmentCaseInsensitive() bool {
	return false
}
