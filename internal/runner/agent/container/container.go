package container

//
//import (
//	"context"
//	"io"
//
//	"pipeline/pkg/runner/agent"
//)
//
//var _ agent.Agent = (*AgentContainer)(nil)
//
//type AgentContainer struct {
//	c Container
//}
//
//type Container interface {
//	Pull(ctx context.Context, opt agent.PrepareOption) error
//	Prepare(ctx context.Context, opt agent.PrepareOption) error
//	Start(ctx context.Context) error
//	Checkout(ctx context.Context, opt agent.CheckoutOption) error
//	Exec(ctx context.Context, opt agent.ExecOption) error
//	Upload(ctx context.Context, opt agent.UploadOption) (*agent.UploadActionResponse, error)
//	Download(ctx context.Context, opt agent.DownloadOption) error
//	Copy(ctx context.Context, opt agent.CopyOption) error
//	CopyDir(ctx context.Context, destPath string, srcPath string, useGitIgnore bool) error
//	GetArchive(ctx context.Context, opt agent.ArchiveOption) (io.ReadCloser, error)
//	Remove(ctx context.Context) error
//}
//
//func NewAgentContainer() *AgentContainer {
//	return &AgentContainer{
//		c: NewDockerContainer(),
//	}
//}
//
//func (a *AgentContainer) Prepare(ctx context.Context, opt agent.PrepareOption) error {
//	return a.c.Prepare(ctx, opt)
//}
//
//func (a *AgentContainer) Checkout(ctx context.Context, opt agent.CheckoutOption) error {
//	return a.c.Checkout(ctx, opt)
//}
//
//func (a *AgentContainer) Exec(ctx context.Context, opt agent.ExecOption) error {
//	return a.c.Exec(ctx, opt)
//}
//
//func (a *AgentContainer) Upload(ctx context.Context, opt agent.UploadOption) (*agent.UploadActionResponse, error) {
//	return a.c.Upload(ctx, opt)
//}
//
//func (a *AgentContainer) Download(ctx context.Context, opt agent.DownloadOption) error {
//	return a.c.Download(ctx, opt)
//}
//
//func (a *AgentContainer) GetArchive(ctx context.Context, opt agent.ArchiveOption) (io.ReadCloser, error) {
//	return a.c.GetArchive(ctx, opt)
//}
//
//func (a *AgentContainer) Copy(ctx context.Context, opt agent.CopyOption) error {
//	return a.c.Copy(ctx, opt)
//}
//
//func (a *AgentContainer) Clean(ctx context.Context) error {
//	return a.c.Remove(ctx)
//}
