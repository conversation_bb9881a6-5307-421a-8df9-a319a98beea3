package container

import (
	"archive/tar"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/git"
	"pipeline/internal/runner/logger"
	pkgCommon "pipeline/pkg/common"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/runner/artifact"
	"pipeline/pkg/util"
	"regexp"
	"runtime"
	"strconv"
	"strings"

	"git.makeblock.com/makeblock-go/log"
	"github.com/avast/retry-go"
	"github.com/docker/docker/pkg/namesgenerator"
	"github.com/joho/godotenv"

	"github.com/Masterminds/semver"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/mount"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/client"
	"github.com/docker/docker/pkg/stdcopy"
	"github.com/go-git/go-billy/v5/helper/polyfill"
	"github.com/go-git/go-billy/v5/osfs"
	"github.com/go-git/go-git/v5/plumbing/format/gitignore"
	"github.com/moby/term"
	spec "github.com/opencontainers/image-spec/specs-go/v1"
)

var _ agent.Agent = &DockerContainer{}

type AgentDockerOpts struct {
	ForcePull      bool
	Binds          []string
	Username       string
	Password       string
	Entrypoint     []string
	Cmd            []string
	Mounts         map[string]string
	Name           string
	Stdout         io.Writer
	Stderr         io.Writer
	NetworkMode    string
	Privileged     bool
	UserNSMode     string
	Platform       string
	Options        string
	NetworkAliases []string
}

type DockerContainerConfigPrepareOpts struct {
	containerName    string
	config           *container.Config
	platform         *spec.Platform
	hostConfig       *container.HostConfig
	networkingConfig *network.NetworkingConfig
}

type DockerContainer struct {
	// options
	agentOpts                  *AgentDockerOpts
	prepareOpts                *agent.PrepareOption
	containerConfigPrepareOpts DockerContainerConfigPrepareOpts
	// step context
	stepContext *pipeline.StepContext
	// docker client
	cli client.APIClient
	id  string //容器id
	UID int
	GID int
	LinuxContainerEnvironmentExtensions
}

func NewDockerContainer() agent.Agent {
	return &DockerContainer{}
}

func (d *DockerContainer) Name() string {
	return agent.Container.String()
}

func (d *DockerContainer) prepareAgentConfig(ctx context.Context) error {
	d.stepContext = common.GetStepContextFromCtx(ctx)
	if d.stepContext == nil {
		return fmt.Errorf("step context not found")
	}
	d.agentOpts = &AgentDockerOpts{}
	// default working dir
	if d.prepareOpts.WorkingDir == "" {
		d.prepareOpts.WorkingDir = fmt.Sprintf("%s/%s",
			common.ContainerWorkspace, d.stepContext.App.Identity)
	}
	// service
	for _, service := range d.prepareOpts.Services {
		if svc := agent.GetService(service, agent.Container); svc != nil {
			if err := svc.Apply(d); err != nil {
				return err
			}
		}
	}
	return nil
}

func (d *DockerContainer) prepareBuildCache(binds *[]string) {
	// prevent duplicate cache paths
	cacheMap := make(map[string]string)
	for _, cache := range d.prepareOpts.Caches {
		// global cache priority
		if _, ok := agent.CommonCacheMap[cache]; ok {
			continue
		}
		cacheMap[cache] = cache
	}
	// global cache, override pipeline cache
	cacheRootPath := common.GetPipelineCacheRootPath()
	for k, v := range agent.CommonCacheMap {
		key := fmt.Sprintf("%s/%s", cacheRootPath, util.RemoveChars(k, common.CharsToRemove))
		*binds = append(*binds, fmt.Sprintf("%s:%s", key, v))
	}
	// pipeline cache
	cacheBasePath := common.GetPipelineCacheBasePath(*d.stepContext)
	for _, cache := range cacheMap {
		localPath := filepath.Join(cacheBasePath, util.RemoveChars(cache, common.CharsToRemove))
		cachePath := fmt.Sprintf("%s:%s", localPath, cache)
		*binds = append(*binds, cachePath)
	}
}

func (d *DockerContainer) prepareContainer(ctx context.Context) error {
	// create host working dir
	if err := util.CreateFolder(common.GetPipelineWorkspace(*d.stepContext)); err != nil {
		return err
	}
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	d.agentOpts.Stdout = logFromCtx
	d.agentOpts.Stderr = logFromCtx
	isTerminal := term.IsTerminal(uintptr(int(os.Stdout.Fd())))
	// create container config
	config := &container.Config{
		Image:      d.prepareOpts.Image,
		WorkingDir: d.prepareOpts.WorkingDir,
		Env:        envMap2Arr(d.prepareOpts.Env),
		Tty:        isTerminal,
	}
	if len(d.agentOpts.Username) > 0 {
		config.User = d.agentOpts.Username
	}
	logFromCtx.Infof("common container config ==> %+v", config)
	if len(d.agentOpts.Cmd) != 0 {
		config.Cmd = d.agentOpts.Cmd
	}
	if len(d.agentOpts.Entrypoint) != 0 {
		config.Entrypoint = d.agentOpts.Entrypoint
	} else {
		config.Entrypoint = runner.GetBuildAndHelperContainersCommand()
	}
	mounts := make([]mount.Mount, 0)
	for mountSource, mountTarget := range d.agentOpts.Mounts {
		mounts = append(mounts, mount.Mount{
			Type:   mount.TypeVolume,
			Source: mountSource,
			Target: mountTarget,
		})
	}
	var platSpecs *spec.Platform
	if supportsContainerImagePlatform(ctx, d.cli) && d.agentOpts.Platform != "" {
		desiredPlatform := strings.SplitN(d.agentOpts.Platform, `/`, 2)
		if len(desiredPlatform) != 2 {
			return fmt.Errorf("incorrect container platform option '%s'", d.agentOpts.Platform)
		}
		platSpecs = &spec.Platform{
			Architecture: desiredPlatform[1],
			OS:           desiredPlatform[0],
		}
	}
	// binds 挂载代码 & 设置缓存
	binds := make([]string, 0)
	// with bind
	binds = append(binds, d.agentOpts.Binds...)

	// 挂载工作空间
	binds = append(binds, fmt.Sprintf("%s:%s",
		common.GetStepWorkspace(d.stepContext), d.prepareOpts.WorkingDir)) //挂载代码

	// build cache
	d.prepareBuildCache(&binds)

	// 创建运行时环境
	hostConfig := &container.HostConfig{
		Binds:       binds,
		Mounts:      mounts,
		NetworkMode: container.NetworkMode(d.agentOpts.NetworkMode),
		Privileged:  d.agentOpts.Privileged,
		UsernsMode:  container.UsernsMode(d.agentOpts.UserNSMode),
		//CapAdd:       capAdd,
		//CapDrop:      capDrop,
		//PortBindings: input.PortBindings,
	}

	if d.stepContext.Debug {
		logFromCtx.Infof("common container.HostConfig ==> %+v", hostConfig)
	}

	var networkingConfig *network.NetworkingConfig
	logFromCtx.Info(fmt.Sprintf("input.NetworkAliases ==> %v", d.agentOpts.NetworkAliases))
	n := hostConfig.NetworkMode
	if !n.IsDefault() && !n.IsBridge() && !n.IsHost() && !n.IsNone() &&
		!n.IsContainer() && len(d.agentOpts.NetworkAliases) > 0 {
		endpointConfig := &network.EndpointSettings{
			Aliases: d.agentOpts.NetworkAliases,
		}
		networkingConfig = &network.NetworkingConfig{
			EndpointsConfig: map[string]*network.EndpointSettings{
				d.agentOpts.NetworkMode: endpointConfig,
			},
		}
	} else {
		logFromCtx.Info("not a use defined config")
	}
	name := fmt.Sprintf("%s-%s", d.stepContext.App.Identity, namesgenerator.GetRandomName(10))
	// set container config
	d.containerConfigPrepareOpts = DockerContainerConfigPrepareOpts{
		containerName:    strings.ReplaceAll(name, "_", ""),
		platform:         platSpecs,
		config:           config,
		hostConfig:       hostConfig,
		networkingConfig: networkingConfig,
	}
	return nil
}

func (d *DockerContainer) startContainer(ctx context.Context) error {
	// 解决挂载目录后被容器修改权限成为root问题,导致宿主机无法编辑的问题,通过设置用户和组
	//if config.User == "" {
	//	uid := strconv.Itoa(os.Getuid())
	//	gid := strconv.Itoa(os.Getgid())
	//	config.User = uid + ":" + gid
	//}
	// 如果ctx被取消，容器已经创建，但是没有启动，需要删除
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	cf := d.containerConfigPrepareOpts

	resp, err := d.cli.ContainerCreate(ctx, cf.config,
		cf.hostConfig, cf.networkingConfig, cf.platform, cf.containerName)
	if err != nil {
		logFromCtx.Errorf("failed to create container: '%v'", err)
		return err
	}

	d.id = resp.ID

	logFromCtx.Infof("env ==> %v", d.prepareOpts.Env)
	logFromCtx.Infof("created container name=%s (platform: %s)", cf.containerName, d.agentOpts.Platform)

	//直接进行启动容器
	return d.Start(ctx)
}

func (d *DockerContainer) Start(ctx context.Context) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	logFromCtx.Infof("starting container: %v", d.id)
	if err := d.cli.ContainerStart(ctx, d.id, types.ContainerStartOptions{}); err != nil {
		return fmt.Errorf("failed to start container: %w", err)
	}
	logFromCtx.Infof("Started container: %v", d.id)
	// 读取容器的用户和组
	d.tryReadID(ctx, "-u", func(id int) { d.UID = id })
	d.tryReadID(ctx, "-g", func(id int) { d.GID = id })
	//if d.UID != 0 || d.GID != 0 {
	//	_ = d.Exec(ctx, agent.ExecOption{
	//		Command: fmt.Sprintf("chown -R %d:%d %s", d.UID, d.GID, d.option.WorkingDir),
	//		User:    "0",
	//		Workdir: "",
	//	})
	//}
	return nil
}

func (d *DockerContainer) tryReadID(ctx context.Context, opt string, cbk func(id int)) {
	idResp, err := d.cli.ContainerExecCreate(ctx, d.id, types.ExecConfig{
		Cmd:          []string{"id", opt},
		AttachStdout: true,
		AttachStderr: true,
	})
	if err != nil {
		return
	}

	resp, err := d.cli.ContainerExecAttach(ctx, idResp.ID, types.ExecStartCheck{})
	if err != nil {
		return
	}
	defer resp.Close()

	sid, err := resp.Reader.ReadString('\n')
	if err != nil {
		return
	}
	exp := regexp.MustCompile(`\d+\n`)
	found := exp.FindString(sid)
	id, err := strconv.ParseInt(strings.TrimSpace(found), 10, 32)
	if err != nil {
		return
	}
	cbk(int(id))
}

func (d *DockerContainer) pullImage(ctx context.Context) error {

	logFromCtx := logger.GetLoggerFromCtx(ctx)
	logFromCtx.Infof("docker pull %s", d.prepareOpts.Image)

	pull := d.agentOpts.ForcePull

	if !pull {
		imageExists, err := ImageExistsLocally(ctx, d.prepareOpts.Image, d.agentOpts.Platform)
		logFromCtx.Infof("image exists %v", imageExists)
		if err != nil {
			return fmt.Errorf("unable to determine if image already exists for image '%s' (%s): %w",
				d.prepareOpts.Image, d.agentOpts.Platform, err)
		}
		if !imageExists {
			pull = true
		}
	}

	if !pull {
		return nil
	}

	imageRef := cleanImage(ctx, d.prepareOpts.Image)
	logFromCtx.Infof("pulling image '%v' (%s)", imageRef, d.agentOpts.Platform)
	imagePullOptions := types.ImagePullOptions{}
	reader, err := d.cli.ImagePull(ctx, imageRef, imagePullOptions)
	_ = logDockerResponse(logFromCtx, reader, err != nil)

	if err != nil {
		if imagePullOptions.RegistryAuth != "" && strings.Contains(err.Error(), "unauthorized") {
			logFromCtx.Errorf("pulling image '%v' (%s) failed with credentials %s retrying without them, "+
				"please check for stale docker config files", imageRef, d.agentOpts.Platform, err.Error())
			imagePullOptions.RegistryAuth = ""
			reader, err = d.cli.ImagePull(ctx, imageRef, imagePullOptions)
			_ = logDockerResponse(logFromCtx, reader, err != nil)
		}
		return err
	}
	return nil
}

func (d *DockerContainer) initContainerEnvironment(ctx context.Context) error {
	// copy file
	for _, file := range d.prepareOpts.PrepareFiles {
		err := d.Copy(ctx, file)
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *DockerContainer) Prepare(ctx context.Context, opt agent.PrepareOption) error {
	// set prepare opt
	d.prepareOpts = &opt
	// set agent opt by input
	if err := d.prepareAgentConfig(ctx); err != nil {
		return err
	}
	// load docker client
	if err := d.loadDockerClient(ctx); err != nil {
		return err
	}
	// pull image
	if err := d.pullImage(ctx); err != nil {
		return err
	}
	// prepare container config
	if err := d.prepareContainer(ctx); err != nil {
		return err
	}
	// start container
	if err := d.startContainer(ctx); err != nil {
		return err
	}
	// init container environment
	return d.initContainerEnvironment(ctx)
}

func envMap2Arr(env map[string]string) []string {
	envArr := make([]string, 0)
	for k, v := range env {
		envArr = append(envArr, fmt.Sprintf("%s=%s", k, v))
	}
	return envArr
}

func (d *DockerContainer) Checkout(ctx context.Context, opt agent.CheckoutOption) error {
	// CloneAndCheckout
	hash, err := git.Checkout(ctx, opt)
	if err != nil {
		return err
	}
	// Checkout hash
	if hash == nil {
		return errors.New("checkout hash is nil")
	}
	return nil
}

func (d *DockerContainer) Exec(ctx context.Context, opt agent.ExecOption) error {

	stepCtx := common.GetStepContextFromCtx(ctx)
	LOG := logger.GetLoggerFromCtx(ctx)
	cmd, err := d.setupShellCommandExecutor(ctx, opt)
	if err != nil {
		LOG.Errorf("failed to setup exec: %v", err)
		return err
	}

	workdir := opt.Workdir
	user := opt.User

	// Fix slashes when running on Windows
	if runtime.GOOS == pkgCommon.Windows {
		var newCmd []string
		for _, v := range cmd {
			newCmd = append(newCmd, strings.ReplaceAll(v, `\`, `/`))
		}
		cmd = newCmd
	}

	isTerminal := term.IsTerminal(uintptr(int(os.Stdout.Fd())))
	envList := make([]string, 0)
	for k, v := range opt.Env {
		envList = append(envList, fmt.Sprintf("%s=%s", k, v))
	}

	var wd string
	if workdir != "" {
		if strings.HasPrefix(workdir, "/") {
			wd = workdir
		} else {
			wd = fmt.Sprintf("%s/%s", d.prepareOpts.WorkingDir, workdir)
		}
	} else {
		// default to the step workspace
		wd = fmt.Sprintf("%s/%s", common.ContainerWorkspace, stepCtx.App.Identity)
	}

	// set PATH
	envList = append(envList, d.buildPaths(opt.PATH)) // update image , so it unnecessary
	// create exec
	idResp, err := d.cli.ContainerExecCreate(ctx, d.id, types.ExecConfig{
		User:         user,
		Cmd:          cmd,
		WorkingDir:   wd,
		Env:          envList,
		Tty:          isTerminal,
		AttachStderr: true,
		AttachStdout: true,
	})
	if err != nil {
		return fmt.Errorf("failed to create exec: %w", err)
	}

	resp, err := d.cli.ContainerExecAttach(ctx, idResp.ID, types.ExecStartCheck{
		Tty: isTerminal,
	})
	if err != nil {
		return fmt.Errorf("failed to attach to exec: %w", err)
	}
	defer resp.Close()

	err = d.waitForCommand(ctx, isTerminal, resp, idResp, user, workdir)
	if err != nil {
		return err
	}

	inspectResp, err := d.cli.ContainerExecInspect(ctx, idResp.ID)
	if err != nil {
		return fmt.Errorf("failed to inspect exec: %w", err)
	}

	switch inspectResp.ExitCode {
	case 0:
		return nil
	case 127:
		return fmt.Errorf("exitcode '%d': command not found ", inspectResp.ExitCode)
	default:
		return fmt.Errorf("exitcode '%d': failure", inspectResp.ExitCode)
	}
}

// nolint
func (d *DockerContainer) buildPaths(paths []string) string {
	sb := strings.Builder{}
	sb.WriteString("PATH=")
	for _, p := range paths {
		sb.WriteString(p)
		sb.WriteString(":")
	}
	defaultPath := d.DefaultPathVariable()
	// get default path
	envMap, _ := d.extractFromImageEnv(context.Background())
	if v, ok := envMap["PATH"]; ok && v != "" {
		defaultPath = v
	}
	sb.WriteString(defaultPath)
	return sb.String()
}

// nolint
func (d *DockerContainer) extractFromImageEnv(ctx context.Context) (map[string]string, error) {
	envMap := make(map[string]string)
	inspect, _, err := d.cli.ImageInspectWithRaw(ctx, d.prepareOpts.Image)
	if err != nil {
		log.Error(err.Error())
		return nil, fmt.Errorf("inspect image: %w", err)
	}
	if inspect.Config == nil {
		return nil, nil
	}
	imageEnv, err := godotenv.Unmarshal(strings.Join(inspect.Config.Env, "\n"))
	if err != nil {
		log.Error(err.Error())
		return nil, fmt.Errorf("unmarshal image env: %w", err)
	}
	for k, v := range imageEnv {
		// nolint
		if k == "PATH" {
			if envMap[k] == "" {
				envMap[k] = v
			} else {
				envMap[k] += `:` + v
			}
		} else if envMap[k] == "" {
			envMap[k] = v
		}
	}
	return envMap, nil
}

func (d *DockerContainer) waitForCommand(ctx context.Context, isTerminal bool, resp types.HijackedResponse, _ types.IDResponse, _ string, _ string) error {

	logFromCtx := logger.GetLoggerFromCtx(ctx)

	cmdResponse := make(chan error)

	go func() {
		var outWriter io.Writer
		outWriter = d.agentOpts.Stdout
		if outWriter == nil {
			outWriter = os.Stdout
		}
		errWriter := d.agentOpts.Stderr
		if errWriter == nil {
			errWriter = os.Stderr
		}

		var err error
		if !isTerminal || os.Getenv("NORAW") != "" {
			_, err = stdcopy.StdCopy(outWriter, errWriter, resp.Reader)
		} else {
			_, err = io.Copy(outWriter, resp.Reader)
		}
		cmdResponse <- err
	}()

	select {
	case <-ctx.Done():
		// send ctrl + c
		_, err := resp.Conn.Write([]byte{3})
		if err != nil {
			logFromCtx.Errorf("Failed to send CTRL+C: %+v", err)
		}
		// we return the context canceled error to prevent other steps
		// from executing
		return ctx.Err()
	case err := <-cmdResponse:
		if err != nil {
			logFromCtx.Error(err.Error())
		}
		return nil
	}
}

func (d *DockerContainer) setupShellCommandExecutor(ctx context.Context, opt agent.ExecOption) ([]string, error) {
	// host: step workspace/host executor
	opt.ScriptName = util.Coalesce(opt.ScriptName, agent.DefaultScriptName)
	opt.ScriptPath = util.Coalesce(opt.ScriptPath, agent.DefaultContainerPath)
	// setup shell command
	stepCtx := common.GetStepContextFromCtx(ctx)
	name, script, _, cmd, _, err := runner.SetupShellCommand(stepCtx, opt)
	if err != nil {
		return nil, err
	}
	//复制到容器中
	err = d.Copy(ctx, agent.CopyOption{
		DestPath: opt.ScriptPath,
		Files: []*agent.FileEntry{
			{
				Name: name,
				Mode: 0o755,
				Body: script,
			},
		},
	})
	return cmd, err
}

// ReadContainerArchive 读取容器归档文件
func (d *DockerContainer) ReadContainerArchive(reader io.ReadCloser, artifactFullPath string) error {
	if reader != nil {
		defer reader.Close()
	}
	tarReader := tar.NewReader(reader)
	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		//nolint:gosec
		target := filepath.Join(artifactFullPath, header.Name)
		switch header.Typeflag {
		case tar.TypeDir:
			//nolint:gosec
			if err = os.MkdirAll(target, os.FileMode(header.Mode)); err != nil {
				return err
			}
		case tar.TypeReg:
			file, err := os.OpenFile(target, os.O_CREATE|os.O_RDWR, os.FileMode(header.Mode)) //nolint:gosec
			if err != nil {
				return err
			}
			//nolint:gosec
			if _, err = io.Copy(file, tarReader); err != nil {
				return err
			}
			file.Close()
		default:
			return fmt.Errorf("unsupported type: %v", header.Typeflag)
		}
	}
	return nil
}

// CopyArchive 复制一个归档文件
func (d *DockerContainer) CopyArchive(ctx context.Context, relativePath, distPath string, logger *logger.Logger) error {
	filePath := relativePath
	// 如果是相对路径则需要拼接到当前工作目录
	if !filepath.IsAbs(relativePath) {
		filePath = filepath.Join(d.prepareOpts.WorkingDir, relativePath)
	}
	reader, _, e := d.cli.CopyFromContainer(ctx, d.id, filePath)
	if e != nil {
		logger.Errorf("copying from container failed with error: %v", e)
		return e
	}
	if reader != nil {
		err := d.ReadContainerArchive(reader, distPath)
		if err != nil {
			logger.Errorf("read from container file failed with error: %v", err)
			return err
		}
	}
	return nil
}

// Upload 上传制品
func (d *DockerContainer) Upload(ctx context.Context, opt agent.UploadOption) (*agent.UploadActionResponse, error) {
	return artifact.Upload(ctx, opt, d)
}

// Download 下载制品
func (d *DockerContainer) Download(ctx context.Context, opt agent.DownloadOption) error {
	return artifact.Download(ctx, opt)
}

// Copy destPath must be a path, not a full path file name
func (d *DockerContainer) Copy(ctx context.Context, opt agent.CopyOption) error {

	files := opt.Files
	destPath := opt.DestPath

	if destPath == "" {
		destPath = d.prepareOpts.WorkingDir
	}

	// Mkdir
	buffer := &bytes.Buffer{}
	twr := tar.NewWriter(buffer)
	_ = twr.WriteHeader(&tar.Header{
		Name:     destPath,
		Mode:     0o777,
		Typeflag: tar.TypeDir,
	})
	twr.Close()
	// ignore existing error
	_ = d.cli.CopyToContainer(ctx, d.id, "/", buffer, types.CopyToContainerOptions{})

	// Copy
	var buf bytes.Buffer
	tw := tar.NewWriter(&buf)

	for _, file := range files {
		hdr := &tar.Header{
			Name: file.Name,
			Mode: file.Mode,
			Size: int64(len(file.Body)),
			Uid:  d.UID,
			Gid:  d.GID,
		}
		if err := tw.WriteHeader(hdr); err != nil {
			return err
		}
		if _, err := tw.Write([]byte(file.Body)); err != nil {
			return err
		}
	}
	if err := tw.Close(); err != nil {
		return err
	}

	if err := d.cli.CopyToContainer(ctx, d.id, destPath, &buf, types.CopyToContainerOptions{
		AllowOverwriteDirWithFile: true,
	}); err != nil {
		return fmt.Errorf("failed to copy content to container: %w", err)
	}

	// 将文件的权限设置为容器的用户和组
	//if d.UID != 0 || d.GID != 0 {
	//	_ = d.Exec(ctx, agent.ExecOption{
	//		Command: fmt.Sprintf("chown -R %d:%d %s", d.UID, d.GID, destPath),
	//		User:    "0",
	//		Workdir: "",
	//	})
	//}

	return nil
}

func (d *DockerContainer) CopyDir(ctx context.Context, destPath string, srcPath string, useGitIgnore bool) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	tarFile, err := os.CreateTemp("", "act")
	if err != nil {
		return err
	}
	logFromCtx.Info(fmt.Sprintf("Writing tarball %s from %s", tarFile.Name(), srcPath))
	defer func(tarFile *os.File) {
		name := tarFile.Name()
		err = tarFile.Close()
		if !errors.Is(err, os.ErrClosed) {
			logFromCtx.Error(err.Error())
		}
		err = os.Remove(name)
		if err != nil {
			logFromCtx.Error(err.Error())
		}
	}(tarFile)
	tw := tar.NewWriter(tarFile)

	srcPrefix := filepath.Dir(srcPath)
	if !strings.HasSuffix(srcPrefix, string(filepath.Separator)) {
		srcPrefix += string(filepath.Separator)
	}
	logFromCtx.Info(fmt.Sprintf("Stripping prefix:%s src:%s", srcPrefix, srcPath))

	var ignorer gitignore.Matcher
	if useGitIgnore {
		ps, e := gitignore.ReadPatterns(polyfill.New(osfs.New(srcPath)), nil)
		if e != nil {
			logFromCtx.Info(fmt.Sprintf("Error loading .gitignore: %v", e))
		}

		ignorer = gitignore.NewMatcher(ps)
	}

	fc := &agent.FileCollector{
		Fs:        &agent.DefaultFs{},
		Ignorer:   ignorer,
		SrcPath:   srcPath,
		SrcPrefix: srcPrefix,
		Handler: &agent.TarCollector{
			TarWriter: tw,
			UID:       d.UID,
			GID:       d.GID,
			DstDir:    destPath[1:],
		},
	}

	err = filepath.Walk(srcPath, fc.CollectFiles(ctx, []string{}))
	if err != nil {
		return err
	}
	if err = tw.Close(); err != nil {
		return err
	}

	logFromCtx.Info(fmt.Sprintf("Extracting content from '%s' to '%s'", tarFile.Name(), destPath))
	_, err = tarFile.Seek(0, 0)
	if err != nil {
		return fmt.Errorf("failed to seek tar archive: %w", err)
	}
	err = d.cli.CopyToContainer(ctx, d.id, "/", tarFile, types.CopyToContainerOptions{})
	if err != nil {
		return fmt.Errorf("failed to copy content to container: %w", err)
	}
	// If this fails, then folders have wrong permissions on non root container
	// 将文件的权限设置为容器的用户和组
	//if d.UID != 0 || d.GID != 0 {
	//	_ = d.Exec(ctx, agent.ExecOption{
	//		Command: fmt.Sprintf("chown -R %d:%d %s", d.UID, d.GID, destPath),
	//		User:    "0",
	//		Workdir: "",
	//	})
	//}
	return nil
}

// Remove 移除容器
func (d *DockerContainer) Remove(ctx context.Context) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	if d.id == "" {
		return nil
	}
	logFromCtx.Info(fmt.Sprintf("try to remove container: %v", d.id))
	// retry to remove container, because moby has a bug that sometimes it fails to remove container
	// see: https://github.com/moby/moby/issues/43094
	err := retry.Do(func() error {
		return d.cli.ContainerRemove(context.Background(), d.id, types.ContainerRemoveOptions{
			RemoveVolumes: true,
			Force:         true,
		})
	}, retry.Attempts(3))
	// failed to remove container, log error
	if err != nil {
		log.ErrorE("failed to remove container", err) // report error to sentry
		logFromCtx.Error(fmt.Sprintf("failed to remove container: %v", err))
		//return err
	} else {
		logFromCtx.Info(fmt.Sprintf("removed container: %v", d.id))
	}
	d.id = ""
	return d.Close()
}

func (d *DockerContainer) Close() error {
	if d.cli != nil {
		err := d.cli.Close()
		d.cli = nil
		if err != nil {
			return fmt.Errorf("failed to close client: %w", err)
		}
	}
	return nil
}

func (d *DockerContainer) GetArchive(ctx context.Context, opt agent.ArchiveOption) (io.ReadCloser, error) {
	srcPath := filepath.Join(d.prepareOpts.WorkingDir, opt.Path)
	if opt.Absolute {
		srcPath = opt.Path
	}
	a, _, err := d.cli.CopyFromContainer(ctx, d.id, srcPath)
	return a, err
}

func (d *DockerContainer) loadDockerClient(ctx context.Context) (err error) {
	dockerClient, err := GetDockerClient(ctx)
	if err != nil {
		return err
	}
	d.cli = dockerClient
	return nil
}

func supportsContainerImagePlatform(ctx context.Context, cli client.APIClient) bool {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	ver, err := cli.ServerVersion(ctx)
	if err != nil {
		logFromCtx.Error(fmt.Sprintf("Failed to get Docker API Version: %s", err))
		return false
	}
	sv, err := semver.NewVersion(ver.APIVersion)
	if err != nil {
		logFromCtx.Error(fmt.Sprintf("Failed to unmarshal Docker Version: %s", err))
		return false
	}
	constraint, _ := semver.NewConstraint(">= 1.41")
	return constraint.Check(sv)
}

func (d *DockerContainer) Clean(ctx context.Context) error {
	return d.Remove(ctx)
}
