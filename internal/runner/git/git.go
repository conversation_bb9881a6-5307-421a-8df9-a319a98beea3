package git

import (
	"context"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
	"sync"
)

// RepositoryManager 仓库管理器，提供并发安全的仓库操作
type RepositoryManager struct {
	// 锁映射表，key 为仓库 URL，value 为对应的互斥锁
	locks map[string]*sync.Mutex
	// 保护 locks map 的读写锁
	mu sync.RWMutex
}

// NewRepositoryManager 创建一个新的仓库管理器实例
func NewRepositoryManager() *RepositoryManager {
	return &RepositoryManager{
		locks: make(map[string]*sync.Mutex),
	}
}

// getLock 获取或创建指定 URL 的锁
func (s *RepositoryManager) getLock(url string) *sync.Mutex {
	// 先尝试读锁获取
	s.mu.RLock()
	if mutex, exists := s.locks[url]; exists {
		s.mu.RUnlock()
		return mutex
	}
	s.mu.RUnlock()

	// 不存在则使用写锁创建
	s.mu.Lock()
	defer s.mu.Unlock()

	// 双重检查，避免并发创建
	if mutex, exists := s.locks[url]; exists {
		return mutex
	}

	// 创建新的锁
	mutex := &sync.Mutex{}
	s.locks[url] = mutex
	return mutex
}

// Checkout 执行 Git checkout 操作
func (s *RepositoryManager) Checkout(ctx context.Context, opt agent.CheckoutOption) (*string, error) {
	// if opt.Branch is empty, use default branch
	if util.IsEmpty(opt.Branch) {
		opt.Branch = opt.DefaultBranch
	}
	// set DistDir based on CacheRepo flag
	opt.DistDir = opt.HostStepRepoPath
	if opt.CacheRepo {
		opt.DistDir = opt.HostGitRepoPath
	}

	// get logger and step context
	log := logger.GetLoggerFromCtx(ctx)
	stepContext := common.GetStepContextFromCtx(ctx)

	// get lock for the repository URL, ensuring only one process can clone or pull at a time
	mutex := s.getLock(opt.HTTPURLToRepo)
	mutex.Lock()
	defer mutex.Unlock()

	// clone or pull the repository
	hash, err := NewCommandManager(stepContext, opt, log).Exec(ctx)
	if err != nil {
		return nil, err
	}
	// copy repo to step workspace, when CacheRepo is true
	if opt.CacheRepo {
		if err = util.CopyDir(opt.HostGitRepoPath, opt.HostStepRepoPath); err != nil {
			log.Error(err.Error())
			return nil, err
		}
	}
	return hash, nil
}

// ClearAll 清理所有锁（在系统空闲时调用）
func (s *RepositoryManager) ClearAll() int {
	s.mu.Lock()
	defer s.mu.Unlock()

	count := len(s.locks)
	// 创建新的 map，旧的会被 GC 回收
	s.locks = make(map[string]*sync.Mutex)
	return count
}

// GetLockCount 获取当前锁的数量（用于监控）
func (s *RepositoryManager) GetLockCount() int {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return len(s.locks)
}

// 为了兼容性，保留全局实例和函数
var defaultRepoManager = NewRepositoryManager()

// Checkout 使用默认的仓库管理器执行 checkout 操作
// 保留此函数以兼容现有代码
func Checkout(ctx context.Context, opt agent.CheckoutOption) (*string, error) {
	return defaultRepoManager.Checkout(ctx, opt)
}

// ClearAllLocks 清理所有锁（供 poller 调用）
func ClearAllLocks() int {
	return defaultRepoManager.ClearAll()
}

// GetLockCount 获取当前锁数量
func GetLockCount() int {
	return defaultRepoManager.GetLockCount()
}
