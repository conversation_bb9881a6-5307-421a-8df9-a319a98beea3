package git

import (
	"sync"
	"testing"
	"time"
)

// TestConcurrentAccess 测试并发访问
func TestConcurrentAccess(t *testing.T) {
	manager := NewRepositoryManager()

	repoURL := "https://github.com/test/repo.git"
	concurrency := 10
	var wg sync.WaitGroup

	// 模拟多个 goroutine 同时访问同一仓库
	start := time.Now()
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			mutex := manager.getLock(repoURL)
			mutex.Lock()
			defer mutex.Unlock()

			// 模拟 Git 操作
			time.Sleep(10 * time.Millisecond)
		}(i)
	}

	wg.Wait()
	elapsed := time.Since(start)

	// 验证操作是串行的（总时间应该接近 concurrency * 10ms）
	expectedMin := time.Duration(concurrency) * 10 * time.Millisecond
	if elapsed < expectedMin {
		t.Errorf("operations completed too fast, expected at least %v, got %v", expectedMin, elapsed)
	}

	// 验证锁数量
	if count := manager.GetLockCount(); count != 1 {
		t.<PERSON><PERSON><PERSON>("expected 1 lock, got %d", count)
	}
}

// TestClearAll 测试清理所有锁
func TestClearAll(t *testing.T) {
	manager := NewRepositoryManager()

	// 创建多个锁
	urls := []string{
		"https://github.com/test/repo1.git",
		"https://github.com/test/repo2.git",
		"https://github.com/test/repo3.git",
	}

	for _, url := range urls {
		manager.getLock(url)
	}

	// 验证锁数量
	if count := manager.GetLockCount(); count != 3 {
		t.Errorf("expected 3 locks, got %d", count)
	}

	// 清理所有锁
	clearedCount := manager.ClearAll()
	if clearedCount != 3 {
		t.Errorf("expected to clear 3 locks, cleared %d", clearedCount)
	}

	// 验证锁已清理
	if count := manager.GetLockCount(); count != 0 {
		t.Errorf("expected 0 locks after clear, got %d", count)
	}
}

// TestGlobalFunctions 测试全局函数
func TestGlobalFunctions(t *testing.T) {
	// 先清理可能存在的锁
	ClearAllLocks()

	// 验证锁数量增加
	initialCount := GetLockCount()

	// 获取锁（通过内部方法）
	defaultRepoManager.getLock("https://github.com/test/global-repo.git")

	if count := GetLockCount(); count != initialCount+1 {
		t.Errorf("expected lock count to increase by 1, got %d", count-initialCount)
	}

	// 清理所有锁
	cleared := ClearAllLocks()
	if cleared == 0 {
		t.Error("expected to clear at least 1 lock")
	}

	// 验证锁已清理
	if count := GetLockCount(); count != 0 {
		t.Errorf("expected 0 locks after clear, got %d", count)
	}
}

// TestRaceCondition 测试数据竞争
func TestRaceCondition(t *testing.T) {
	manager := NewRepositoryManager()

	repoURL := "https://github.com/test/race-repo.git"

	// 并发访问同一个锁，测试并发安全性
	var wg sync.WaitGroup
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				manager.getLock(repoURL)
			}
		}()
	}

	wg.Wait()

	// 如果没有 panic 或 race condition，测试通过
	t.Log("No race condition detected")
}

// TestMultipleRepositories 测试多个仓库的并发操作
func TestMultipleRepositories(t *testing.T) {
	manager := NewRepositoryManager()

	repos := []string{
		"https://github.com/test/repo1.git",
		"https://github.com/test/repo2.git",
		"https://github.com/test/repo3.git",
	}

	var wg sync.WaitGroup

	// 每个仓库启动多个 goroutine
	for _, repo := range repos {
		for i := 0; i < 3; i++ {
			wg.Add(1)
			go func(url string, id int) {
				defer wg.Done()

				mutex := manager.getLock(url)
				mutex.Lock()
				defer mutex.Unlock()

				// 模拟操作
				time.Sleep(5 * time.Millisecond)
			}(repo, i)
		}
	}

	wg.Wait()

	// 验证创建了正确数量的锁
	if count := manager.GetLockCount(); count != len(repos) {
		t.Errorf("expected %d locks, got %d", len(repos), count)
	}
}
