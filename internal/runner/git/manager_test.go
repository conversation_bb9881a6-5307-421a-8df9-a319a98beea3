package git

import (
	"testing"
)

func TestCloneRepo(t *testing.T) {
	//required, err := CloneIfRequired0(context.Background(), plumbing.NewBranchReferenceName(""), CloneOption{
	//	URL:   "http://172.16.50.132:8088/common/Apache.git",
	//	Ref:   "main",
	//	Dir:   "/tmp/Apache",
	//	Token: "**************************",
	//})
	//fmt.Printf("required = %v\n", required)
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
}

//
//func CloneIfRequired0(ctx context.Context, refName plumbing.ReferenceName, input CloneOption) (*git.Repository, error) {
//	r, err := git.PlainOpen(input.Dir)
//	if err != nil {
//		cloneOptions := git.CloneOptions{
//			URL:      input.URL,
//			Progress: os.<PERSON>do<PERSON>,
//		}
//		if input.Token != "" {
//			cloneOptions.Auth = &http.BasicAuth{
//				Username: "token",
//				Password: input.Token,
//			}
//		}
//		r, err = git.PlainCloneContext(ctx, input.Dir, false, &cloneOptions)
//		if err != nil {
//			log.Error(fmt.Sprintf("Unable to clone %v %s: %v", input.URL, refName, err))
//			return nil, err
//		}
//		if err = os.Chmod(input.Dir, 0o755); err != nil {
//			return nil, err
//		}
//	}
//	return r, nil
//}
//
//func TestPull(t *testing.T) {
//	err := Pull(context.Background(), PullOption{
//		Dir:   "/tmp/Apache",
//		Token: "**************************",
//	})
//	if err != nil {
//		t.Error(err)
//		return
//	}
//}
//
//func TestCheckoutCommitHash(t *testing.T) {
//	commithash := "92105eb051ca10c5ddf07418daaeacc2025da965"
//	Checkout(context.Background(), CheckoutOption{
//		CommitHash: &commithash,
//		Dir:        "/tmp/Apache",
//		Token:      "**************************",
//		Branch:     nil,
//	})
//}
//
//func TestCheckoutBranch(t *testing.T) {
//	branch := "release"
//	Checkout(context.Background(), CheckoutOption{
//		Branch: &branch,
//		Dir:    "/tmp/Apache",
//		Token:  "**************************",
//	})
//}
//
//func TestCheckoutTag(t *testing.T) {
//	tag := "release-v1.0.0"
//	Checkout(context.Background(), CheckoutOption{
//		Tag:   &tag,
//		Dir:   "/tmp/Apache",
//		Token: "**************************",
//	})
//}
//
//func TestFetch(t *testing.T) {
//	err := Fetch(context.Background(), FetchOption{
//		Dir:   "/tmp/Apache",
//		Token: "**************************",
//	})
//	if err != nil {
//		t.Error(err)
//		return
//	}
//}
