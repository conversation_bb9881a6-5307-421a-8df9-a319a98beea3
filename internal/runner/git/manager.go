package git

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"pipeline/internal/runner/common"
	pkgCommon "pipeline/pkg/common"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"runtime"
	"strings"

	"pipeline/internal/runner/logger"
	"pipeline/pkg/util"
)

type CommandManager struct {
	opt             agent.CheckoutOption
	logger          *logger.Logger
	pipelineContext *pipeline.StepContext
}

func NewCommandManager(ctx *pipeline.StepContext, opt agent.CheckoutOption, log *logger.Logger) *CommandManager {
	return &CommandManager{
		opt:             opt,
		logger:          log,
		pipelineContext: ctx,
	}
}

func (c *CommandManager) Exec(ctx context.Context) (*string, error) {
	// make sure the directory exists
	if _, err := os.Stat(c.opt.DistDir); os.IsNotExist(err) {
		err = os.MkdirAll(c.opt.DistDir, 0777)
		if err != nil {
			c.logger.Errorf("Failed to create dir %s: %v", c.opt.DistDir, err)
		}
	}
	// prevent git clone from failing due to existing index.lock or shallow.lock
	indexLockPath := filepath.Join(c.opt.DistDir, "/.git/index.lock")
	if err := os.RemoveAll(indexLockPath); err != nil {
		c.logger.Errorf("Failed to remove %s: %s", indexLockPath, err)
	}
	shallowLockPath := filepath.Join(c.opt.DistDir, "/.git/shallow.lock")
	if err := os.RemoveAll(shallowLockPath); err != nil {
		c.logger.Errorf("Failed to remove %s: %s", shallowLockPath, err)
	}
	// build git clone command
	cmdList := c.buildGitCloneCommand(ctx)
	defer func() {
		cleanCmdList := c.buildCleanCommandAfterClone()
		for _, cmd := range cleanCmdList {
			// show command args
			if !cmd.DisableTrace || c.pipelineContext.Debug {
				c.logger.WriteLine(fmt.Sprintf("+ %s", strings.Join(cmd.Cmd.Args, " ")))
			}
			_ = cmd.Run()
		}
	}()
	for _, cmd := range cmdList {
		// show command args
		if !cmd.DisableTrace || c.pipelineContext.Debug {
			c.logger.WriteLine(fmt.Sprintf("+ %s", strings.Join(cmd.Cmd.Args, " ")))
		}
		if err := cmd.Run(); err != nil {
			return nil, err
		}
	}

	getCommitShaCmd := common.Command{Cmd: GetCommitSha()}
	common.SetCmdsWorkDir(c.opt.DistDir, []*common.Command{&getCommitShaCmd})

	output, err := getCommitShaCmd.Cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}
	commitSha := string(output)
	return &commitSha, nil
}

func (c *CommandManager) buildGitCloneCommand(_ context.Context) []*common.Command {
	cmdList := make([]*common.Command, 0)
	defer func() {
		common.SetCmdsWorkDir(c.opt.DistDir, cmdList)
		common.SetCmdsOutput(c.logger, c.logger, cmdList)
	}()

	cmdList = append(cmdList, &common.Command{Cmd: ShowGitVersion()}, &common.Command{Cmd: SetSafeDirectory(c.opt.DistDir)})

	if util.EmptyDir(filepath.Join(c.opt.DistDir, ".git")) {
		cmdList = append(cmdList,
			&common.Command{Cmd: InitGit()},
			&common.Command{Cmd: AddRemoteWithAuth(c.opt.HTTPURLToRepo, c.pipelineContext.Source.AccessName, c.pipelineContext.Source.AccessToken)})
	} else {
		// use set-url instead of remove + add to avoid log redundancy caused by refs redisplay
		cmdList = append(cmdList, &common.Command{Cmd: ResetRemote(c.buildRemoteUrlWithAuth()), IgnoreError: true})
	}

	if runtime.GOOS == "windows" {
		cmdList = append(cmdList, &common.Command{Cmd: SetConfig("user.email", "<EMAIL>"), DisableTrace: true})
	}

	c.logger.AddMask(c.pipelineContext.Source.AccessToken)

	// fetch the remote repository
	cmdList = append(cmdList, &common.Command{Cmd: Fetch(c.RefSpec(), c.opt.Depth)})
	// checkout the specified ref
	cmdList = append(cmdList, &common.Command{Cmd: CheckoutWithOpt(c.opt)})

	if c.opt.Lfs {
		cmdList = append(cmdList, &common.Command{Cmd: LfsPull()})
	}

	// 解决 submodule 在checkout时，当前commit不存在的子模块未清理的问题
	cmdList = append(cmdList, &common.Command{Cmd: CleanFfdx()})

	if c.opt.Submodule {
		// 解决 submodule 克隆协议不一致的问题
		cmdList = append(cmdList, &common.Command{Cmd: ConfigSshToHttps(c.opt.AccessToken, c.opt.HTTPURLToRepo)})
		cmdList = append(cmdList, &common.Command{Cmd: ShowSubmoduleStatus()})
		cmdList = append(cmdList, &common.Command{Cmd: UpdateSubmodules()})
	}

	return cmdList
}

// buildRemoteUrlWithAuth 构建带认证信息的远程仓库URL
func (c *CommandManager) buildRemoteUrlWithAuth() string {
	return strings.Replace(c.opt.HTTPURLToRepo, "https://",
		fmt.Sprintf("https://%s:%s@", c.pipelineContext.Source.AccessName, c.pipelineContext.Source.AccessToken), 1)
}

func (c *CommandManager) buildCleanCommandAfterClone() []*common.Command {
	cmdList := make([]*common.Command, 0)
	defer func() {
		common.SetCmdsWorkDir(c.opt.DistDir, cmdList)
		common.SetCmdsOutput(c.logger, c.logger, cmdList)
	}()
	cmdList = append(cmdList,
		&common.Command{Cmd: UnsetSafeDirectory(c.opt.DistDir), IgnoreError: true, DisableTrace: true},
		&common.Command{Cmd: ResetRemote(c.opt.HTTPURLToRepo), IgnoreError: true, DisableTrace: true}, // 兼容：保留不带认证信息的 remote
		&common.Command{Cmd: UnsetConfigSshToHttps(c.opt.AccessToken, c.opt.HTTPURLToRepo), IgnoreError: true, DisableTrace: true},
	)
	return cmdList
}

func (c *CommandManager) BuildCommand() (string, error) {
	return common.CmdsToString(c.buildGitCloneCommand(context.Background())), nil
}

func (c *CommandManager) RefSpec() []string {
	// if depth is 0, fetch all branches and tags
	if c.opt.Depth == 0 {
		return []string{"+refs/heads/*:refs/remotes/origin/*", "+refs/tags/*:refs/tags/*"}
	}
	// if commit hash is provided, fetch that specific commit
	if c.opt.Tag != nil && util.NotEmpty(*c.opt.Tag) {
		return []string{"+" + *c.opt.CheckoutSHA + ":" + pkgCommon.RefTags + *c.opt.Tag}
	}
	// if branch is provided, fetch that specific branch
	return []string{"+" + *c.opt.CheckoutSHA + ":refs/remotes/origin/" + util.Coalesce(c.opt.Branch, c.opt.DefaultBranch)}
}
