// nolint:gosec
package git

import (
	"fmt"
	"net/url"
	"os/exec"
	"pipeline/pkg/runner/agent"
	"strconv"
	"strings"

	"git.makeblock.com/makeblock-go/log"
)

// InitGit creates an empty git repository.
// it returns command git init
func InitGit() *exec.Cmd {
	cmd := exec.Command(
		"git",
		"init",
	)
	return cmd
}

// AddRemote adds the remote origin for the repository.
func AddRemote(remoteUrl string) *exec.Cmd {
	return exec.Command(
		"git",
		"remote",
		"add",
		"origin",
		remoteUrl,
	)
}

// AddRemoteWithAuth adds the remote origin for the repository with auth.
func AddRemoteWithAuth(remoteUrl, accessName, accessToken string) *exec.Cmd {
	newRemoteUrl := strings.Replace(remoteUrl, "https://", "https://"+accessName+":"+accessToken+"@", 1)
	return exec.Command(
		"git",
		"remote",
		"add",
		"origin",
		newRemoteUrl,
	)
}

// ResetRemote adds the remote origin for the repository.
func ResetRemote(remoteUrl string) *exec.Cmd {
	return exec.Command(
		"git",
		"remote",
		"set-url",
		"origin",
		remoteUrl,
	)
}

// ConfigAuth configures HTTP authentication for a given endpoint.
// git config --global http.https://gitlab.example.com/.extraheader "AUTHORIZATION: basic <encoded_credentials>"
// echo -n "username:password" | base64
func ConfigAuth(endpoint, authBase64 string) *exec.Cmd {
	return exec.Command(
		"git",
		"config",
		"http."+endpoint+".extraheader",
		"\"Authorization: Basic "+authBase64+"\"",
	)
}

// UnsetConfigAuth removes HTTP authentication configuration
func UnsetConfigAuth(endpoint string) *exec.Cmd {
	return exec.Command(
		"git",
		"config",
		"--unset",
		"http."+endpoint+".extraheader",
	)
}

// RemoteRemove removes the remote origin for the repository.
func RemoteRemove() *exec.Cmd {
	return exec.Command(
		"git",
		"remote",
		"remove",
		"origin",
	)
}

// CheckoutHead returns command git checkout -qf FETCH_HEAD
func CheckoutHead() *exec.Cmd {
	return exec.Command(
		"git",
		"checkout",
		"-qf",
		"FETCH_HEAD",
	)
}

// CheckoutWithOpt checkout to the commit hash, branch or tag，保证检出的是远端最新
func CheckoutWithOpt(opt agent.CheckoutOption) *exec.Cmd {
	var ref string
	if opt.CheckoutSHA != nil { // CommitHash
		ref = *opt.CheckoutSHA
	} else if opt.Tag != nil { // Tag
		ref = *opt.Tag
	} else if len(opt.Branch) > 0 { // Branch
		ref = "origin/" + opt.Branch // ensure checkout to the latest remote branch
	}
	return exec.Command("git", "checkout", "-qf", ref)
}

// Fetch fetches changes by ref, ref can be a tag, branch or pr. --depth=1 is used to limit fetching
// to the last commit from the tip of each remote branch history.
// e.g. git fetch origin +refs/heads/onboarding --depth=1 --prune
func Fetch(ref []string, depth int) *exec.Cmd {
	cmdArgs := []string{"fetch", "origin", "--no-tags", "--prune"}
	if depth > 0 {
		cmdArgs = append(cmdArgs, "--depth="+strconv.Itoa(depth))
	}
	if len(ref) > 0 {
		cmdArgs = append(cmdArgs, ref...)
	}
	return exec.Command("git", cmdArgs...)
}

// LfsPull pull LFS objects from the remote.
func LfsPull() *exec.Cmd {
	return exec.Command(
		"git",
		"lfs",
		"pull",
	)
}

// DeepenedFetch deepens the fetch history. It is similar with Fetch but accepts 500 more commit history than
// last Fetch operation by --deepen=500 option.
// e.g. git fetch origin +refs/heads/onboarding --deepen=500
func DeepenedFetch(remoteName, ref, source string) *exec.Cmd {
	cmdArgs := []string{
		"fetch",
		remoteName,
		"+" + ref, // "+" means overwrite
	}
	return exec.Command(
		"git",
		cmdArgs...,
	)
}

// ResetMerge reset last merge
// It return command git reset --merge
func ResetMerge() *exec.Cmd {
	return exec.Command(
		"git",
		"reset",
		"--merge",
	)
}

// Merge merge a branch
// e.g. git merge demo
func Merge(branch string) *exec.Cmd {
	return exec.Command(
		"git",
		"merge",
		branch,
		"--allow-unrelated-histories", // add this option to avoid "fatal: refusing to merge unrelated histories"
	)
}

// ConfigSshToHttps solve git submodule cloning permission problem
// return
// git config --global url.https://x-access-token:${GITHUB_TOKEN}@git.makeblock.com/.insteadOf \
// ssh://*********************/ && git config --global url.https://x-access-token:${GITHUB_TOKEN}@git.makeblock.com/.insteadOf \
// *********************/ && git config --global url.https://x-access-token:${GITHUB_TOKEN}@git.makeblock.com/.insteadOf \
// *********************:
func ConfigSshToHttps(accessToken, repoUrl string) *exec.Cmd {
	host := ""
	parsedURL, err := url.Parse(repoUrl)
	if err != nil {
		log.Error(fmt.Sprintf("Failed to parse repo url: %s", repoUrl))
	} else {
		host = parsedURL.Host
	}
	return exec.Command(
		"git",
		"config",
		"--global",
		"url.https://x-access-token:"+accessToken+"@"+host+"/.insteadOf",
		"git@"+host+":",
	)
	//return exec.Command(
	//	"git",
	//	"config",
	//	"--global",
	//	"url.https://x-access-token:"+accessToken+"@"+host+"/.insteadOf",
	//	"\"ssh://git@"+host+"/\"",
	//	"&&",
	//	"git",
	//	"config",
	//	"--global",
	//	"url.https://x-access-token:"+accessToken+"@"+host+"/.insteadOf",
	//	"\"git@"+host+"/\"",
	//	"&&",
	//	"git",
	//	"config",
	//	"--global",
	//	"url.https://x-access-token:"+accessToken+"@"+host+"/.insteadOf",
	//	"\"git@"+host+":\"",
	//)
}

// UnsetConfigSshToHttps unset git config --global url.https://x-access-token:${GITHUB_TOKEN}@${HOST}/.insteadOf git@${HOST}:
func UnsetConfigSshToHttps(accessToken, repoUrl string) *exec.Cmd {
	host := ""
	parsedURL, err := url.Parse(repoUrl)
	if err != nil {
		log.Error(fmt.Sprintf("Failed to parse repo url: %s", repoUrl))
	} else {
		host = parsedURL.Host
	}
	return exec.Command(
		"git",
		"config",
		"--global",
		"--unset",
		"url.https://x-access-token:"+accessToken+"@"+host+"/.insteadOf",
		"git@"+host+":",
	)
}

// UpdateSubmodules returns command: git submodule update --init --recursive
func UpdateSubmodules() *exec.Cmd {
	cmd := exec.Command(
		"git",
		"submodule",
		"update",
		"--init",
		"--recursive",
	)
	return cmd
}

// SetConfig returns command: git config --global $KEY $VA
// e.g. git config --global user.name username
func SetConfig(key, value string) *exec.Cmd {
	return exec.Command(
		"git",
		"config",
		"--local",
		key,
		value,
	)
}

func Gc() *exec.Cmd {
	return exec.Command(
		"git",
		"gc",
	)
}

// ShowLastLog returns command git --no-pager log --oneline -1
// It shows last commit messge with sha
func ShowLastLog() *exec.Cmd {
	return exec.Command(
		"git",
		"--no-pager",
		"log",
		"--oneline",
		"-1",
	)
}

// SetSafeDirectory returns command git config --global --add safe.directory $workdir
func SetSafeDirectory(workdir string) *exec.Cmd {
	return exec.Command(
		"git",
		"config",
		"--global",
		"--add",
		"safe.directory",
		workdir,
	)
}

// UnsetSafeDirectory git config --global --unset-all safe.directory $workdir
func UnsetSafeDirectory(workdir string) *exec.Cmd {
	return exec.Command(
		"git",
		"config",
		"--global",
		"--unset-all",
		"safe.directory",
		workdir,
	)
}

func ShowGitVersion() *exec.Cmd {
	return exec.Command(
		"git",
		"version",
	)
}

func ShowSubmoduleStatus() *exec.Cmd {
	return exec.Command(
		"git",
		"submodule",
		"status",
	)
}

// GetCommitSha git rev-parse HEAD
func GetCommitSha() *exec.Cmd {
	return exec.Command(
		"git",
		"rev-parse",
		"HEAD",
	)
}

func CleanFfdx() *exec.Cmd {
	return exec.Command(
		"git",
		"clean",
		"-ffdx",
	)
}
