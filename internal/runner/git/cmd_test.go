package git

import (
	"os/exec"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"reflect"
	"testing"
)

func TestInitGit(t *testing.T) {
	cmd := InitGit()

	expectedArgs := []string{"git", "init"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.<PERSON><PERSON><PERSON>("InitGit() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestAddRemoteWithAuth(t *testing.T) {
	tests := []struct {
		name        string
		remoteUrl   string
		accessName  string
		accessToken string
		want        []string
	}{
		{
			name:        "基本的HTTPS URL",
			remoteUrl:   "https://github.com/user/repo.git",
			accessName:  "username",
			accessToken: "token123",
			want:        []string{"git", "remote", "add", "origin", "https://username:<EMAIL>/user/repo.git"},
		},
		{
			name:        "空访问令牌",
			remoteUrl:   "https://gitlab.com/user/repo.git",
			accessName:  "user",
			accessToken: "",
			want:        []string{"git", "remote", "add", "origin", "https://user:@gitlab.com/user/repo.git"},
		},
		{
			name:        "特殊字符令牌",
			remoteUrl:   "https://git.makeblock.com/user/repo.git",
			accessName:  "api",
			accessToken: "abc@123#xyz",
			want:        []string{"git", "remote", "add", "origin", "*******************#<EMAIL>/user/repo.git"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := AddRemoteWithAuth(tt.remoteUrl, tt.accessName, tt.accessToken)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("AddRemoteWithAuth() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestResetRemote(t *testing.T) {
	tests := []struct {
		name      string
		remoteUrl string
		want      []string
	}{
		{
			name:      "HTTPS URL",
			remoteUrl: "https://github.com/user/repo.git",
			want:      []string{"git", "remote", "set-url", "origin", "https://github.com/user/repo.git"},
		},
		{
			name:      "SSH URL",
			remoteUrl: "**************:user/repo.git",
			want:      []string{"git", "remote", "set-url", "origin", "**************:user/repo.git"},
		},
		{
			name:      "空URL",
			remoteUrl: "",
			want:      []string{"git", "remote", "set-url", "origin", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := ResetRemote(tt.remoteUrl)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("ResetRemote() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestConfigAuth(t *testing.T) {
	tests := []struct {
		name       string
		endpoint   string
		authBase64 string
		want       []string
	}{
		{
			name:       "基本认证配置",
			endpoint:   "github.com",
			authBase64: "dXNlcjpwYXNz",
			want:       []string{"git", "config", "http.github.com.extraheader", "\"Authorization: Basic dXNlcjpwYXNz\""},
		},
		{
			name:       "空认证",
			endpoint:   "gitlab.com",
			authBase64: "",
			want:       []string{"git", "config", "http.gitlab.com.extraheader", "\"Authorization: Basic \""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := ConfigAuth(tt.endpoint, tt.authBase64)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("ConfigAuth() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestRemoteRemove(t *testing.T) {
	cmd := RemoteRemove()

	expectedArgs := []string{"git", "remote", "remove", "origin"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("RemoteRemove() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestCheckoutHead(t *testing.T) {
	cmd := CheckoutHead()

	expectedArgs := []string{"git", "checkout", "-qf", "FETCH_HEAD"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("CheckoutHead() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestCheckoutWithOpt(t *testing.T) {
	commitHash := "abc123"
	branch := "main"
	tag := "v1.0.0"

	tests := []struct {
		name   string
		option agent.CheckoutOption
		want   []string
	}{
		{
			name: "使用提交哈希",
			option: agent.CheckoutOption{
				Checkout: pipeline.Checkout{
					CheckoutSHA: &commitHash,
				},
			},
			want: []string{"git", "checkout", "-qf", "abc123"},
		},
		{
			name: "使用分支",
			option: agent.CheckoutOption{
				Checkout: pipeline.Checkout{
					Branch: branch,
				},
			},
			want: []string{"git", "checkout", "-qf", "origin/main"},
		},
		{
			name: "使用标签",
			option: agent.CheckoutOption{
				Checkout: pipeline.Checkout{
					Tag: &tag,
				},
			},
			want: []string{"git", "checkout", "-qf", "v1.0.0"},
		},
		{
			name: "同时设置多个选项时，优先级：提交哈希 > 标签 > 分支",
			option: agent.CheckoutOption{
				Checkout: pipeline.Checkout{
					CheckoutSHA: &commitHash,
					Branch:      branch,
					Tag:         &tag,
				},
			},
			want: []string{"git", "checkout", "-qf", "abc123"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := CheckoutWithOpt(tt.option)
			if cmd == nil {
				t.Errorf("CheckoutWithOpt() returned nil")
				return
			}
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("CheckoutWithOpt() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestGitFetch(t *testing.T) {
	tests := []struct {
		name  string
		ref   string
		depth int
		want  []string
	}{
		{
			name:  "ref有值，depth为正",
			ref:   "refs/heads/main",
			depth: 1,
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune", "--depth=1", "+refs/heads/main"},
		},
		{
			name:  "ref有值，depth为零",
			ref:   "refs/heads/dev",
			depth: 0,
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune", "+refs/heads/dev"},
		},
		{
			name:  "ref有值，depth为负",
			ref:   "refs/heads/feature",
			depth: -5,
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune", "+refs/heads/feature"},
		},
		{
			name:  "ref为空，depth为零",
			ref:   "",
			depth: 0,
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune"},
		},
		{
			name:  "ref为空，depth为正",
			ref:   "",
			depth: 10,
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune", "--depth=10"},
		},
		{
			name:  "ref为空，depth为负",
			ref:   "",
			depth: -10,
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune"},
		},
		{
			name:  "ref有值，depth为极大值",
			ref:   "refs/tags/v1.0.0",
			depth: 2147483647, // math.MaxInt32
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune", "--depth=2147483647", "+refs/tags/v1.0.0"},
		},
		{
			name:  "ref有值，depth为极小值",
			ref:   "refs/tags/v0.1.0",
			depth: -2147483648, // math.MinInt32
			want:  []string{"git", "fetch", "origin", "--no-tags", "--prune", "+refs/tags/v0.1.0"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var refs []string
			if tt.ref != "" {
				refs = []string{"+" + tt.ref}
			}
			cmd := Fetch(refs, tt.depth)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("Fetch(%q, %d) = %v, want %v", tt.ref, tt.depth, cmd.Args, tt.want)
			}
		})
	}
}

func TestLfsPull(t *testing.T) {
	cmd := LfsPull()

	expectedArgs := []string{"git", "lfs", "pull"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("LfsPull() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestDeepenedFetch(t *testing.T) {
	tests := []struct {
		name       string
		remoteName string
		ref        string
		source     string
		want       []string
	}{
		{
			name:       "基本的deepened fetch",
			remoteName: "origin",
			ref:        "refs/heads/main",
			source:     "main",
			want:       []string{"git", "fetch", "origin", "+refs/heads/main"},
		},
		{
			name:       "不同的远程名称",
			remoteName: "upstream",
			ref:        "refs/heads/develop",
			source:     "develop",
			want:       []string{"git", "fetch", "upstream", "+refs/heads/develop"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := DeepenedFetch(tt.remoteName, tt.ref, tt.source)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("DeepenedFetch() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestResetMerge(t *testing.T) {
	cmd := ResetMerge()

	expectedArgs := []string{"git", "reset", "--merge"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("ResetMerge() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestMerge(t *testing.T) {
	tests := []struct {
		name   string
		branch string
		want   []string
	}{
		{
			name:   "合并主分支",
			branch: "main",
			want:   []string{"git", "merge", "main", "--allow-unrelated-histories"},
		},
		{
			name:   "合并功能分支",
			branch: "feature/login",
			want:   []string{"git", "merge", "feature/login", "--allow-unrelated-histories"},
		},
		{
			name:   "空分支名",
			branch: "",
			want:   []string{"git", "merge", "", "--allow-unrelated-histories"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := Merge(tt.branch)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("Merge() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestConfigSshToHttps(t *testing.T) {
	tests := []struct {
		name        string
		accessToken string
		repoUrl     string
		want        []string
	}{
		{
			name:        "标准GitHub URL",
			accessToken: "token123",
			repoUrl:     "https://github.com/user/repo.git",
			want:        []string{"git", "config", "--global", "url.https://x-access-token:<EMAIL>/.insteadOf", "**************:"},
		},
		{
			name:        "GitLab URL",
			accessToken: "glpat-xyz",
			repoUrl:     "https://gitlab.com/user/repo.git",
			want:        []string{"git", "config", "--global", "url.https://x-access-token:<EMAIL>/.insteadOf", "**************:"},
		},
		{
			name:        "Makeblock Git URL",
			accessToken: "mb-token",
			repoUrl:     "https://git.makeblock.com/user/repo.git",
			want:        []string{"git", "config", "--global", "url.https://x-access-token:<EMAIL>/.insteadOf", "*********************:"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := ConfigSshToHttps(tt.accessToken, tt.repoUrl)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("ConfigSshToHttps() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestUnsetConfigSshToHttps(t *testing.T) {
	tests := []struct {
		name        string
		accessToken string
		repoUrl     string
		want        []string
	}{
		{
			name:        "取消设置GitHub配置",
			accessToken: "token123",
			repoUrl:     "https://github.com/user/repo.git",
			want:        []string{"git", "config", "--global", "--unset", "url.https://x-access-token:<EMAIL>/.insteadOf", "**************:"},
		},
		{
			name:        "取消设置GitLab配置",
			accessToken: "glpat-xyz",
			repoUrl:     "https://gitlab.com/user/repo.git",
			want:        []string{"git", "config", "--global", "--unset", "url.https://x-access-token:<EMAIL>/.insteadOf", "**************:"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := UnsetConfigSshToHttps(tt.accessToken, tt.repoUrl)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("UnsetConfigSshToHttps() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestUpdateSubmodules(t *testing.T) {
	cmd := UpdateSubmodules()

	expectedArgs := []string{"git", "submodule", "update", "--init", "--recursive"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("UpdateSubmodules() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestSetConfig(t *testing.T) {
	tests := []struct {
		name  string
		key   string
		value string
		want  []string
	}{
		{
			name:  "设置用户名",
			key:   "user.name",
			value: "John Doe",
			want:  []string{"git", "config", "--local", "user.name", "John Doe"},
		},
		{
			name:  "设置邮箱",
			key:   "user.email",
			value: "<EMAIL>",
			want:  []string{"git", "config", "--local", "user.email", "<EMAIL>"},
		},
		{
			name:  "空值",
			key:   "test.key",
			value: "",
			want:  []string{"git", "config", "--local", "test.key", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := SetConfig(tt.key, tt.value)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("SetConfig() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestGc(t *testing.T) {
	cmd := Gc()

	expectedArgs := []string{"git", "gc"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("Gc() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestShowLastLog(t *testing.T) {
	cmd := ShowLastLog()

	expectedArgs := []string{"git", "--no-pager", "log", "--oneline", "-1"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("ShowLastLog() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestSetSafeDirectory(t *testing.T) {
	tests := []struct {
		name    string
		workdir string
		want    []string
	}{
		{
			name:    "设置工作目录",
			workdir: "/workspace/project",
			want:    []string{"git", "config", "--global", "--add", "safe.directory", "/workspace/project"},
		},
		{
			name:    "设置相对路径",
			workdir: "./project",
			want:    []string{"git", "config", "--global", "--add", "safe.directory", "./project"},
		},
		{
			name:    "空路径",
			workdir: "",
			want:    []string{"git", "config", "--global", "--add", "safe.directory", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := SetSafeDirectory(tt.workdir)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("SetSafeDirectory() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestUnsetSafeDirectory(t *testing.T) {
	tests := []struct {
		name    string
		workdir string
		want    []string
	}{
		{
			name:    "取消设置工作目录",
			workdir: "/workspace/project",
			want:    []string{"git", "config", "--global", "--unset-all", "safe.directory", "/workspace/project"},
		},
		{
			name:    "取消设置相对路径",
			workdir: "./project",
			want:    []string{"git", "config", "--global", "--unset-all", "safe.directory", "./project"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := UnsetSafeDirectory(tt.workdir)
			if !reflect.DeepEqual(cmd.Args, tt.want) {
				t.Errorf("UnsetSafeDirectory() = %v, want %v", cmd.Args, tt.want)
			}
		})
	}
}

func TestShowGitVersion(t *testing.T) {
	cmd := ShowGitVersion()

	expectedArgs := []string{"git", "version"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("ShowGitVersion() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestShowSubmoduleStatus(t *testing.T) {
	cmd := ShowSubmoduleStatus()

	expectedArgs := []string{"git", "submodule", "status"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("ShowSubmoduleStatus() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestGetCommitSha(t *testing.T) {
	cmd := GetCommitSha()

	expectedArgs := []string{"git", "rev-parse", "HEAD"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("GetCommitSha() = %v, want %v", actualArgs, expectedArgs)
	}
}

func TestCleanFfdx(t *testing.T) {
	cmd := CleanFfdx()

	expectedArgs := []string{"git", "clean", "-ffdx"}
	actualArgs := cmd.Args

	if !reflect.DeepEqual(expectedArgs, actualArgs) {
		t.Errorf("CleanFfdx() = %v, want %v", actualArgs, expectedArgs)
	}
}

// 辅助函数测试：验证返回的命令不为nil
func TestCommandsNotNil(t *testing.T) {
	tests := []struct {
		name string
		cmd  *exec.Cmd
	}{
		{"InitGit", InitGit()},
		{"AddRemoteWithAuth", AddRemoteWithAuth("https://github.com/user/repo.git", "user", "token")},
		{"ResetRemote", ResetRemote("https://github.com/user/repo.git")},
		{"ConfigAuth", ConfigAuth("github.com", "base64auth")},
		{"RemoteRemove", RemoteRemove()},
		{"CheckoutHead", CheckoutHead()},
		{"Fetch", Fetch([]string{"refs/heads/main"}, 1)},
		{"LfsPull", LfsPull()},
		{"DeepenedFetch", DeepenedFetch("origin", "refs/heads/main", "main")},
		{"ResetMerge", ResetMerge()},
		{"Merge", Merge("main")},
		{"ConfigSshToHttps", ConfigSshToHttps("token", "https://github.com/user/repo.git")},
		{"UnsetConfigSshToHttps", UnsetConfigSshToHttps("token", "https://github.com/user/repo.git")},
		{"UpdateSubmodules", UpdateSubmodules()},
		{"SetConfig", SetConfig("user.name", "John")},
		{"Gc", Gc()},
		{"ShowLastLog", ShowLastLog()},
		{"SetSafeDirectory", SetSafeDirectory("/workspace")},
		{"UnsetSafeDirectory", UnsetSafeDirectory("/workspace")},
		{"ShowGitVersion", ShowGitVersion()},
		{"ShowSubmoduleStatus", ShowSubmoduleStatus()},
		{"GetCommitSha", GetCommitSha()},
		{"CleanFfdx", CleanFfdx()},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.cmd == nil {
				t.Errorf("%s returned nil command", tt.name)
			}
		})
	}
}

// 边界测试：测试特殊输入情况
func TestSpecialInputs(t *testing.T) {
	t.Run("URL解析错误处理", func(t *testing.T) {
		// 测试无效URL不会导致panic
		cmd := ConfigSshToHttps("token", "invalid-url")
		if cmd == nil {
			t.Error("ConfigSshToHttps should not return nil for invalid URL")
		}

		cmd = UnsetConfigSshToHttps("token", "invalid-url")
		if cmd == nil {
			t.Error("UnsetConfigSshToHttps should not return nil for invalid URL")
		}
	})

	t.Run("特殊字符处理", func(t *testing.T) {
		specialChars := "!@#$%^&*(){}[]|\\:;\"'<>?,./"
		cmd := SetConfig("test.key", specialChars)
		expected := []string{"git", "config", "--local", "test.key", specialChars}
		if !reflect.DeepEqual(cmd.Args, expected) {
			t.Errorf("SetConfig with special chars = %v, want %v", cmd.Args, expected)
		}
	})
}
