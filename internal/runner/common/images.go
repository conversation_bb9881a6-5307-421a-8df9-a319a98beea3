package common

import (
	"fmt"
)

// runtime image

func GetRuntimeImageVersion(image, version string) string {
	return fmt.Sprintf("mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-%s:%s", image, version)
}

func GetAliYunImage(version string) string {
	return GetRuntimeImageVersion("aliyun", version)
}

func GetExecStepImage(version string) string {
	return GetRuntimeImageVersion("ubuntu", version)
}

func GetJavaStepImage(version string) string {
	return GetRuntimeImageVersion("java", version)
}

func GetNodeStepImage(version string) string {
	return GetRuntimeImageVersion("node", version)
}

func GetAzureStepImage(version string) string {
	return GetRuntimeImageVersion("azure", version)
}

func GetGolangStepImage(version string) string {
	return GetRuntimeImageVersion("golang", version)
}

func GetPythonStepImage(version string) string {
	return GetRuntimeImageVersion("python", version)
}

func GetKubernetesStepImage(version string) string {
	return GetRuntimeImageVersion("kubernetes", version)
}

func GetDockerStepImage(version string) string {
	return GetRuntimeImageVersion("docker", version)
}

func GetHelmStepImage(version string) string {
	return GetRuntimeImageVersion("helm", version)
}

func GetMinioStepImage(version string) string {
	return GetRuntimeImageVersion("minio", version)
}

func GetYapiStepImage(version string) string {
	return GetRuntimeImageVersion("yapi", version)
}
