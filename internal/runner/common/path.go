package common

import (
	"path/filepath"
	"pipeline/pkg/runner/agent"
	"strconv"

	"pipeline/config"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/util"
)

const ContainerWorkspace = "/root/workspace"

func GetWorkSpace() string {
	dir, err := util.RemoveHomeDir(config.GetRunnerConfig().Workspace)
	if err != nil {
		return ""
	}
	return dir
}

func GetRepoWorkspace(source, identity string) string {
	return filepath.Join(GetWorkSpace(), "repo", source, identity)
}

// GetRunBasePath 获取运行的基础路径
func GetRunBasePath(app string, pipelineID, buildNumber int64) string {
	// 将 pipelineID 和 BuildNumber 转换为字符串
	pipelineIDStr := strconv.FormatInt(pipelineID, 10)
	buildNumberStr := strconv.FormatInt(buildNumber, 10)
	// 如果使用uuid会导致路径过长，部分操作系统会限制路径长度
	return filepath.Join(GetWorkSpace(), "run", app, pipelineIDStr, buildNumberStr)
}

func GetStepRootWorkspace(stepContext *pipeline.StepContext) string {
	basePath := GetRunBasePath(stepContext.App.Identity, stepContext.Pipeline.ID, stepContext.BuildNumber)
	return filepath.Join(basePath, stepContext.Step, stepContext.Index.GetIndexKey())
}

func GetStepWorkspace(stepContext *pipeline.StepContext) string {
	return filepath.Join(GetStepRootWorkspace(stepContext), stepContext.App.Identity)
}

func GetPipelineWorkspace(stepContext pipeline.StepContext) string {
	return GetRunBasePath(stepContext.App.Identity, stepContext.Pipeline.ID, stepContext.BuildNumber)
}

func GetArtifactWorkspace(stepContext *pipeline.StepContext) string {
	basePath := GetRunBasePath(stepContext.App.Identity, stepContext.Pipeline.ID, stepContext.BuildNumber)
	return filepath.Join(basePath, "ARTIFACT")
}

func GetPipelineCacheRootPath() string {
	return util.Coalesce(config.GetRunnerConfig().Cache.RunnerLocalCachePath, // 如果主动指定了缓存路径，则使用指定的路径
		filepath.Join(GetWorkSpace(), "cache")) // 否则使用默认的缓存路径
}

// GetPipelineCacheBasePath 获取流水线缓存基础路径
func GetPipelineCacheBasePath(stepContext pipeline.StepContext) string {
	return filepath.Join(GetPipelineCacheRootPath(), stepContext.App.Identity, stepContext.Pipeline.UUID)
}

func GetStepContainerWorkspace(identity string) string {
	return filepath.Join(ContainerWorkspace, identity)
}

func HostExecutorPath(ctx *pipeline.StepContext) string {
	return filepath.Join(GetStepRootWorkspace(ctx), ".hostexecutor")
}

// EnvsFilePath 用户写入环境变量的文件路径
func EnvsFilePath() string {
	return filepath.Join(".envs", "envs.txt")
}

// EnvsFileHostPath returns the path of the .envs file
func EnvsFileHostPath(ctx *pipeline.StepContext) string {
	return filepath.Join(GetStepRootWorkspace(ctx), EnvsFilePath())
}

// EnvsFileContainerPath returns the path of the .envs file in the container
func EnvsFileContainerPath() string {
	return filepath.Join(ContainerWorkspace, EnvsFilePath())
}

func EnvFilePath(a agent.Agent, ctx *pipeline.StepContext) string {
	path := EnvsFileContainerPath()
	switch a.Name() {
	case agent.HostAgent.String():
		path = EnvsFileHostPath(ctx)
	}
	return path
}
