package common

import (
	"context"
	"pipeline/pkg/pipeline"
)

type StepContextKeyType string

const StepContextKey = StepContextKeyType("STEP-CONTEXT-KEY")

func GetStepContextFromCtx(ctx context.Context) *pipeline.StepContext {
	val := ctx.Value(StepContextKey)
	if val != nil {
		if stepContext, ok := val.(*pipeline.StepContext); ok {
			return stepContext
		}
	}
	return nil
}

var CharsToRemove = []string{"/", ".", "_", "-"}
