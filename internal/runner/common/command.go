package common

import (
	"fmt"
	"io"
	"os/exec"
	"strings"
)

type Command struct {
	Cmd           *exec.Cmd
	BeforeRun     func(args ...any) error
	BeforeRunArgs []any
	AfterRun      func(args ...any) error
	AfterRunArgs  []any
	// DisableTrace display command args
	DisableTrace bool
	// IgnoreError ingore command run error
	IgnoreError bool
}

func (c *Command) Run() error {
	if c.BeforeRun != nil {
		if err := c.BeforeRun(c.BeforeRunArgs...); err != nil {
			if !c.IgnoreError {
				return fmt.Errorf("before execute error: %v", err)
			}
		}
	}

	err := c.Cmd.Run()
	if !c.IgnoreError && err != nil {
		return err
	}

	if c.AfterRun != nil {
		if afterErr := c.AfterRun(c.AfterRunArgs...); afterErr != nil {
			if !c.IgnoreError {
				return fmt.Errorf("after execute error: %v", afterErr)
			}
		}
	}

	return nil
}

func SetCmdsWorkDir(dir string, cmds []*Command) {
	for _, c := range cmds {
		c.Cmd.Dir = dir
	}
}

func SetCmdsOutput(stdOutput, stdErr io.Writer, cmds []*Command) {
	for _, c := range cmds {
		c.Cmd.Stdout = stdOutput
		c.Cmd.Stderr = stdErr
	}
}

func CmdsToString(cmds []*Command) string {
	var cmdsStr string
	for _, c := range cmds {
		cmdsStr += fmt.Sprintf("%s\n", strings.Join(c.Cmd.Args, " "))
	}
	return cmdsStr
}
