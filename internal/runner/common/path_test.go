package common

import (
	"os"
	"path/filepath"
	"pipeline/config"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetPipelineCacheRootPath(t *testing.T) {
	tests := []struct {
		name                    string
		envRunnerLocalCachePath string
		envWorkspace            string
		expected                string
	}{
		{
			name:                    "使用环境变量指定的缓存路径",
			envRunnerLocalCachePath: "/custom/cache/path",
			envWorkspace:            "/workspace",
			expected:                "/custom/cache/path",
		},
		{
			name:                    "使用默认的缓存路径",
			envRunnerLocalCachePath: "",
			envWorkspace:            "/workspace",
			expected:                filepath.Join("/workspace", "cache"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置环境变量
			_ = os.Setenv("RUNNER_LOCAL_CACHE_PATH", tt.envRunnerLocalCachePath)
			_ = os.Setenv("WORKSPACE", tt.envWorkspace)
			// 模拟配置加载
			config.GetRunnerConfig = func() config.RunnerConfig {
				return config.RunnerConfig{
					Workspace: tt.envWorkspace,
					Cache: config.Cache{
						RunnerLocalCachePath: tt.envRunnerLocalCachePath,
					},
				}
			}
			result := GetPipelineCacheRootPath()
			assert.Equal(t, tt.expected, result)
		})
	}
}
