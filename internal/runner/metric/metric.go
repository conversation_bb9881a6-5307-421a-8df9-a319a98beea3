package metric

import (
	v1 "pipeline/pkg/proto/runner/v1"
	"time"

	"git.makeblock.com/makeblock-go/log"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
)

func GenRunnerMetric() (*v1.RunnerMetric, error) {
	memory, err := GetMemStats()
	if err != nil {
		log.Error("get memory stats failed", log.Any("error", err))
		return nil, err
	}

	cpu, err := GetCPUStats()
	if err != nil {
		log.Error("get cpu stats failed", log.Any("error", err))
		return nil, err
	}

	disk, err := GetDiskStats()
	if err != nil {
		log.Error("get disk stats failed", log.Any("error", err))
		return nil, err
	}

	return &v1.RunnerMetric{
		Memory: memory,
		Cpu:    cpu,
		Disk:   disk,
	}, nil
}

// GetMemStats 获取内存的使用数据
func GetMemStats() (*v1.ResourceMetric, error) {
	// 获取内存的使用百分比
	memStat, err := mem.VirtualMemory()
	if err != nil {
		return nil, err
	}

	// 将内存使用量从字节转换为兆字节
	totalMB := float64(memStat.Total) / 1024 / 1024
	usedMB := float64(memStat.Used) / 1024 / 1024

	return &v1.ResourceMetric{
		Total: uint64(totalMB),
		Used:  uint64(usedMB),
	}, nil
}

// GetCPUStats 获取CPU的使用数据
func GetCPUStats() (*v1.ResourceMetric, error) {
	// 获取CPU的使用百分比
	percent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return nil, err
	}

	// 获取CPU的总数
	total, err := cpu.Counts(true)
	if err != nil {
		return nil, err
	}

	// 计算used和free
	used := float64(total) * percent[0] / 100

	return &v1.ResourceMetric{
		//nolint:gosec
		Total: uint64(total * 1000), // 转换为毫核数
		Used:  uint64(used * 1000),
	}, nil
}

// GetDiskStats 获取磁盘的使用数据
func GetDiskStats() (*v1.ResourceMetric, error) {
	// 获取磁盘的使用情况
	usage, err := disk.Usage("/")
	if err != nil {
		return nil, err
	}

	// 将磁盘使用量从字节转换为兆字节
	totalMB := float64(usage.Total) / 1024 / 1024
	usedMB := float64(usage.Used) / 1024 / 1024

	return &v1.ResourceMetric{
		Total: uint64(totalMB),
		Used:  uint64(usedMB),
	}, nil
}
