package tool

import (
	"time"

	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"

	"github.com/mitchellh/mapstructure"
)

const (
	InvalidateStep = "invalidate"
	ValidateStep   = "validate"
)

type ApproveUser struct {
	Name   string     `json:"name"`
	Status string     `json:"status"`
	Time   *time.Time `json:"time"`
}

type ApproveStepContent struct {
	*ManualValidateConfig
	ApproveUsers []ApproveUser `json:"approveUsers"`
}

func (r *ApproveStepContent) HasConfigApproveUsers() bool {
	return len(r.Reviewer) > 0
}

func (r *ApproveStepContent) UserApproveDone() bool {
	for _, user := range r.ApproveUsers {
		if user.Status != ValidateStep {
			return false
		}
	}
	return true
}

func (r *ApproveStepContent) Approve(user, status string) {
	now := time.Now()
	// not config approve users
	if len(r.ApproveUsers) == 0 {
		r.ApproveUsers = append(r.ApproveUsers, ApproveUser{
			Name:   user,
			Status: status,
			Time:   &now,
		})
		return
	}
	// config approve users
	for i, u := range r.ApproveUsers {
		if u.Name == user {
			r.ApproveUsers[i].Status = status
			r.ApproveUsers[i].Time = &now
			return
		}
	}
}

// 人工卡点节点配置

const (
	AndConditionType = "AND"
	OrConditionType  = "OR"
	ApproveUsers     = "approveUsers"
)

type ManualValidateConfig struct {
	Reviewer      []string          `json:"reviewer,omitempty"`      //审批人
	ConditionType string            `json:"conditionType,omitempty"` //条件类型： and/or
	Message       string            `json:"message,omitempty"`       //通知凭证证书
	Notices       []pipeline.Notice `json:"notices,omitempty"`       //通知凭证证书
}

func (g ManualValidateConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func (g ManualValidateConfig) GetCredentialKey() []string {
	return nil
}

func (g ManualValidateConfig) UserInApproves(user string) bool {
	for _, approve := range g.Reviewer {
		if approve == user {
			return true
		}
	}
	return false
}

func ParseTaskDiyValidateTbDefine(define map[string]any) (*ManualValidateConfig, error) {
	ret := &ManualValidateConfig{}
	err := mapstructure.Decode(&define, ret)
	if err != nil {
		return nil, err
	}
	return ret, nil
}
