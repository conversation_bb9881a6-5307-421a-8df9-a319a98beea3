package tool

// 执行命令
import (
	"context"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	YapiDocImportStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact //需要下载的构建物
		Token             string           `mapstructure:"token"`     //token
		Merge             string           `mapstructure:"merge"`     //合并策略
		Endpoint          string           `mapstructure:"endpoint"`  //endpoint
		FilePath          string           `mapstructure:"file_path"` //文件路径
	}
	// YapiDocImportStep in Golang
	YapiDocImportStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config YapiDocImportStepConfig
	}
)

func (g YapiDocImportStep) Actions() []action.Action {
	return g.Action
}

func (g YapiDocImportStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g YapiDocImportStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g YapiDocImportStepConfig) GetCredentialKey() []string {
	return nil
}

func (g YapiDocImportStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

func NewYapiDocImportStep(opt step.CreateStepOption) (*YapiDocImportStep, error) {
	builder := NewYapiDocImportStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.download,
			builder.exec,
			builder.clean,
		).Build()
}

// YapiDocImportStepBuilder builder
type YapiDocImportStepBuilder struct {
	step         *YapiDocImportStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewYapiDocImportStepBuilder 创建构建器
func NewYapiDocImportStepBuilder() *YapiDocImportStepBuilder {
	return &YapiDocImportStepBuilder{
		step: new(YapiDocImportStep),
	}
}

func (g *YapiDocImportStepBuilder) WithOpt(opt step.CreateStepOption) *YapiDocImportStepBuilder {
	g.opt = opt
	return g
}

func (g *YapiDocImportStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *YapiDocImportStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *YapiDocImportStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[YapiDocImportStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetYapiStepImage(util.Coalesce(config.Version, "1.5.0")),
		Caches: g.step.Config.Caches,
		PrepareFiles: []agent.CopyOption{{
			DestPath: common.GetStepContainerWorkspace(g.opt.StepContext.App.Identity),
			Files: []*agent.FileEntry{{
				Name: "yapi-import.json",
				Body: `{
							"type": "swagger",
							"merge": "` + config.Merge + `",
							"token": "` + config.Token + `",
							"file": "` + config.FilePath + `",
							"server": "` + config.Endpoint + `"
						}`}}}},
	}), nil
}

// 下载构建物
func (g *YapiDocImportStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 执行命令
func (g *YapiDocImportStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Env:     step.InjectEnv(g.opt), //注入环境变量
		Shell:   g.step.Config.Shell,
		Command: "yapi-cli import",
	}, "同步文档"), nil
}

// 清理工作
func (g *YapiDocImportStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *YapiDocImportStepBuilder) Build() (*YapiDocImportStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
