package factory

import (
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/build"
	"pipeline/internal/runner/step/deploy"
	"pipeline/internal/runner/step/exec"
	"pipeline/internal/runner/step/image"
	"pipeline/internal/runner/step/scan"
	"pipeline/internal/runner/step/tool"
	"pipeline/pkg/pipeline"

	"git.makeblock.com/makeblock-go/log"
	"github.com/mitchellh/mapstructure"
)

// StepConfigHub 定义StepHub，用于注册各种Step
type StepConfigHub struct {
	configCreators map[string]func(map[string]any) step.StepConfig
}

func NewStepConfigHub() *StepConfigHub {
	return &StepConfigHub{configCreators: make(map[string]func(map[string]any) step.StepConfig)}
}

func (h *StepConfigHub) Register(name string, scCreator func() step.StepConfig) {
	h.configCreators[name] = func(m map[string]any) step.StepConfig {
		sc := scCreator()
		err := mapstructure.Decode(m, &sc)
		if err != nil {
			log.ErrorE("Error decoding step config", err)
		}
		return sc
	}
}

// DefaultStepConfigFactory 创建StepFactory的实现
type DefaultStepConfigFactory struct {
	hub *StepConfigHub
}

func NewStepConfigFactory(hub *StepConfigHub) *DefaultStepConfigFactory {
	return &DefaultStepConfigFactory{hub: hub}
}

func (f *DefaultStepConfigFactory) Create(step string, config map[string]any) step.StepConfig {
	if creator, ok := f.hub.configCreators[step]; ok {
		return creator(config)
	}
	return nil
}

var stepConfigFactory *DefaultStepConfigFactory

func InitializeStepConfigFactory() {
	// 创建并注册各种Step
	hub := NewStepConfigHub()
	// build
	hub.Register(pipeline.StepJavaBuild, func() step.StepConfig { return &build.JavaBuildConfig{} })
	hub.Register(pipeline.StepNodeBuild, func() step.StepConfig { return &build.NodeBuildConfig{} })
	hub.Register(pipeline.StepGoBuild, func() step.StepConfig { return &build.GolangBuildConfig{} })
	hub.Register(pipeline.StepPythonImageBuild, func() step.StepConfig { return &build.NodeBuildConfig{} })
	hub.Register(pipeline.StepImageBuild, func() step.StepConfig { return &image.BuildImageConfig{} })
	hub.Register(pipeline.StepGoImageBuild, func() step.StepConfig { return &image.GolangImageBuildConfig{} })
	hub.Register(pipeline.StepJavaImageBuild, func() step.StepConfig { return &image.JavaImageBuildConfig{} })
	hub.Register(pipeline.StepNodeImageBuild, func() step.StepConfig { return &image.NodeImageBuildConfig{} })
	// kubernetes
	hub.Register(pipeline.StepKubernetesDeployRepo, func() step.StepConfig { return &deploy.KubernetesDeployByConfigRepoStepConfig{} })
	hub.Register(pipeline.StepKubernetesDeployPlatform, func() step.StepConfig { return &deploy.KubernetesDeployByConfigPlatformStepConfig{} })
	hub.Register(pipeline.StepKubernetesDeploy, func() step.StepConfig { return &deploy.KubernetesDeployConfig{} })
	hub.Register(pipeline.StepKubernetesArtifactBuild, func() step.StepConfig { return &build.KubernetesArtifactBuildConfig{} })
	hub.Register(pipeline.StepHelmDeploy, func() step.StepConfig { return &deploy.HelmDeployStepConfig{} })
	// cdn refresh
	hub.Register(pipeline.StepAzureCDNRefresh, func() step.StepConfig { return &deploy.AzureCDNRefreshStepConfig{} })
	hub.Register(pipeline.StepCloudflareCDNRefresh, func() step.StepConfig { return &deploy.CloudflareCDNRefreshStepConfig{} })
	hub.Register(pipeline.StepAliYunCDNRefresh, func() step.StepConfig { return &deploy.AliYunCDNRefreshStepConfig{} })
	// oss
	hub.Register(pipeline.StepAliYunOSSDeploy, func() step.StepConfig { return &deploy.AliYunOSSDeployStepConfig{} })
	hub.Register(pipeline.StepAzureBlobDeploy, func() step.StepConfig { return &deploy.AzureBlobDeployStepConfig{} })
	// scan
	hub.Register(pipeline.StepSonarQube, func() step.StepConfig { return &scan.SonarqubeConfig{} })

	hub.Register(pipeline.StepManualValidate, func() step.StepConfig { return &tool.ManualValidateConfig{} })
	hub.Register(pipeline.StepCustomImageExec, func() step.StepConfig { return &exec.CustomImageExecShellStepConfig{} })
	hub.Register(pipeline.StepHttpHealthCheck, func() step.StepConfig { return &deploy.CheckDeployHealthConfig{} })
	hub.Register(pipeline.StepHostDeploy, func() step.StepConfig { return &deploy.HostDeployConfig{} })
	hub.Register(pipeline.StepMinioOSSDeploy, func() step.StepConfig { return &deploy.MinioDeployStepConfig{} })

	hub.Register(pipeline.StepYapiDocImport, func() step.StepConfig { return &tool.YapiDocImportStepConfig{} })
	// 创建StepFactory
	stepConfigFactory = NewStepConfigFactory(hub)
}

func GetStepConfig(config map[string]any) step.StepConfig {
	step, ok := config["step"].(string)
	if ok {
		return stepConfigFactory.Create(step, config)
	}
	return nil
}
