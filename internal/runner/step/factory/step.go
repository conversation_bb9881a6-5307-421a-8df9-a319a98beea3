package factory

import (
	"fmt"
	"pipeline/internal/runner/step/image"
	"pipeline/internal/runner/step/tool"

	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/build"
	"pipeline/internal/runner/step/deploy"
	"pipeline/internal/runner/step/exec"
	"pipeline/internal/runner/step/scan"
	"pipeline/internal/runner/step/test"
	"pipeline/pkg/pipeline"
)

// StepHub 定义StepHub，用于注册各种Step
type StepHub struct {
	stepCreators map[string]func(ctx step.CreateStepOption) (step.Step, error)
}

func NewStepHub() *StepHub {
	return &StepHub{stepCreators: make(map[string]func(ctx step.CreateStepOption) (step.Step, error))}
}

func (h *StepHub) Register(name string, creator func(ctx step.CreateStepOption) (step.Step, error)) {
	h.stepCreators[name] = creator
}

// DefaultStepFactory 创建StepFactory的实现
type DefaultStepFactory struct {
	hub *StepHub
}

func NewStepFactory(hub *StepHub) *DefaultStepFactory {
	return &DefaultStepFactory{hub: hub}
}

func (f *DefaultStepFactory) Create(ctx step.CreateStepOption) (step.Step, error) {
	if creator, ok := f.hub.stepCreators[ctx.StepContext.Step]; ok {
		return creator(ctx)
	}
	return nil, fmt.Errorf("step %s not found", ctx.StepContext.Step)
}

var StepFactory *DefaultStepFactory

func Initialize() {
	// 创建并注册各种Step
	hub := NewStepHub()
	// 构建
	hub.Register(pipeline.StepKubernetesArtifactBuild, func(ctx step.CreateStepOption) (step.Step, error) { return build.NewKubernetesArtifactBuildStep(ctx) })
	hub.Register(pipeline.StepGoBuild, func(ctx step.CreateStepOption) (step.Step, error) { return build.NewGolangBuildStep(ctx) })
	hub.Register(pipeline.StepImageBuild, func(ctx step.CreateStepOption) (step.Step, error) { return image.NewBuildImageStep(ctx) })
	hub.Register(pipeline.StepGoImageBuild, func(ctx step.CreateStepOption) (step.Step, error) { return image.NewGolangImageBuildStep(ctx) })
	hub.Register(pipeline.StepNodeBuild, func(ctx step.CreateStepOption) (step.Step, error) { return build.NewNodeBuildUploadStep(ctx) })
	// 镜像构建
	hub.Register(pipeline.StepCustomImageExec, func(ctx step.CreateStepOption) (step.Step, error) { return exec.NewCustomImageExecShellStep(ctx) })
	hub.Register(pipeline.StepNodeImageBuild, func(ctx step.CreateStepOption) (step.Step, error) { return image.NewNodeImageBuildStep(ctx) })
	hub.Register(pipeline.StepPythonImageBuild, func(ctx step.CreateStepOption) (step.Step, error) { return image.NewPythonImageBuildStep(ctx) })
	hub.Register(pipeline.StepJavaImageBuild, func(ctx step.CreateStepOption) (step.Step, error) { return image.NewJavaImageBuildStep(ctx) })
	hub.Register(pipeline.StepJavaBuild, func(ctx step.CreateStepOption) (step.Step, error) { return build.NewJavaBuildStep(ctx) })
	// 质量
	hub.Register(pipeline.StepGoUnitTest, func(ctx step.CreateStepOption) (step.Step, error) { return test.NewGolangUnitTestStep(ctx) })
	hub.Register(pipeline.StepScanGo, func(ctx step.CreateStepOption) (step.Step, error) { return scan.NewGolangCodeScanStep(ctx) })
	hub.Register(pipeline.StepSonarQube, func(ctx step.CreateStepOption) (step.Step, error) { return scan.NewSonarQubeStep(ctx) })
	hub.Register(pipeline.StepJsUnitTest, func(ctx step.CreateStepOption) (step.Step, error) { return test.NewJavaScriptUnitTestStep(ctx) })
	// 部署
	hub.Register(pipeline.StepKubernetesDeploy, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewKubernetesDeployStep(ctx) })
	hub.Register(pipeline.StepKubernetesDeployRepo, func(ctx step.CreateStepOption) (step.Step, error) {
		return deploy.NewKubernetesDeployByConfigRepoStep(ctx)
	})
	hub.Register(pipeline.StepKubernetesDeployPlatform, func(ctx step.CreateStepOption) (step.Step, error) {
		return deploy.NewKubernetesDeployByConfigPlatformStep(ctx)
	})
	hub.Register(pipeline.StepHttpHealthCheck, func(ctx step.CreateStepOption) (step.Step, error) {
		return deploy.NewHttpAppDeploySuccessCheckStep(ctx)
	})
	hub.Register(pipeline.StepHostDeploy, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewHostDeployStep(ctx) })
	hub.Register(pipeline.StepHelmDeploy, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewHelmDeployStep(ctx) })
	hub.Register(pipeline.StepMinioOSSDeploy, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewMinioDeployStep(ctx) })
	// 前端
	hub.Register(pipeline.StepEsLint, func(ctx step.CreateStepOption) (step.Step, error) { return scan.NewESLintScanStep(ctx) })
	hub.Register(pipeline.StepAliYunOSSDeploy, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewAliYunOSSDeployStep(ctx) })
	hub.Register(pipeline.StepAliYunCDNRefresh, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewAliYunCDNRefreshStep(ctx) })
	hub.Register(pipeline.StepAzureBlobDeploy, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewAzureBlobDeployStep(ctx) })
	hub.Register(pipeline.StepAzureCDNRefresh, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewAzureCDNRefreshStep(ctx) })
	hub.Register(pipeline.StepExecShell, func(ctx step.CreateStepOption) (step.Step, error) { return exec.NewBuildExecShellStep(ctx) })
	hub.Register(pipeline.StepCloudflareCDNRefresh, func(ctx step.CreateStepOption) (step.Step, error) { return deploy.NewCloudflareCDNRefreshStep(ctx) })
	// 工具
	hub.Register(pipeline.StepYapiDocImport, func(ctx step.CreateStepOption) (step.Step, error) { return tool.NewYapiDocImportStep(ctx) })
	// 创建StepFactory
	StepFactory = NewStepFactory(hub)
}
