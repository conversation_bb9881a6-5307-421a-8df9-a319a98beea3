package test

import (
	"archive/tar"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"path/filepath"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/runner/artifact"
	"strconv"
	"strings"

	"pipeline/internal/runner/logger"
	"pipeline/pkg/runner/agent"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner"
	"pipeline/pkg/util"
)

//https://github.com/mochajs/mocha
//https://github.com/jestjs/jest

//package.json
//jest --passWithNoTests --json --outputFile=./test-report/report.json --coverage --timeout=30000
//https://www.npmjs.com/package/jest-html-reporter

//npm install jest-html-reporters --save-dev

/** jest.config.js
reporters: [
    "default",
    ["jest-html-reporters", {
      publicPath: "./test-report",
      filename: "report.html"
    }]
  ]
**/

// JavaScriptUnitTestStep in JavaScript
type (
	// JavaScriptUnitTestConfig in JavaScript
	JavaScriptUnitTestConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		//Commands           string
		TestReportPath     string               `json:"testReportPath" mapstructure:"testReportPath"`
		TestReportFile     string               `json:"testReportFile" mapstructure:"testReportFile"`
		CoverageReportPath string               `json:"coverageReportPath" mapstructure:"coverageReportPath"`
		CoverageReportFile string               `json:"coverageReportFile" mapstructure:"coverageReportFile"`
		NodeVersion        string               `json:"node_version" mapstructure:"node_version"`
		Checkpoints        []runner.Checkpoints `json:"checkpoints" mapstructure:"checkpoints"`
	}
	JavaScriptUnitTestStep struct {
		Config JavaScriptUnitTestConfig
		Env    []string
		Name   string
		Agent  agent.Agent
		Action []action.Action
	}
)

func (g JavaScriptUnitTestStep) Actions() []action.Action {
	return g.Action
}

func (g JavaScriptUnitTestStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g JavaScriptUnitTestStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g JavaScriptUnitTestConfig) GetCredentialKey() []string {
	return nil
}

type JavaScriptUnitTestStepBuilder struct {
	step         *JavaScriptUnitTestStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewJavaScriptUnitTestStepBuilder 创建Builder的工厂方法
func NewJavaScriptUnitTestStepBuilder() *JavaScriptUnitTestStepBuilder {
	return &JavaScriptUnitTestStepBuilder{
		step: new(JavaScriptUnitTestStep),
	}
}

func (g *JavaScriptUnitTestStepBuilder) WithOpt(opt step.CreateStepOption) *JavaScriptUnitTestStepBuilder {
	g.opt = opt
	return g
}

func (g *JavaScriptUnitTestStepBuilder) WithActionFn(fs ...func() (action.Action, error)) *JavaScriptUnitTestStepBuilder {
	g.fs = append(g.fs, fs...)
	return g
}

func (g *JavaScriptUnitTestStepBuilder) Build() (*JavaScriptUnitTestStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}

// 拆分各个构建步骤
func (g *JavaScriptUnitTestStepBuilder) prepare() (action.Action, error) {
	config, err := step.GetStepConfig[JavaScriptUnitTestConfig](g.opt)
	if err != nil {
		return nil, err
	}
	g.step.Config = config
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	g.commonAction = action.NewCommonAction(g.step.Agent)
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetNodeStepImage(config.NodeVersion),
		Caches: config.Caches,
	}), nil
}

func (g *JavaScriptUnitTestStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

func (g *JavaScriptUnitTestStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: g.step.Config.Commands,
		Env:     step.InjectEnv(g.opt),
		PATH: []string{
			fmt.Sprintf("%s/node_modules/.bin", common.GetStepContainerWorkspace(g.opt.StepContext.App.Identity)),
		},
	}, "单元测试"), nil
}

func (g *JavaScriptUnitTestStepBuilder) upload() (action.Action, error) {
	stepIdx := g.opt.StepContext.Index.GetIndexKey()
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: []agent.Artifact{
			{
				Name:  fmt.Sprintf("%s-test-report", stepIdx),
				Path:  g.step.Config.TestReportPath + "," + g.step.Config.CoverageReportPath,
				UnZip: true,
			},
		},
	}), nil
}

// 检查红线
func (g *JavaScriptUnitTestStepBuilder) threshold() (action.Action, error) {
	return action.NewDynamicAction("检查红线", g.commonAction, func(ctx context.Context, ca *action.CommonAction) error {
		config := g.step.Config
		logFromCtx := logger.GetLoggerFromCtx(ctx)
		archive, err := ca.Agent.GetArchive(ctx, agent.ArchiveOption{
			Path: filepath.Join(config.TestReportPath, "/jest-html-reporters-attach/report/result.js"),
		})
		if err != nil {
			logFromCtx.Errorf("获取单元测试报告数据失败：%s", err.Error())
			return err
		}
		//解析单元测试报告数据
		testReport, err := parseJavaScriptUnitTestReport(archive)
		if err != nil {
			logFromCtx.Errorf("解析单元测试报告数据失败：%s", err.Error())
			return err
		}

		archive, err = ca.Agent.GetArchive(ctx, agent.ArchiveOption{
			Path: filepath.Join(config.CoverageReportPath, "coverage-summary.json"),
		})
		if err != nil {
			logFromCtx.Errorf("获取覆盖率报告数据失败：%s", err.Error())
			return err
		}
		//解析覆盖率报告数据
		coverageReport, err := parseJavaScriptCoverageReport[JavaScriptCoverageReport](archive)
		if err != nil {
			logFromCtx.Errorf("解析报告信息失败：%s", err.Error())
			return err
		}
		// 组装最终报告
		report := JavaScriptTestReport{
			TotalTests:    testReport.NumTotalTests,                  //测试用例总数(成功+失败+跳过)
			SuccessTests:  testReport.NumPassedTests,                 //成功用例数（Output字段匹配前缀 --- PASS: 个数）
			FailedTests:   testReport.NumFailedTests,                 //失败的测试用例数("Action":"fail" )
			SkippedTests:  0,                                         //跳过的测试用例数("Action":"skip")
			LineCoverRate: int(coverageReport.Total.Lines.Pct.Value), //行覆盖率
		}
		//通过率(SuccessTests/测试用例总数)
		if testReport.NumTotalTests > 0 {
			report.PassRate = (testReport.NumPassedTests / testReport.NumTotalTests) * 100
		}
		// 测试报告地址
		report.ReportURL = artifact.FormatTempArtifactPath(g.opt.StepContext,
			fmt.Sprintf("%s-test-report/test-report/%s", g.opt.StepContext.Index.GetIndexKey(), config.TestReportFile))

		// 覆盖率报告地址
		report.CoverageURL = artifact.FormatTempArtifactPath(g.opt.StepContext,
			fmt.Sprintf("%s-test-report/coverage/%s", g.opt.StepContext.Index.GetIndexKey(), config.CoverageReportFile))

		//设置响应数据
		ca.Response.Content["REPORT"] = report
		logFromCtx.Infof("解析报告信息：%+v", report)

		//检查红线
		for _, checkpoint := range config.Checkpoints {
			logFromCtx.Infof("检查红线：%s %s %d", checkpoint.Key, checkpoint.RelationalOperator, checkpoint.Value)
			value := report.Mapping(checkpoint.Key)
			ok, err := util.Eval(checkpoint.RelationalOperator, value, checkpoint.Value)
			if err != nil {
				return err
			}
			if !ok {
				message := fmt.Sprintf("单元测试红线检查无法通过 %s 实际值：%d %s 预期值：%d, ",
					checkpoint.Key, value, checkpoint.LogicalOperator, checkpoint.Value)
				logFromCtx.Error(message)
				return errors.New(message)
			}
		}
		logFromCtx.Success("检查红线通过")
		return nil
	}), nil
}

// 清理工作
func (g *JavaScriptUnitTestStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// NewJavaScriptUnitTestStep 函数使用Builder模式
func NewJavaScriptUnitTestStep(opt step.CreateStepOption) (*JavaScriptUnitTestStep, error) {
	builder := NewJavaScriptUnitTestStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.threshold,
			builder.clean,
		).Build()
}

func parseJavaScriptUnitTestReport(archive io.ReadCloser) (*JavaScriptUnitTestReport, error) {
	data, err := io.ReadAll(archive)
	if err != nil {
		return nil, err
	}
	// Convert to string
	strData := string(data)

	// Find the start and end of the JSON data
	start := strings.Index(strData, "{")
	end := strings.LastIndex(strData, "}")

	// Extract the JSON string
	jsonStr := strData[start : end+1]

	// Unmarshal the JSON data
	var result JavaScriptUnitTestReport
	err = json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func parseJavaScriptCoverageReport[T any](archive io.ReadCloser) (*T, error) {
	defer func(archive io.ReadCloser) {
		_ = archive.Close()
	}(archive)
	//解析报告数据
	report := new(T)
	tarReader := tar.NewReader(archive)
	header, err := tarReader.Next() // Skip the first file (the directory)
	if err != nil {
		return nil, err
	}

	//header, err := tarReader.Next() // Get the next file (should be your JSON file)
	//if err != nil {
	//	return nil, err
	//}

	if header.Typeflag != tar.TypeReg {
		return nil, fmt.Errorf("expected a regular file, got type: %v", header.Typeflag)
	}

	data, err := io.ReadAll(tarReader)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(data, report)
	if err != nil {
		return nil, err
	}
	return report, nil
}

// JavaScriptTestReport 最后一行不计算
type JavaScriptTestReport struct {
	LineCoverRate int    `json:"lineCoverRate"` //行覆盖率
	TotalTests    int    `json:"totalTests"`    //测试用例总数
	SuccessTests  int    `json:"successTests"`  //成功用例数
	FailedTests   int    `json:"failedTests"`   //失败的测试用例数
	SkippedTests  int    `json:"skippedTests"`  //跳过的测试用例数
	PassRate      int    `json:"passRate"`      //通过率(SuccessTests/测试用例总数)
	ReportURL     string `json:"reportURL"`     //测试报告地址
	CoverageURL   string `json:"coverageURL"`   //覆盖率报告地址
}

func (r JavaScriptTestReport) Mapping(key string) int {
	switch key {
	case LineCoverageRate:
		return r.LineCoverRate
	case TestPassRate:
		return r.PassRate
	default:
		return 0
	}
}

// 读取测试报告

type JavaScriptUnitTestReport struct {
	NumFailedTestSuites       int   `json:"numFailedTestSuites"`
	NumFailedTests            int   `json:"numFailedTests"`
	NumPassedTestSuites       int   `json:"numPassedTestSuites"`
	NumPassedTests            int   `json:"numPassedTests"`
	NumPendingTestSuites      int   `json:"numPendingTestSuites"`
	NumPendingTests           int   `json:"numPendingTests"`
	NumRuntimeErrorTestSuites int   `json:"numRuntimeErrorTestSuites"`
	NumTodoTests              int   `json:"numTodoTests"`
	NumTotalTestSuites        int   `json:"numTotalTestSuites"`
	NumTotalTests             int   `json:"numTotalTests"`
	StartTime                 int64 `json:"startTime"`
	Success                   bool  `json:"success"`
	EndTime                   int64 `json:"endTime"`
}

// 读取覆盖率报告

type (
	JavaScriptCoverageReport struct {
		Total Total `json:"total"`
	}
	Lines struct {
		Total   int           `json:"total"`
		Covered int           `json:"covered"`
		Skipped int           `json:"skipped"`
		Pct     StringOrFloat `json:"pct"` // 如果没有测试用例Pct的值为Unknown, 无法转换为float64 会导致解析失败
	}
	Statements struct {
		Total   int           `json:"total"`
		Covered int           `json:"covered"`
		Skipped int           `json:"skipped"`
		Pct     StringOrFloat `json:"pct"`
	}
	Functions struct {
		Total   int           `json:"total"`
		Covered int           `json:"covered"`
		Skipped int           `json:"skipped"`
		Pct     StringOrFloat `json:"pct"`
	}
	Branches struct {
		Total   int           `json:"total"`
		Covered int           `json:"covered"`
		Skipped int           `json:"skipped"`
		Pct     StringOrFloat `json:"pct"`
	}
	BranchesTrue struct {
		Total   int           `json:"total"`
		Covered int           `json:"covered"`
		Skipped int           `json:"skipped"`
		Pct     StringOrFloat `json:"pct"`
	}
	Total struct {
		Lines        Lines        `json:"lines"`
		Statements   Statements   `json:"statements"`
		Functions    Functions    `json:"functions"`
		Branches     Branches     `json:"branches"`
		BranchesTrue BranchesTrue `json:"branchesTrue"`
	}
)

// StringOrFloat is a custom type to handle values that can be either a string or a float.
type StringOrFloat struct {
	Value float64
}

// UnmarshalJSON implements the json.Unmarshaler interface.
func (sf *StringOrFloat) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		if str == "Unknown" {
			sf.Value = 0
			return nil
		}
		val, err := strconv.ParseFloat(str, 64)
		if err != nil {
			return err
		}
		sf.Value = val
		return nil
	}
	var f float64
	if err := json.Unmarshal(data, &f); err == nil {
		sf.Value = f
		return nil
	}
	sf.Value = 0
	return nil
}
