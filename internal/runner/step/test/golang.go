package test

import (
	"archive/tar"
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"path/filepath"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/runner/artifact"
	"strings"
	"time"

	"pipeline/internal/runner/logger"
	"pipeline/pkg/runner/agent"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner"
	"pipeline/pkg/util"
)

type (
	// GolangUnitTestConfig in Golang
	GolangUnitTestConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Path              string
		File              string
		Index             string
		GoVersion         string               `json:"go_version" mapstructure:"go_version"`
		Checkpoints       []runner.Checkpoints `json:"checkpoints" mapstructure:"checkpoints"`
	}
	// GolangUnitTestStep in Golang
	GolangUnitTestStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config GolangUnitTestConfig
	}
)

func (g GolangUnitTestStep) Actions() []action.Action {
	return g.Action
}

func (g GolangUnitTestStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g GolangUnitTestStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g GolangUnitTestConfig) GetCredentialKey() []string {
	return nil
}

// GolangUnitTestStepBuilder builder
type GolangUnitTestStepBuilder struct {
	step         *GolangUnitTestStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewGolangUnitTestStepBuilder 创建构建器
func NewGolangUnitTestStepBuilder() *GolangUnitTestStepBuilder {
	return &GolangUnitTestStepBuilder{
		step: new(GolangUnitTestStep),
	}
}

func (g *GolangUnitTestStepBuilder) WithOpt(opt step.CreateStepOption) *GolangUnitTestStepBuilder {
	g.opt = opt
	return g
}

func (g *GolangUnitTestStepBuilder) WithActionFn(fs ...func() (action.Action, error)) *GolangUnitTestStepBuilder {
	g.fs = append(g.fs, fs...)
	return g
}

// Build 返回完整step
func (g *GolangUnitTestStepBuilder) Build() (*GolangUnitTestStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}

// 准备环境
func (g *GolangUnitTestStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[GolangUnitTestConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetGolangStepImage(g.step.Config.GoVersion),
		Caches: g.step.Config.Caches,
	}), nil
}

// 克隆代码
func (g *GolangUnitTestStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *GolangUnitTestStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: fmt.Sprintf("%s\n%s", step.BuildGitInsteadOfCmd(g.opt), g.step.Config.Commands),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "单元测试"), nil
}

// 上传构建物
func (g *GolangUnitTestStepBuilder) upload() (action.Action, error) {
	config := g.step.Config
	stepIdx := g.opt.StepContext.Index.GetIndexKey()
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: []agent.Artifact{
			{
				Name:  fmt.Sprintf("%s-report.jsonl", stepIdx), // 测试报告
				Path:  filepath.Join(config.Path, config.File),
				UnZip: true,
			},
			{
				Name:  fmt.Sprintf("%s-index.html", stepIdx), // 覆盖率报告
				Path:  filepath.Join(config.Path, config.Index),
				UnZip: true,
			},
		},
	}), nil
}

// 清理工作
func (g *GolangUnitTestStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// 检查红线
func (g *GolangUnitTestStepBuilder) threshold() (action.Action, error) {
	return action.NewDynamicAction("检查红线", g.commonAction,
		func(ctx context.Context, ca *action.CommonAction) error {
			config := g.step.Config
			archive, err := ca.Agent.GetArchive(ctx, agent.ArchiveOption{
				Path: filepath.Join(config.Path, config.File),
			})
			if err != nil {
				return err
			}
			logFromCtx := logger.GetLoggerFromCtx(ctx)
			//解析报告数据
			report, err := parseGolangTestReport(archive)
			if err != nil {
				logFromCtx.Errorf("解析报告信息失败：%s", err.Error())
				return err
			}

			// 测试报告地址
			report.ReportURL = artifact.FormatTempArtifactPath(g.opt.StepContext,
				fmt.Sprintf("%s-report.jsonl/report.jsonl", g.opt.StepContext.Index.GetIndexKey()))

			// 覆盖率报告地址
			report.CoverageURL = artifact.FormatTempArtifactPath(g.opt.StepContext,
				fmt.Sprintf("%s-index.html/index.html", g.opt.StepContext.Index.GetIndexKey()))

			//设置响应数据
			ca.Response.Content["REPORT"] = report
			logFromCtx.Infof("解析报告信息：%+v", report)

			//检查红线
			for _, checkpoint := range config.Checkpoints {
				logFromCtx.Infof("检查红线：%s %s %d",
					checkpoint.Key, checkpoint.RelationalOperator, checkpoint.Value)
				value := report.Mapping(checkpoint.Key)
				ok, err := util.Eval(checkpoint.RelationalOperator, value, checkpoint.Value)
				if err != nil {
					return err
				}
				if !ok {
					message := fmt.Sprintf("单元测试红线检查无法通过 %s 实际值：%d %s 预期值：%d, ",
						checkpoint.Key, value, checkpoint.LogicalOperator, checkpoint.Value)
					logFromCtx.Error(message)
					return errors.New(message)
				}
			}
			logFromCtx.Success("检查红线通过")

			return nil
		}), nil
}

func NewGolangUnitTestStep(opt step.CreateStepOption) (*GolangUnitTestStep, error) {
	builder := NewGolangUnitTestStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.threshold,
			builder.clean,
		).Build()
}

// GolangTestReport 最后一行不计算
type GolangTestReport struct {
	LineCoverRate int    `json:"lineCoverRate"` //行覆盖率（report.jsonl中最后一个overage: 79.3% 读取）
	TotalTests    int    `json:"totalTests"`    //测试用例总数(成功+失败+跳过)
	SuccessTests  int    `json:"successTests"`  //成功用例数（Output字段匹配前缀 --- PASS: 个数）
	FailedTests   int    `json:"failedTests"`   //失败的测试用例数("Action":"fail" )
	SkippedTests  int    `json:"skippedTests"`  //跳过的测试用例数("Action":"skip")
	PassRate      int    `json:"passRate"`      //通过率(SuccessTests/测试用例总数)
	ReportURL     string `json:"reportURL"`     //测试报告地址
	CoverageURL   string `json:"coverageURL"`   //覆盖率报告地址
}

const (
	LineCoverageRate = "LineCoverageRate"
	TestPassRate     = "TestPassRate"
)

func (r GolangTestReport) Mapping(key string) int {
	switch key {
	case LineCoverageRate:
		return r.LineCoverRate
	case TestPassRate:
		return r.PassRate
	default:
		return 0
	}
}

func parseGolangTestReport(rc io.ReadCloser) (*GolangTestReport, error) {
	defer func(rc io.ReadCloser) {
		_ = rc.Close()
	}(rc)
	// Create a new tar reader
	tr := tar.NewReader(rc)
	// Read the first (and only) file from the tar archive
	_, err := tr.Next()
	if err != nil {
		return nil, err
	}
	// Read the file content

	//Create a new Scanner for the file
	scanner := bufio.NewScanner(tr)

	reportIssues := GolangTestReport{}

	type ReportModel struct {
		Time    time.Time `json:"Time"`
		Action  string    `json:"Action"`
		Package string    `json:"Package"`
		Test    string    `json:"Test"`
		Output  string    `json:"Output"`
	}

	for scanner.Scan() {
		line := scanner.Text()
		var test ReportModel
		err := json.Unmarshal([]byte(line), &test)
		if err != nil {
			return nil, err
		}

		switch test.Action {
		case "fail":
			reportIssues.FailedTests++
		case "skip":
			reportIssues.SkippedTests++
		}
		if strings.Contains(test.Output, "--- PASS") {
			reportIssues.SuccessTests++
		}
		//line coverage
		if strings.Contains(test.Output, "coverage:") {
			var coverage float64
			_, err = fmt.Sscanf(test.Output, "coverage: %f%% of statements", &coverage)
			if err == nil {
				reportIssues.LineCoverRate = int(coverage)
			}
		}
	}
	//total = success + failed + skipped
	reportIssues.TotalTests = reportIssues.SuccessTests + reportIssues.FailedTests + reportIssues.SkippedTests

	//rate
	if reportIssues.TotalTests > 0 {
		reportIssues.PassRate = int(float64(reportIssues.SuccessTests) / float64(reportIssues.TotalTests) * 100)
	}

	return &reportIssues, nil
}
