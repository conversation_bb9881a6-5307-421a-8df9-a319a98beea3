package step

import (
	"context"
	"fmt"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"strings"

	"github.com/mitchellh/mapstructure"
)

type CreateStepOption struct {
	StepContext *pipeline.StepContext
	Logger      *logger.Logger
}

type CommonConfig struct {
	Run      pipeline.Run      `mapstructure:"run"`
	Caches   []string          `mapstructure:"caches"`
	Notices  []pipeline.Notice `mapstructure:"notices"`
	Strategy pipeline.Strategy `mapstructure:"strategy"`
	// step version(image version)
	Version string `mapstructure:"version"`
	// shell
	Shell    string `mapstructure:"shell"`
	Commands string `mapstructure:"commands"`
}

// Step 编排actions
type Step interface {
	Actions() []action.Action           //获取所有的Action
	CommonConfig() *CommonConfig        //获取公共配置用于重试,发送通知等
	Finalize(ctx context.Context) error //析构函数，执行step完成释放资源
}

type StepConfig interface {
	GetCredentialKey() []string
	GetDownloadArtifacts() []agent.Artifact
}

type StepConfigPlugin interface {
	GetConfigPluginKey() []string
}

func InjectEnv(opt CreateStepOption) map[string]string {
	envMap := make(map[string]string)
	// 注入环境变量
	for k, v := range opt.StepContext.Env {
		envMap[k] = v
	}
	return envMap
}

func GetStepConfig[T any](opt CreateStepOption) (T, error) {
	var config T
	err := mapstructure.Decode(opt.StepContext.StepDefine, &config)
	return config, err
}

func NewDefaultCheckoutAction(ca *action.CommonAction, opt CreateStepOption) *action.CheckoutAction {
	return action.NewCheckoutAction(ca, agent.CheckoutOption{
		// checkout
		Checkout: opt.StepContext.Checkout,
		// source
		AccessToken: opt.StepContext.Source.AccessToken,
		// url
		SSHURLToRepo:  opt.StepContext.App.SSHURLToRepo,
		HTTPURLToRepo: opt.StepContext.App.HTTPURLToRepo,
		// path
		HostStepRepoPath: common.GetStepWorkspace(opt.StepContext),
		HostGitRepoPath:  common.GetRepoWorkspace(opt.StepContext.Source.Name, opt.StepContext.App.Identity),
	})
}

func BuildGitInsteadOfCmd(opt CreateStepOption) string {
	sb := strings.Builder{}
	accessToken := opt.StepContext.Source.AccessToken
	accessNameWithAccessToken := fmt.Sprintf("%s:%s", opt.StepContext.Source.AccessName, accessToken)
	opt.Logger.AddMask(accessToken)
	sb.WriteString(fmt.Sprintf("git config --global url.\"https://%<EMAIL>\".insteadOf \"https://git.makeblock.com\"\n", accessNameWithAccessToken))
	return sb.String()
}
