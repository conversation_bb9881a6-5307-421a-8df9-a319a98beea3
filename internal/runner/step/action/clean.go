package action

import (
	"context"
	"errors"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/util"
	"time"

	"github.com/avast/retry-go"
)

const CleanActionName = "清理工作"

// CleanAction in Golang
type CleanAction struct {
	Common   *CommonAction
	Path     []string
	executor pipeline.Executor
}

func NewCleanAction(common *CommonAction) *CleanAction {
	return &CleanAction{Common: common}
}

func (c *CleanAction) Name() string {
	return CleanActionName
}

func (c *CleanAction) Fire(ctx context.Context) (FireActionResponse, error) {
	response := NewDefaultActionResponse()
	//execute step custom clean executor
	if c.executor != nil {
		err := c.executor(ctx)
		if err != nil {
			response.Status = pipeline.Failed
			return response, err
		}
	}
	sc := common.GetStepContextFromCtx(ctx)
	// check if is debug mode
	if sc.Debug {
		al := logger.GetLoggerFromCtx(ctx)
		al.Info("Debug mode, skip clean action")
		return response, nil // TODO: 这里直接返回会导致主机缓存不更新, 待优化
	}
	//agent clean
	err := c.Common.Agent.Clean(ctx)
	if err != nil {
		if errors.Is(ctx.Err(), context.Canceled) {
			response.Status = pipeline.Cancel
		} else {
			response.Status = pipeline.Failed
		}
		return response, err
	}
	//清理宿主机工作空间
	stepRootWorkspace := common.GetStepRootWorkspace(sc)
	err = retry.Do(func() error {
		return util.DeleteFolder(stepRootWorkspace)
	},
		retry.Attempts(5),
		retry.Delay(time.Second),
		retry.DelayType(retry.FixedDelay))

	if err != nil {
		logger.GetLoggerFromCtx(ctx).Error(err.Error())
		response.Status = pipeline.Failed
		return response, err
	}
	return response, nil
}
