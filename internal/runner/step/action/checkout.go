package action

import (
	"context"
	"errors"

	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
)

type CheckoutAction struct {
	Common *CommonAction
	opt    agent.CheckoutOption
}

func (c *CheckoutAction) Name() string {
	return "克隆代码"
}

func NewCheckoutAction(ca *CommonAction, opt agent.CheckoutOption) *CheckoutAction {
	return &CheckoutAction{
		Common: ca,
		opt:    opt,
	}
}

func (c *CheckoutAction) Fire(ctx context.Context) (FireActionResponse, error) {
	response := NewDefaultActionResponse()
	err := c.Common.Agent.Checkout(ctx, c.opt)
	if err != nil {
		if errors.Is(ctx.Err(), context.Canceled) {
			response.Status = pipeline.Cancel
		} else {
			response.Status = pipeline.Failed
		}
		return response, err
	}
	return response, nil
}
