package action

//
//import (
//	"context"
//	"errors"
//
//	"pipeline/pkg/pipeline"
//	"pipeline/pkg/runner/agent"
//)
//
//type CacheDownloadAction struct {
//	Common *CommonAction
//	opt    agent.CachesOption
//}
//
//func (c *CacheDownloadAction) Name() string {
//	return "下载缓存"
//}
//
//func NewCacheDownloadAction(ca *CommonAction, opt agent.CachesOption) *CacheDownloadAction {
//	return &CacheDownloadAction{
//		Common: ca,
//		opt:    opt,
//	}
//}
//
//func (c *CacheDownloadAction) Fire(ctx context.Context) (FireActionResponse, error) {
//	response := NewDefaultActionResponse()
//	err := c.Common.Agent.DownloadCache(ctx, c.opt)
//	if err != nil {
//		if errors.Is(ctx.Err(), context.Canceled) {
//			response.Status = pipeline.Cancel
//		} else {
//			response.Status = pipeline.Failed
//		}
//		return response, err
//	}
//	return response, nil
//}
//
//type CacheUploadAction struct {
//	Common *CommonAction
//	opt    agent.CachesOption
//}
//
//func (c *CacheUploadAction) Name() string {
//	return "上传缓存"
//}
//
//func NewCacheUploadAction(ca *CommonAction, opt agent.CachesOption) *CacheUploadAction {
//	return &CacheUploadAction{
//		Common: ca,
//		opt:    opt,
//	}
//}
//
//func (c *CacheUploadAction) Fire(ctx context.Context) (FireActionResponse, error) {
//	response := NewDefaultActionResponse()
//	err := c.Common.Agent.UploadCache(ctx, c.opt)
//	if err != nil {
//		if errors.Is(ctx.Err(), context.Canceled) {
//			response.Status = pipeline.Cancel
//		} else {
//			response.Status = pipeline.Failed
//		}
//		return response, err
//	}
//	return response, nil
//}
