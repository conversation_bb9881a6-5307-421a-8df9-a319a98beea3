package action

import (
	"context"
	"errors"

	"pipeline/pkg/pipeline"
)

type DynamicAction struct {
	name   string
	common *CommonAction
	do     func(context.Context, *CommonAction) error
}

func (c *DynamicAction) Name() string {
	return c.name
}

func NewDynamicAction(name string, ca *CommonAction, f func(context.Context, *CommonAction) error) *DynamicAction {
	return &DynamicAction{
		common: ca,
		name:   name,
		do:     f,
	}
}

func (c *DynamicAction) Fire(ctx context.Context) (FireActionResponse, error) {
	//执行
	err := c.do(ctx, c.common)
	//结果数据
	if err != nil {
		if errors.Is(ctx.Err(), context.Canceled) {
			c.common.Response.Status = pipeline.Cancel
		} else {
			c.common.Response.Status = pipeline.Failed
		}
	}
	// 响应
	return c.common.Response, err
}
