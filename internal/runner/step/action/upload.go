package action

import (
	"context"
	"errors"

	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
)

type UploadArtifactAction struct {
	*CommonAction
	opt agent.UploadOption
}

func (u *UploadArtifactAction) Name() string {
	return "产物上传"
}

func NewUploadArtifactAction(ca *CommonAction, opt agent.UploadOption) *UploadArtifactAction {
	return &UploadArtifactAction{
		CommonAction: ca,
		opt:          opt,
	}
}

func (u *UploadArtifactAction) Fire(ctx context.Context) (FireActionResponse, error) {
	// 默认响应
	ret := NewDefaultActionResponse()
	// 上传产物
	response, err := u.Agent.Upload(ctx, u.opt)
	// 错误处理
	if err != nil {
		// 取消执行
		if errors.Is(ctx.Err(), context.Canceled) {
			ret.Status = pipeline.Cancel
		} else {
			ret.Status = pipeline.Failed
		}
	}
	// 设置响应
	if response != nil {
		ret.Content = response.Content
	}
	// 响应结果
	return ret, err
}
