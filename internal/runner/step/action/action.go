package action

import (
	"context"

	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
)

type FireActionResponse struct {
	Status  string         `json:"status"`
	Message string         `json:"message"`
	Content map[string]any //结果内容
}

type CommonAction struct {
	Agent    agent.Agent
	Response FireActionResponse
}

func NewCommonAction(agent agent.Agent) *CommonAction {
	return &CommonAction{Agent: agent, Response: NewDefaultActionResponse()}
}

type Action interface {
	Name() string
	Fire(ctx context.Context) (FireActionResponse, error)
}

type ExtendAction interface {
	SetName(string)
}

func NewDefaultActionResponse() FireActionResponse {
	return FireActionResponse{
		Status:  pipeline.Success,
		Content: make(map[string]any),
	}
}

func (r *FireActionResponse) Fail(err error) {
	r.Status = pipeline.Failed
	r.Message = err.Error()
}
