package action

import (
	"context"
	"errors"
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"github.com/avast/retry-go"
	"pipeline/internal/runner/common"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
)

type ExecAction struct {
	name string
	*CommonAction
	opt agent.ExecOption
}

func NewExecActionWithName(ca *CommonAction, opt agent.ExecOption, name string) *ExecAction {
	return &ExecAction{
		CommonAction: ca,
		opt:          opt,
		name:         name,
	}
}

func (e *ExecAction) Name() string {
	return e.name
}

func (e *ExecAction) Fire(ctx context.Context) (FireActionResponse, error) {

	response := NewDefaultActionResponse()
	// 执行命令
	err := e.Agent.Exec(ctx, e.opt)
	// 如果执行命令失败，返回失败状态
	if err != nil {
		if errors.Is(ctx.Err(), context.Canceled) {
			response.Status = pipeline.Cancel
		} else {
			response.Status = pipeline.Failed
		}
		return response, err
	}
	// 在执行完命令后，尝试读取设置的环境变量（echo "action_state=yellow" >> "$PIPELINE_ENV"）
	e.outputEnv(ctx, &response)

	return response, nil
}

func (e *ExecAction) outputEnv(ctx context.Context, response *FireActionResponse) {
	var envs = map[string]string{}
	stepCtx := common.GetStepContextFromCtx(ctx)
	opt := agent.ArchiveOption{
		Path:     common.EnvFilePath(e.Agent, stepCtx),
		Absolute: true,
	}
	// parse env file, use retry to retry the operation, because the network may be unstable
	if err := retry.Do(func() error {
		return agent.ParseEnvFile(ctx, e.Agent, opt, envs)
	}, retry.Attempts(3)); err != nil {
		log.ErrorE("parse env file error", err)
	}
	// 限制每个环境变量的长度
	envMaps := make(map[string]string)
	for k, v := range envs {
		// The key can be a maximum of 255 characters
		if len(k) > 255 {
			log.Error(fmt.Sprintf(
				"ignore the output env of task %s because the key is too long: %q", stepCtx.TxUUID, k))
			continue
		}
		// The value can be a maximum of 1 MB
		if l := len(v); l > 1024*1024 {
			log.Warn(fmt.Sprintf(
				"ignore the output env %q of task %s because the value is too long: %v", k, stepCtx.TxUUID, l))
			continue
		}
		envMaps[k] = v
	}
	// 返回给服务端
	if len(envMaps) > 0 {
		response.Content[pipeline.EnvPipelineKey] = envMaps
		// inject env to step context, for render the artifact name or other
		stepCtx.Env = util.MergeMap(stepCtx.Env, envMaps)
	}
}
