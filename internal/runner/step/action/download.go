package action

import (
	"context"
	"errors"

	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
)

//下载资源

type DownloadAction struct {
	*CommonAction
	opt agent.DownloadOption
}

func (u *DownloadAction) Name() string {
	return "产物下载"
}

func NewDownloadAction(ca *CommonAction, opt agent.DownloadOption) *DownloadAction {
	return &DownloadAction{
		CommonAction: ca,
		opt:          opt,
	}
}

func (u *DownloadAction) Fire(ctx context.Context) (FireActionResponse, error) {
	response := NewDefaultActionResponse()
	err := u.Agent.Download(ctx, u.opt)
	if err != nil {
		if errors.Is(ctx.Err(), context.Canceled) {
			response.Status = pipeline.Cancel
		} else {
			response.Status = pipeline.Failed
		}
		return response, err
	}
	return response, nil
}
