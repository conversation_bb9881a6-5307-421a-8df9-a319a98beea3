package action

import (
	"context"
	"errors"
	"path/filepath"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
	"pipeline/pkg/version"
)

type PrepareAction struct {
	*CommonAction
	//配置
	opt agent.PrepareOption
}

func NewPrepareAction(ca *CommonAction, opt agent.PrepareOption) *PrepareAction {
	return &PrepareAction{
		CommonAction: ca,
		opt:          opt,
	}
}

func (r *PrepareAction) Name() string {
	return "申请环境"
}

func (r *PrepareAction) Fire(ctx context.Context) (FireActionResponse, error) {
	response := NewDefaultActionResponse()
	// 初始化公共操作
	if err := r.Init(ctx); err != nil {
		response.Fail(err)
		return response, err
	}
	// 准备环境
	err := r.Agent.Prepare(ctx, r.opt)
	if err != nil {
		if errors.Is(ctx.Err(), context.Canceled) {
			response.Status = pipeline.Cancel
		} else {
			response.Status = pipeline.Failed
		}
		return response, err
	}
	return response, nil
}

func (r *PrepareAction) Init(ctx context.Context) error {

	// 获取上下文
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	stepContext := common.GetStepContextFromCtx(ctx)

	// 打印日志
	logFromCtx.Infof("tx: %s", stepContext.TxUUID)
	logFromCtx.Infof("run-on: %s", ctx.Value(runner.RUNNER_NAME))
	logFromCtx.Info(version.RunnerVersionInfoRaw())

	// 写入必要的环境变量
	r.opt.Env = util.EnsureMapInit(r.opt.Env)
	r.opt.Env["CI"] = "true"
	r.opt.Env["TX_UUID"] = stepContext.TxUUID

	// 注入环境变量接收地址
	envFilePath := common.EnvFilePath(r.Agent, stepContext)
	r.opt.Env[pipeline.EnvPipelineKey] = envFilePath

	// 创建env文件
	r.opt.PrepareFiles = append(r.opt.PrepareFiles, agent.CopyOption{
		Files: []*agent.FileEntry{{
			Mode: 0o644,
			Name: "envs.txt",
		}},
		DestPath: filepath.Dir(envFilePath),
	})

	return nil
}
