package deploy

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	HelmDeployStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact // 需要上传的构建物
		Credential        string           `json:"credential"` // k8s凭证
		Helm              string           `json:"helm"`       // Helm版本
		Timeout           int              `json:"timeout" mapstructure:"timeout"`
	}
	HelmDeployStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config HelmDeployStepConfig
	}
)

func (g HelmDeployStep) Actions() []action.Action {
	return g.Action
}

func (g HelmDeployStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g HelmDeployStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g HelmDeployStepConfig) GetCredentialKey() []string {
	return []string{g.Credential}
}

func (g HelmDeployStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

func NewHelmDeployStep(opt step.CreateStepOption) (*HelmDeployStep, error) {
	builder := NewHelmDeployStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.download,
			builder.exec,
			builder.clean,
		).Build()
}

// HelmDeployStepBuilder builder
type HelmDeployStepBuilder struct {
	step         *HelmDeployStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewHelmDeployStepBuilder 创建构建器
func NewHelmDeployStepBuilder() *HelmDeployStepBuilder {
	return &HelmDeployStepBuilder{
		step: new(HelmDeployStep),
	}
}

func (g *HelmDeployStepBuilder) WithOpt(opt step.CreateStepOption) *HelmDeployStepBuilder {
	g.opt = opt
	return g
}

func (g *HelmDeployStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *HelmDeployStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *HelmDeployStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[HelmDeployStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 获取秘钥
	cert := g.opt.StepContext.Credentials[config.Credential]
	if cert == nil {
		return nil, fmt.Errorf("k8s credential not found, credential: %s", config.Credential)
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetHelmStepImage(g.step.Config.Helm),
		Caches: g.step.Config.Caches,
		PrepareFiles: []agent.CopyOption{
			{
				DestPath: "/root/.kube/",
				Files: []*agent.FileEntry{
					{
						Name: "config",
						Body: cert.Value,
						Mode: 0o755,
					},
				},
			}},
	}), nil
}

// 下载制品
func (g *HelmDeployStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 执行命令
func (g *HelmDeployStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: g.step.Config.Commands,
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "执行部署"), nil
}

// 清理工作
func (g *HelmDeployStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *HelmDeployStepBuilder) Build() (*HelmDeployStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
