package deploy

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	KubernetesDeployConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact // 需要下载的构建物
		Credential        string           // docker凭证
		Kubectl           string           // kubectl 版本
		YamlPath          string           // yaml 路径
		Namespace         string           // 命名空间
		// for status check
		Deployment string `json:"deployment" mapstructure:"deployment"`
		Timeout    int    `json:"timeout" mapstructure:"timeout"`
	}
	// KubernetesDeployStep in Golang
	KubernetesDeployStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config KubernetesDeployConfig
	}
)

func (g KubernetesDeployStep) Actions() []action.Action {
	return g.Action
}

func (g KubernetesDeployStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g KubernetesDeployStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g KubernetesDeployConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g KubernetesDeployConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

func NewKubernetesDeployStep(opt step.CreateStepOption) (*KubernetesDeployStep, error) {
	builder := NewKubernetesDeployStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.download,
			builder.exec,
			builder.clean,
		).Build()
}

// KubernetesDeployStepBuilder builder
type KubernetesDeployStepBuilder struct {
	step         *KubernetesDeployStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewKubernetesDeployStepBuilder 创建构建器
func NewKubernetesDeployStepBuilder() *KubernetesDeployStepBuilder {
	return &KubernetesDeployStepBuilder{
		step: new(KubernetesDeployStep),
	}
}

func (g *KubernetesDeployStepBuilder) WithOpt(opt step.CreateStepOption) *KubernetesDeployStepBuilder {
	g.opt = opt
	return g
}

func (g *KubernetesDeployStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *KubernetesDeployStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *KubernetesDeployStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[KubernetesDeployConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// 获取秘钥
	cert := g.opt.StepContext.Credentials[config.Credential]
	if cert == nil {
		return nil, fmt.Errorf("k8s credential not found, credential: %s", config.Credential)
	}
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetKubernetesStepImage(g.step.Config.Kubectl),
		Caches: g.step.Config.Caches,
		PrepareFiles: []agent.CopyOption{
			{
				DestPath: "/root/.kube/",
				Files: []*agent.FileEntry{
					{
						Name: "config",
						Body: cert.Value,
						Mode: 0o755,
					},
				},
			},
		},
	}), nil
}

func (g *KubernetesDeployStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

func buildKubernetesDeployCommand(config KubernetesDeployConfig) string {
	sb := strings.Builder{}
	sb.WriteString("kubectl version\n")
	sb.WriteString(fmt.Sprintf("kubectl apply -f %s\n", util.SanitizeInput(config.YamlPath)))
	// rollout status deployment/xxx -n xxx --timeout=xxx
	if config.Namespace != "" && config.Deployment != "" {
		// timeout limit
		if config.Timeout <= 0 {
			config.Timeout = 60
		}
		// prevent shell injection
		namespace := util.SanitizeInput(config.Namespace)
		deployment := util.SanitizeInput(config.Deployment)
		// build command
		sb.WriteString(fmt.Sprintf("kubectl rollout status deployment/%s -n %s --timeout=%ds\n", deployment, namespace, config.Timeout))
	}
	return sb.String()
}

// 执行命令
func (g *KubernetesDeployStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Env:     step.InjectEnv(g.opt), //注入环境变量
		Shell:   g.step.Config.Shell,
		Command: buildKubernetesDeployCommand(g.step.Config),
	}, "发布应用"), nil
}

// 清理工作
func (g *KubernetesDeployStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *KubernetesDeployStepBuilder) Build() (*KubernetesDeployStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
