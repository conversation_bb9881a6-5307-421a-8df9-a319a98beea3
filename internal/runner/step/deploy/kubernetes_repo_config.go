package deploy

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/pipeline"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

const (
	ConfigRepo = "deploy-config"
)

type (
	KubernetesDeployByConfigRepoStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact //需要下载的构建物
		Credential        string           //docker凭证
		Env               string           // 部署到的环境
		Kubectl           string           `json:"kubectl" mapstructure:"kubectl"`
		Kustomize         string           `json:"kustomize" mapstructure:"kustomize"`
		Registry          string           `json:"registry" mapstructure:"registry"`
	}
	// KubernetesDeployByConfigRepoStep in Golang
	KubernetesDeployByConfigRepoStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config KubernetesDeployByConfigRepoStepConfig
	}
)

func (g KubernetesDeployByConfigRepoStep) Actions() []action.Action {
	return g.Action
}

func (g KubernetesDeployByConfigRepoStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g KubernetesDeployByConfigRepoStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g KubernetesDeployByConfigRepoStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g KubernetesDeployByConfigRepoStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

func extractPath(input string) string {
	var result string
	if strings.HasPrefix(input, "git@") {
		parts := strings.Split(input, ":")
		result = strings.Replace(parts[1], ".git", "", -1)
	}
	//
	if strings.HasPrefix(input, "https://") || strings.HasPrefix(input, "http://") {
		input = strings.TrimPrefix(input, "http://")
		input = strings.TrimPrefix(input, "https://")
		// 找到最后一个 '/' 的位置
		index := strings.Index(input, "/")
		// 从最后一个 '/' 之后的位置开始截取字符串
		result = input[index+1:]
		// 删除 .git
		result = strings.Replace(result, ".git", "", -1)
	}
	return result
}

func buildKubernetesDeployByConfigRepoStepCmd(config KubernetesDeployByConfigRepoStepConfig, opt step.CreateStepOption) string {
	sb := strings.Builder{}
	sb.WriteString("kubectl version\n")
	// make cmd
	appConfigPath := extractPath(opt.StepContext.App.HTTPURLToRepo)
	// make ${ENV_CD_ENV} APP_NAME=${ENV_APP_NAME} APP_VERSION=${ENV_APP_VERSION} APP_PATH=${ENV_APP_PATH} COMMIT_ID=${ENV_GIT_COMMIT}
	sb.WriteString(fmt.Sprintf("make %s APP_PATH=%s", config.Env, appConfigPath))
	// 指定了镜像仓库
	if config.Registry != "" {
		sb.WriteString(fmt.Sprintf(" IMAGE_REGISTRY=%s", config.Registry))
	}
	return sb.String()
}

func NewKubernetesDeployByConfigRepoStep(opt step.CreateStepOption) (*KubernetesDeployByConfigRepoStep, error) {
	builder := NewKubernetesDeployByConfigRepoStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.download,
			builder.exec,
			builder.clean,
		).Build()
}

// KubernetesDeployByConfigRepoStepBuilder builder
type KubernetesDeployByConfigRepoStepBuilder struct {
	step         *KubernetesDeployByConfigRepoStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewKubernetesDeployByConfigRepoStepBuilder 创建构建器
func NewKubernetesDeployByConfigRepoStepBuilder() *KubernetesDeployByConfigRepoStepBuilder {
	return &KubernetesDeployByConfigRepoStepBuilder{
		step: new(KubernetesDeployByConfigRepoStep),
	}
}

func (g *KubernetesDeployByConfigRepoStepBuilder) WithOpt(opt step.CreateStepOption) *KubernetesDeployByConfigRepoStepBuilder {
	g.opt = opt
	return g
}

func (g *KubernetesDeployByConfigRepoStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *KubernetesDeployByConfigRepoStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *KubernetesDeployByConfigRepoStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[KubernetesDeployByConfigRepoStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 获取秘钥
	cert := g.opt.StepContext.Credentials[config.Credential]
	if cert == nil {
		return nil, fmt.Errorf("k8s credential not found, credential: %s", config.Credential)
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:      common.GetKubernetesStepImage(g.step.Config.Kubectl),
		WorkingDir: common.ContainerWorkspace + "/" + ConfigRepo,
		Caches:     g.step.Config.Caches,
		PrepareFiles: []agent.CopyOption{
			{
				DestPath: "/root/.kube/",
				Files: []*agent.FileEntry{
					{
						Name: "config",
						Body: cert.Value,
						Mode: 0o755,
					},
				},
			}},
	}), nil
}

// 检出代码
func (g *KubernetesDeployByConfigRepoStepBuilder) checkout() (action.Action, error) {
	return action.NewCheckoutAction(g.commonAction, agent.CheckoutOption{
		Checkout: pipeline.Checkout{
			CacheRepo:     true,
			DefaultBranch: "main",
		},
		DistDir:          ConfigRepo,
		AccessToken:      g.opt.StepContext.Source.AccessToken,
		HostStepRepoPath: common.GetStepWorkspace(g.opt.StepContext),
		HTTPURLToRepo:    "https://git.makeblock.com/makeblock-devops/deploy-config.git",
		HostGitRepoPath:  common.GetRepoWorkspace(g.opt.StepContext.Source.Name, ConfigRepo),
	}), nil
}

// 执行命令
func (g *KubernetesDeployByConfigRepoStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildKubernetesDeployByConfigRepoStepCmd(g.step.Config, g.opt),
		Env:     step.InjectEnv(g.opt), //注入环境变量
		Workdir: common.ContainerWorkspace + "/" + ConfigRepo,
	}, "发布应用"), nil
}

// 上传构建物
func (g *KubernetesDeployByConfigRepoStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *KubernetesDeployByConfigRepoStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *KubernetesDeployByConfigRepoStepBuilder) Build() (*KubernetesDeployByConfigRepoStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
