package deploy

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
	"time"
)

type CheckDeployHealthConfig struct {
	step.CommonConfig   `mapstructure:",squash"`
	HealthzURL          string `mapstructure:"healthzURL"`          // check application health url
	InitialDelaySeconds int    `mapstructure:"initialDelaySeconds"` // initial delay
	FailureThreshold    int    `mapstructure:"failureThreshold"`    // failure threshold
	PeriodSeconds       int    `mapstructure:"periodSeconds"`       // check period
}

type CheckDeployHealthStep struct {
	Agent  agent.Agent
	Action []action.Action
	Config CheckDeployHealthConfig
}

func (s CheckDeployHealthStep) Actions() []action.Action {
	return s.Action
}

func (s CheckDeployHealthStep) Finalize(ctx context.Context) error {
	return s.Agent.Clean(ctx)
}

func (s CheckDeployHealthStep) CommonConfig() *step.CommonConfig {
	return &s.Config.CommonConfig
}

// GetCredentialKey get credential key, implement StepConfig interface
func (c CheckDeployHealthConfig) GetCredentialKey() []string {
	return []string{}
}

func (c CheckDeployHealthConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewHttpAppDeploySuccessCheckStep(opt step.CreateStepOption) (*CheckDeployHealthStep, error) {
	builder := NewCheckDeployHealthStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.check,
		).Build()
}

// CheckDeployHealthStepBuilder builder
type CheckDeployHealthStepBuilder struct {
	step         *CheckDeployHealthStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewCheckDeployHealthStepBuilder 创建构建器
func NewCheckDeployHealthStepBuilder() *CheckDeployHealthStepBuilder {
	return &CheckDeployHealthStepBuilder{
		step: new(CheckDeployHealthStep),
	}
}

func (g *CheckDeployHealthStepBuilder) WithOpt(opt step.CreateStepOption) *CheckDeployHealthStepBuilder {
	g.opt = opt
	return g
}

func (g *CheckDeployHealthStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *CheckDeployHealthStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

func (g *CheckDeployHealthStepBuilder) check() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[CheckDeployHealthConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewDynamicAction("健康检查", g.commonAction, healthCheck(config)), nil
}

// healthCheck check application health
func healthCheck(config CheckDeployHealthConfig) func(ctx context.Context, ca *action.CommonAction) error {
	return func(ctx context.Context, ca *action.CommonAction) error {
		stepContext := common.GetStepContextFromCtx(ctx)
		logFromCtx := logger.GetLoggerFromCtx(ctx)
		// expected version
		expectedVersion := stepContext.Env["GIT_CHECKOUT_SHA"]
		// priority use custom version
		customHealthVersionEnvKey := "CUSTOM_HEALTH_VERSION"
		if stepContext.Env[customHealthVersionEnvKey] != "" {
			expectedVersion = stepContext.Env[customHealthVersionEnvKey]
		}
		// print version info
		logFromCtx.Infof("Expected version: %s", expectedVersion)
		logFromCtx.Infof("Waiting %d seconds for application to be ready", config.InitialDelaySeconds)
		retryCount := 0
		time.Sleep(time.Duration(config.InitialDelaySeconds) * time.Second)
		for {
			response, err := resty.New().R().Get(config.HealthzURL)
			if err != nil {
				return fmt.Errorf("health check failed: %v", err)
			}
			if response.StatusCode() == 200 {
				version, extractErr := extractVersion(response.Body())
				if extractErr != nil {
					logFromCtx.Errorf("extract version failed: %v", extractErr)
				}
				if version == expectedVersion {
					logFromCtx.Success("application is healthy and version is correct")
					return nil
				} else {
					logFromCtx.Info("application version is not correct")
					logFromCtx.Infof("expected version: %s, but got: %s", expectedVersion, version)
					return fmt.Errorf("version mismatch")
				}
			} else {
				logFromCtx.Errorf("health check failed, http code: %d", response.StatusCode())
			}
			retryCount++
			if retryCount < config.FailureThreshold {
				logFromCtx.Infof("retry in %d seconds", config.PeriodSeconds)
				time.Sleep(time.Duration(config.PeriodSeconds) * time.Second)
				continue
			} else {
				logFromCtx.Infof("health check failed %d times. Rolling back...", config.FailureThreshold)
				return fmt.Errorf("health check failed %d times", config.FailureThreshold)
			}
		}
	}
}

// extractVersion 从响应中提取版本号, json body 字段为 version
func extractVersion(body []byte) (string, error) {
	type Response struct {
		Version string `json:"version"`
	}
	var resp Response
	err := json.Unmarshal(body, &resp)
	if err != nil {
		return "", err // 如果解析失败，返回错误
	}
	return resp.Version, nil
}

// Build 返回完整step
func (g *CheckDeployHealthStepBuilder) Build() (*CheckDeployHealthStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
