package deploy

import (
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/models"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
	"strings"
)

const (
	fileName = "resource.yaml"
)

type (
	KubernetesDeployByConfigPlatformStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Credential        string // k8s凭证
		Kubectl           string // Kubectl版本
		Content           string // 部署模板
		// for status check
		Namespace  string `json:"namespace" mapstructure:"namespace"`
		Deployment string `json:"deployment" mapstructure:"deployment"`
		Timeout    int    `json:"timeout" mapstructure:"timeout"`
	}
	KubernetesDeployByConfigPlatformStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config KubernetesDeployByConfigPlatformStepConfig
	}
)

func (g KubernetesDeployByConfigPlatformStep) Actions() []action.Action {
	return g.Action
}

func (g KubernetesDeployByConfigPlatformStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g KubernetesDeployByConfigPlatformStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g KubernetesDeployByConfigPlatformStepConfig) GetCredentialKey() []string {
	return []string{g.Credential}
}

func (g KubernetesDeployByConfigPlatformStepConfig) GetConfigPluginKey() []string {
	return []string{g.Content}
}

func (g KubernetesDeployByConfigPlatformStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func buildDeployCommand(config KubernetesDeployByConfigPlatformStepConfig, name string) string {
	sb := strings.Builder{}
	// "kubectl apply -f " + fileName
	sb.WriteString(fmt.Sprintf("kubectl apply -f %s\n", name))
	// rollout status deployment/xxx -n xxx --timeout=xxx
	if config.Namespace != "" && config.Deployment != "" {
		// timeout limit
		if config.Timeout <= 0 {
			config.Timeout = 60
		}
		// prevent shell injection
		namespace := util.SanitizeInput(config.Namespace)
		deployment := util.SanitizeInput(config.Deployment)
		// build command
		sb.WriteString(fmt.Sprintf("kubectl rollout status deployment/%s -n %s --timeout=%ds\n", deployment, namespace, config.Timeout))
	}
	return sb.String()
}

func NewKubernetesDeployByConfigPlatformStep(opt step.CreateStepOption) (*KubernetesDeployByConfigPlatformStep, error) {
	builder := NewKubernetesDeployByConfigPlatformStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.render,
			builder.exec,
			builder.clean,
		).Build()
}

// KubernetesDeployByConfigPlatformStepBuilder builder
type KubernetesDeployByConfigPlatformStepBuilder struct {
	step         *KubernetesDeployByConfigPlatformStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewKubernetesDeployByConfigPlatformStepBuilder 创建构建器
func NewKubernetesDeployByConfigPlatformStepBuilder() *KubernetesDeployByConfigPlatformStepBuilder {
	return &KubernetesDeployByConfigPlatformStepBuilder{
		step: new(KubernetesDeployByConfigPlatformStep),
	}
}

func (g *KubernetesDeployByConfigPlatformStepBuilder) WithOpt(opt step.CreateStepOption) *KubernetesDeployByConfigPlatformStepBuilder {
	g.opt = opt
	return g
}

func (g *KubernetesDeployByConfigPlatformStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *KubernetesDeployByConfigPlatformStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *KubernetesDeployByConfigPlatformStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[KubernetesDeployByConfigPlatformStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 获取秘钥
	cert := g.opt.StepContext.Credentials[config.Credential]
	if cert == nil {
		return nil, fmt.Errorf("k8s credential not found, credential: %s", config.Credential)
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetKubernetesStepImage(g.step.Config.Kubectl),
		Caches: g.step.Config.Caches,
		PrepareFiles: []agent.CopyOption{
			{
				DestPath: "/root/.kube/",
				Files: []*agent.FileEntry{
					{
						Name: "config",
						Body: cert.Value,
						Mode: 0o755,
					},
				},
			}},
	}), nil
}

// 渲染模板
func (g *KubernetesDeployByConfigPlatformStepBuilder) render() (action.Action, error) {
	return action.NewDynamicAction("渲染模板", g.commonAction, func(ctx context.Context, ca *action.CommonAction) error {
		stepContext := common.GetStepContextFromCtx(ctx)
		actionLogger := logger.GetLoggerFromCtx(ctx)
		data := make(map[string]any)
		for k, v := range stepContext.Env {
			data[k] = v
		}
		configEntry := stepContext.ConfigPluginMap[g.step.Config.Content]
		var entry models.AppConfigEntry
		err := copier.Copy(&entry, configEntry)
		if err != nil {
			actionLogger.Errorf("拷贝配置失败: %s", err)
			return err
		}
		content, err := util.RenderContent(entry.Content, data)
		if err != nil {
			actionLogger.Errorf("渲染模板失败: %s", err)
			return err
		}
		_, _ = actionLogger.Write([]byte(content + "\n"))
		return g.commonAction.Agent.Copy(ctx, agent.CopyOption{
			Files: []*agent.FileEntry{
				{
					Name: fileName,
					Body: content,
					Mode: 0o755,
				},
			},
		})
	}), nil
}

// 执行命令
func (g *KubernetesDeployByConfigPlatformStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildDeployCommand(g.step.Config, fileName),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "执行部署"), nil
}

// 清理工作
func (g *KubernetesDeployByConfigPlatformStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *KubernetesDeployByConfigPlatformStepBuilder) Build() (*KubernetesDeployByConfigPlatformStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
