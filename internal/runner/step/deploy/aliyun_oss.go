package deploy

// 前端项目部署到oss
import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	AliYunOSSDeployStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Path              string           // 部署到的路径
		Credential        string           // oss凭证
		Artifacts         []agent.Artifact //需要下载的构建物
		Endpoint          string           `json:"endpoint" mapstructure:"endpoint"`
		Workspace         string           `json:"workspace" mapstructure:"workspace"`
		// for cdn flush
		AliYun     string `json:"aliyun" mapstructure:"aliyun"`         // 阿里云cli版本
		Region     string `json:"region" mapstructure:"region"`         // 阿里云cdn区域
		ObjectPath string `json:"objectPath" mapstructure:"objectPath"` // 阿里云cdn刷新路径
	}
	AliYunOSSDeployStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config AliYunOSSDeployStepConfig
	}
	AliYunCertEntry struct {
		AccessKeyID     string `json:"accessKeyId"`
		AccessKeySecret string `json:"accessKeySecret"`
	}
)

func (g AliYunOSSDeployStep) Actions() []action.Action {
	return g.Action
}

func (g AliYunOSSDeployStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g AliYunOSSDeployStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g AliYunOSSDeployStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g AliYunOSSDeployStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

func buildAliYunOSSDeployStepCmd(config AliYunOSSDeployStepConfig, opt step.CreateStepOption) string {
	sb := strings.Builder{}
	// 获取阿里云凭证
	cert, _ := opt.StepContext.Credentials[config.Credential]
	entry := util.JSONStringToStruct[AliYunCertEntry](cert.Value)
	access := fmt.Sprintf("--access-key-id=%s --access-key-secret=%s",
		entry.AccessKeyID, entry.AccessKeySecret)
	//make cmd
	sb.WriteString("ls -alh\n")
	sb.WriteString(fmt.Sprintf("ossutil cp -rf %s %s -u --endpoint=%s %s\n",
		config.Path, config.Workspace, config.Endpoint, access))
	// check if need to flush cdn
	if config.ObjectPath != "" {
		sb.WriteString(fmt.Sprintf("aliyun cdn RefreshObjectCaches --ObjectPath %s --ObjectType=Directory --region=%s %s\n",
			config.ObjectPath, config.Region, access))
	}
	// hide key
	if entry.AccessKeyID != "" {
		opt.Logger.AddMask(entry.AccessKeyID)
	}
	if entry.AccessKeySecret != "" {
		opt.Logger.AddMask(entry.AccessKeySecret)
	}
	return sb.String()
}

func NewAliYunOSSDeployStep(opt step.CreateStepOption) (*AliYunOSSDeployStep, error) {
	builder := NewAliYunOSSDeployStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.download,
			builder.exec,
			builder.clean,
		).Build()
}

// AliYunOSSDeployStepBuilder builder
type AliYunOSSDeployStepBuilder struct {
	step         *AliYunOSSDeployStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewAliYunOSSDeployStepBuilder 创建构建器
func NewAliYunOSSDeployStepBuilder() *AliYunOSSDeployStepBuilder {
	return &AliYunOSSDeployStepBuilder{
		step: new(AliYunOSSDeployStep),
	}
}

func (g *AliYunOSSDeployStepBuilder) WithOpt(opt step.CreateStepOption) *AliYunOSSDeployStepBuilder {
	g.opt = opt
	return g
}

func (g *AliYunOSSDeployStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *AliYunOSSDeployStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *AliYunOSSDeployStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[AliYunOSSDeployStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image: common.GetAliYunImage(util.SetDefaultIfEmpty(config.Version, "1.7.18")),
	}), nil
}

// 检出代码
func (g *AliYunOSSDeployStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 执行命令
func (g *AliYunOSSDeployStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildAliYunOSSDeployStepCmd(g.step.Config, g.opt), //注入环境变量
	}, "发布应用"), nil
}

// 清理工作
func (g *AliYunOSSDeployStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *AliYunOSSDeployStepBuilder) Build() (*AliYunOSSDeployStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
