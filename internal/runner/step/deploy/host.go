package deploy

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
	"strings"
	"sync"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
)

type HostDeployConfig struct {
	step.CommonConfig `mapstructure:",squash"`
	Artifacts         []agent.Artifact // 需要上传的构建物
	DependArtifact    []agent.Artifact // 下载上游的构建物
	Path              string           `json:"path" mapstructure:"path"`             // 制品同步到的目录
	Credential        string           `json:"credential" mapstructure:"credential"` // 主机部署凭证信息
	Port              uint             `json:"port" mapstructure:"port"`             // ssh端口
}

type HostDeployStep struct {
	Env    []string
	Name   string
	Agent  agent.Agent
	Config HostDeployConfig
	Action []action.Action
}

func (h HostDeployStep) Actions() []action.Action {
	return h.Action
}

func (h HostDeployStep) Finalize(ctx context.Context) error {
	return h.Agent.Clean(ctx)
}

func (h HostDeployStep) CommonConfig() *step.CommonConfig {
	return &h.Config.CommonConfig
}

func (h HostDeployConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, h.Credential)
	return ret
}

func (h HostDeployConfig) GetDownloadArtifacts() []agent.Artifact {
	return h.Artifacts
}

func NewHostDeployStep(opt step.CreateStepOption) (*HostDeployStep, error) {
	builder := NewHostDeployStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.download,
			builder.transfer,
			builder.deploy,
			builder.clean,
		).Build()
}

// HostDeployStepBuilder builder
type HostDeployStepBuilder struct {
	step         *HostDeployStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewHostDeployStepBuilder 创建构建器
func NewHostDeployStepBuilder() *HostDeployStepBuilder {
	return &HostDeployStepBuilder{
		step: new(HostDeployStep),
	}
}

func (g *HostDeployStepBuilder) WithOpt(opt step.CreateStepOption) *HostDeployStepBuilder {
	g.opt = opt
	return g
}

func (g *HostDeployStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *HostDeployStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *HostDeployStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[HostDeployConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetExecStepImage(util.SetDefaultIfEmpty(config.Version, "22.04")),
		Caches: g.step.Config.Caches,
	}), nil
}

// 传输文件
func (g *HostDeployStepBuilder) transfer() (action.Action, error) {
	return action.NewDynamicAction("传输文件", g.commonAction,
		func(ctx context.Context, ca *action.CommonAction) error {
			return scpFile(ctx, g.step.Config, g.opt)
		}), nil
}

// 主机部署
func (g *HostDeployStepBuilder) deploy() (action.Action, error) {
	return action.NewDynamicAction("主机部署", g.commonAction,
		func(ctx context.Context, ca *action.CommonAction) error {
			return deployToHosts(ctx, g.step.Config, g.opt)
		}), nil
}

// 下载制品
func (g *HostDeployStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *HostDeployStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *HostDeployStepBuilder) Build() (*HostDeployStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}

type HostCredentialEntry struct {
	Server      string `json:"server"`
	Username    string `json:"username"`
	Password    string `json:"password,omitempty"`     // 密码认证时使用
	PrivateKey  string `json:"private_key,omitempty"`  // 密钥认证时使用
	KeyPassword string `json:"key_password,omitempty"` // 私钥密码（如果私钥加密）
}

// createSSHAuthMethods 根据凭证创建SSH认证方法
// 优先使用密码认证，如果没有密码则使用密钥认证
func createSSHAuthMethods(entry HostCredentialEntry) ([]ssh.AuthMethod, error) {
	var authMethods []ssh.AuthMethod

	// 优先使用密码认证
	if entry.Password != "" {
		authMethods = append(authMethods, ssh.Password(entry.Password))
		return authMethods, nil
	}

	// 如果没有密码，使用密钥认证
	if entry.PrivateKey != "" {
		var signer ssh.Signer
		var err error

		if entry.KeyPassword != "" {
			// 加密的私钥
			signer, err = ssh.ParsePrivateKeyWithPassphrase([]byte(entry.PrivateKey), []byte(entry.KeyPassword))
		} else {
			// 未加密的私钥
			signer, err = ssh.ParsePrivateKey([]byte(entry.PrivateKey))
		}

		if err != nil {
			return nil, fmt.Errorf("failed to parse private key: %v", err)
		}
		authMethods = append(authMethods, ssh.PublicKeys(signer))
		return authMethods, nil
	}

	return nil, errors.New("either password or private key must be provided")
}

func scpFile(ctx context.Context, hostDeployConfig HostDeployConfig, opt step.CreateStepOption) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	logFromCtx.Debug("start to copy file to remote host")
	c, ok := opt.StepContext.Credentials[hostDeployConfig.Credential]
	if !ok {
		return errors.New("credential not found")
	}
	// 获取上游构建结果目录
	if len(hostDeployConfig.GetDownloadArtifacts()) == 0 {
		logFromCtx.Info("artifact is empty, skip copy file to remote host")
		return nil
	}
	// 解析凭证
	var entry HostCredentialEntry
	err := json.Unmarshal([]byte(c.Value), &entry)
	if err != nil {
		logFromCtx.Errorf("unmarshal host credential error: %s", err)
		return err
	}
	// 创建SSH认证方法
	authMethods, err := createSSHAuthMethods(entry)
	if err != nil {
		logFromCtx.Errorf("create ssh auth methods error: %s", err)
		return err
	}
	// ssh client config
	config := &ssh.ClientConfig{
		Timeout:         time.Minute,
		User:            entry.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Auth:            authMethods,
	}
	// dial to get ssh client
	addr := fmt.Sprintf("%s:%d", entry.Server, hostDeployConfig.Port)
	sshClient, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		logFromCtx.Errorf("create ssh client failed, dial to %s error: %s", addr, err)
		return err
	}
	defer func(sshClient *ssh.Client) {
		_ = sshClient.Close()
	}(sshClient)
	// create stp client
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		logFromCtx.Errorf("Unable to create SFTP client: %v", err)
		return err
	}
	defer func(sftpClient *sftp.Client) {
		_ = sftpClient.Close()
	}(sftpClient)
	// 确保远程目录存在
	err = sftpClient.MkdirAll(hostDeployConfig.Path)
	if err != nil {
		logFromCtx.Errorf("Unable to create remote directory: %v", err)
		return err
	}
	// range all artifacts
	for _, artifact := range hostDeployConfig.GetDownloadArtifacts() {
		logFromCtx.Infof("artifact: %s", artifact.Name)
		localFilePath := fmt.Sprintf("%s/%s.zip", common.GetArtifactWorkspace(opt.StepContext), artifact.Name)
		// 打开本地文件
		localFile, err := os.Open(localFilePath)
		if err != nil {
			logFromCtx.Errorf("Unable to open local file: %v", err)
			return err
		}
		// 获取本地文件名
		localFileName := filepath.Base(localFilePath)
		// 在远程目录中创建文件
		remoteFilePath := filepath.Join(hostDeployConfig.Path, localFileName)
		remoteFile, err := sftpClient.Create(remoteFilePath)
		if err != nil {
			logFromCtx.Errorf("Unable to create remote file: %v", err)
			return err
		}
		// 复制文件内容
		bytes, err := io.Copy(remoteFile, localFile)
		if err != nil {
			logFromCtx.Errorf("Unable to copy file content: %v", err)
			return err
		}
		logFromCtx.Infof("file %s successfully copied to %s:%s\n", localFilePath, entry.Server, remoteFilePath)
		logFromCtx.Infof("bytes transferred: %d\n", bytes)
		// 关闭文件
		_ = localFile.Close()
		_ = remoteFile.Close()
	}
	logFromCtx.Success("success copy all artifact to remote host")
	return nil
}

func deployToHosts(ctx context.Context, hostDeployConfig HostDeployConfig, opt step.CreateStepOption) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	c, ok := opt.StepContext.Credentials[hostDeployConfig.Credential]
	if !ok {
		return fmt.Errorf("credential not found, key: %s", hostDeployConfig.Credential)
	}
	// parse credential
	var entry HostCredentialEntry
	err := json.Unmarshal([]byte(c.Value), &entry)
	if err != nil {
		logFromCtx.Errorf("unmarshal credential error: %s", err)
		return err
	}
	// 创建SSH认证方法
	authMethods, err := createSSHAuthMethods(entry)
	if err != nil {
		logFromCtx.Errorf("create ssh auth methods error: %s", err)
		return err
	}
	// ssh client config
	config := &ssh.ClientConfig{
		Timeout:         time.Minute,
		User:            entry.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Auth:            authMethods,
	}
	// dial to get ssh client
	addr := fmt.Sprintf("%s:%d", entry.Server, hostDeployConfig.Port)
	sshClient, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		log.Error(fmt.Sprintf("create ssh client failed, dial to %s error: %s", addr, err))
		logFromCtx.Errorf("create ssh client failed, dial to %s error: %s", addr, err)
		return err
	}
	defer func(sshClient *ssh.Client) {
		_ = sshClient.Close()
	}(sshClient)

	// create ssh-session
	session, err := sshClient.NewSession()
	if err != nil {
		log.Error(fmt.Sprintf("create ssh session failed, %v", err))
		logFromCtx.Errorf("create ssh session failed, %v", err)
		return err
	}
	defer func(session *ssh.Session) {
		_ = session.Close()
	}(session)

	// 获取标准输出和标准错误管道
	stdout, err := session.StdoutPipe()
	if err != nil {
		logFromCtx.Errorf("Unable to setup stdout for session: %v", err)
		return err
	}
	stderr, err := session.StderrPipe()
	if err != nil {
		logFromCtx.Errorf("Unable to setup stderr for session: %v", err)
		return err
	}
	// 获取需要设置的环境变量
	envVars := step.InjectEnv(opt)
	// 构建环境变量设置命令
	var sb strings.Builder
	for key, value := range envVars {
		sb.WriteString(fmt.Sprintf("export %s=\"%s\";\n", key, value)) // 使用单引号来确保特殊字符被正确处理
	}
	if opt.StepContext.Debug {
		_, _ = logFromCtx.Write([]byte(sb.String()))
	}
	sb.WriteString("set -ex;\n") // 设置错误退出
	_, _ = logFromCtx.Write([]byte(hostDeployConfig.Commands))
	// 将环境变量设置命令与原有命令组合
	sb.WriteString(runner.InjectSecret(hostDeployConfig.Commands, opt.StepContext.Secret))
	// 注入密钥
	combinedCommands := sb.String()
	// 开始执行命令
	err = session.Start(combinedCommands)
	if err != nil {
		logFromCtx.Errorf("failed to start command: %v", err)
		return err
	}
	// 创建一个等待组来同步输出读取
	var wg sync.WaitGroup
	wg.Add(2)
	// 读取并输出标准输出
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stdout)
		for scanner.Scan() {
			logFromCtx.Infof(scanner.Text())
		}
	}()
	// 读取并输出标准错误
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stderr)
		for scanner.Scan() {
			logFromCtx.Errorf(scanner.Text())
		}
	}()
	// 等待命令执行完成
	err = session.Wait()
	// 等待输出读取完成
	wg.Wait()
	if err != nil {
		logFromCtx.Errorf("command executed with error: %v", err)
		return err
	}
	logFromCtx.Success("command executed successfully")
	return nil
}
