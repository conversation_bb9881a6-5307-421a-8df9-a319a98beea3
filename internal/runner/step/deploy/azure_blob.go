package deploy

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/internal/server/credential"
	"pipeline/pkg/runner/agent"
)

// 前端项目部署到 azure blob

type (
	AzureServicePrincipal struct {
		AppID    string `json:"AZURE_APPID" mapstructure:"AZURE_APPID"`
		Tenant   string `json:"AZURE_TENANT" mapstructure:"AZURE_TENANT"`
		Password string `json:"AZURE_PASSWORD" mapstructure:"AZURE_PASSWORD"`
	}
	AzureBlobDeployStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Path              string           // 部署到的环境
		Version           string           // az版本
		Artifacts         []agent.Artifact //需要下载的构建物
		Credential        string           // oss凭证
		Account           string           `json:"account" mapstructure:"account"`
		Remote            string           `json:"remote" mapstructure:"remote"` // 远程地址: $web\index.html 等等
		// 是否需要刷新CDN
		Directory string `json:"directory" mapstructure:"directory"` // 需要刷新的目录
		Group     string `json:"group" mapstructure:"group"`
		Profile   string `json:"profile" mapstructure:"profile"`
		Endpoint  string `json:"endpoint" mapstructure:"endpoint"`
	}
	// AzureBlobDeployStep in Golang
	AzureBlobDeployStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config AzureBlobDeployStepConfig
	}
)

func (g AzureBlobDeployStep) Actions() []action.Action {
	return g.Action
}

func (g AzureBlobDeployStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g AzureBlobDeployStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g AzureBlobDeployStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g AzureBlobDeployStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

func buildAzureBlobDeployStepCmd(config AzureBlobDeployStepConfig, cert *credential.PipelineCredential, opt step.CreateStepOption) string {
	// get azure service principal, from cert value field
	asp := util.JSONStringToStruct[AzureServicePrincipal](cert.Value)
	// cmd
	sb := strings.Builder{}
	sb.WriteString("az -v\nls -alh\n")
	//make cmd
	opt.Logger.AddMask(asp.AppID)
	opt.Logger.AddMask(asp.Password)
	opt.Logger.AddMask(asp.Tenant)
	//az login --service-principal -u ${AZURE_APPID} -p ${AZURE_PASSWORD} --tenant ${AZURE_TENANT}
	sb.WriteString(fmt.Sprintf("az login --service-principal -u %s -p %s --tenant %s\n", asp.AppID, asp.Password, asp.Tenant))
	//sh "az storage blob upload-batch --account-name ${AZURE_STORAGE_ACCOUNT} -d '\$web' -s ./dist --overwrite"
	sb.WriteString(fmt.Sprintf("az storage blob upload-batch --account-name %s -d '%s' -s %s --overwrite\n", config.Account, config.Remote, config.Path))
	// 是否需要刷新CDN（全部配置不为空）
	if config.Directory != "" && config.Group != "" && config.Profile != "" && config.Endpoint != "" {
		// maybe need to render the config
		data := opt.StepContext.Env
		config.Group = util.RenderContentWithShellDelimiters(config.Group, data)
		config.Profile = util.RenderContentWithShellDelimiters(config.Profile, data)
		config.Endpoint = util.RenderContentWithShellDelimiters(config.Endpoint, data)
		config.Directory = util.RenderContentWithShellDelimiters(config.Directory, data)
		//sh "az cdn endpoint purge -g ${AZURE_CDN_GROUP} -n ${AZURE_CDN_ENDPOINT} --profile-name ${AZURE_CDN_PROFILE} --content-paths '/'"
		sb.WriteString(fmt.Sprintf("az cdn endpoint purge -g %s -n %s --profile-name %s --content-paths '%s'\n", config.Group, config.Endpoint, config.Profile, config.Directory))
	}
	return sb.String()
}

func NewAzureBlobDeployStep(opt step.CreateStepOption) (*AzureBlobDeployStep, error) {
	builder := NewAzureBlobDeployStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.download,
			builder.exec,
			builder.clean,
		).Build()
}

// AzureBlobDeployStepBuilder builder
type AzureBlobDeployStepBuilder struct {
	step         *AzureBlobDeployStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewAzureBlobDeployStepBuilder 创建构建器
func NewAzureBlobDeployStepBuilder() *AzureBlobDeployStepBuilder {
	return &AzureBlobDeployStepBuilder{
		step: new(AzureBlobDeployStep),
	}
}

func (g *AzureBlobDeployStepBuilder) WithOpt(opt step.CreateStepOption) *AzureBlobDeployStepBuilder {
	g.opt = opt
	return g
}

func (g *AzureBlobDeployStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *AzureBlobDeployStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *AzureBlobDeployStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[AzureBlobDeployStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetAzureStepImage(util.SetDefaultIfEmpty(config.Version, "2.65.0")),
		Caches: g.step.Config.Caches,
	}), nil
}

// 下载产物
func (g *AzureBlobDeployStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 执行命令
func (g *AzureBlobDeployStepBuilder) exec() (action.Action, error) {
	cert, ok := g.opt.StepContext.Credentials[g.step.Config.Credential]
	if !ok {
		return nil, fmt.Errorf("credential not found, credential: %s", g.step.Config.Credential)
	}
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildAzureBlobDeployStepCmd(g.step.Config, cert, g.opt),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "发布应用"), nil
}

// 清理工作
func (g *AzureBlobDeployStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *AzureBlobDeployStepBuilder) Build() (*AzureBlobDeployStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
