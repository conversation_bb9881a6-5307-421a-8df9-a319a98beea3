package deploy

// 上传文件到minio
import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	MinioDeployStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Credential        string           // oss凭证
		Artifacts         []agent.Artifact //需要下载的构建物
		SrcPath           string           // 本地资源路径
		DistPath          string           // 部署到的路径
	}
	MinioDeployStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config MinioDeployStepConfig
	}
	MinioDeployCertEntry struct {
		Endpoint     string `json:"endpoint"`
		AccessKey    string `json:"accessKey"`
		AccessSecret string `json:"accessSecret"`
	}
)

func (g MinioDeployStep) Actions() []action.Action {
	return g.Action
}

func (g MinioDeployStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g MinioDeployStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g MinioDeployStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g MinioDeployStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

func buildMinioDeployStepCmd(config MinioDeployStepConfig, opt step.CreateStepOption) string {
	alias := "minio"
	sb := strings.Builder{}
	// 获取阿里云凭证
	cert, _ := opt.StepContext.Credentials[config.Credential]
	entry := util.JSONStringToStruct[MinioDeployCertEntry](cert.Value)
	// mc alias set minio http://minio.makeblock.com:9000 Pw4qJ0FMyyJh4jw BsJni1goQNT97ttgoGv9GTIRxFXscsoNqO
	sb.WriteString(fmt.Sprintf(" mc alias set %s %s %s %s\n",
		alias, entry.Endpoint, entry.AccessKey, entry.AccessSecret))
	// ensure path
	if !strings.HasPrefix(config.DistPath, "/") {
		config.DistPath = "/" + config.DistPath
	}
	// cp cmd
	sb.WriteString("ls -alh\n")
	// render the path
	config.SrcPath = util.RenderContentWithShellDelimiters(config.SrcPath, opt.StepContext.Env)
	config.DistPath = util.RenderContentWithShellDelimiters(config.DistPath, opt.StepContext.Env)
	sb.WriteString(fmt.Sprintf("mc cp --recursive %s %s%s\n",
		config.SrcPath, alias, config.DistPath))
	// hide key
	if entry.AccessKey != "" {
		opt.Logger.AddMask(entry.AccessKey)
	}
	if entry.AccessSecret != "" {
		opt.Logger.AddMask(entry.AccessSecret)
	}
	return sb.String()
}

func NewMinioDeployStep(opt step.CreateStepOption) (*MinioDeployStep, error) {
	builder := NewMinioDeployStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.download,
			builder.exec,
			builder.clean,
		).Build()
}

// MinioDeployStepBuilder builder
type MinioDeployStepBuilder struct {
	step         *MinioDeployStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewMinioDeployStepBuilder 创建构建器
func NewMinioDeployStepBuilder() *MinioDeployStepBuilder {
	return &MinioDeployStepBuilder{
		step: new(MinioDeployStep),
	}
}

func (g *MinioDeployStepBuilder) WithOpt(opt step.CreateStepOption) *MinioDeployStepBuilder {
	g.opt = opt
	return g
}

func (g *MinioDeployStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *MinioDeployStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *MinioDeployStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[MinioDeployStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image: common.GetMinioStepImage(util.SetDefaultIfEmpty(config.Version, "2025-04-16-18-13-26")),
	}), nil
}

// 检出代码
func (g *MinioDeployStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 执行命令
func (g *MinioDeployStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildMinioDeployStepCmd(g.step.Config, g.opt), //注入环境变量
	}, "发布应用"), nil
}

// 清理工作
func (g *MinioDeployStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *MinioDeployStepBuilder) Build() (*MinioDeployStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
