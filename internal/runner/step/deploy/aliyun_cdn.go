package deploy

// 刷新阿里云CDN
import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	AliYunCDNRefreshStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Credential        string // oss凭证
		// for cdn flush
		AliYun     string `json:"aliyun" mapstructure:"aliyun"`         // 阿里云cli版本
		Region     string `json:"region" mapstructure:"region"`         // 阿里云cdn区域
		ObjectPath string `json:"objectPath" mapstructure:"objectPath"` // 阿里云cdn刷新路径
		ObjectType string `json:"objectType" mapstructure:"objectType"` // 指定模板资源类型，有效值为：Directory(目录)、File(文件)、Regex(正则)
	}
	AliYunCDNRefreshStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config AliYunCDNRefreshStepConfig
	}
)

func (g AliYunCDNRefreshStep) Actions() []action.Action {
	return g.Action
}

func (g AliYunCDNRefreshStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g AliYunCDNRefreshStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g AliYunCDNRefreshStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g AliYunCDNRefreshStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func matchObjectType(objectType string) string {
	switch objectType {
	case "Directory":
		return "Directory"
	case "File":
		return "File"
	case "Regex":
		return "Regex"
	default:
		return "File"
	}
}

func NewAliYunCDNRefreshStep(opt step.CreateStepOption) (*AliYunCDNRefreshStep, error) {
	builder := NewAliYunCDNRefreshStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.exec,
			builder.clean,
		).Build()
}

// AliYunCDNRefreshStepBuilder builder
type AliYunCDNRefreshStepBuilder struct {
	step         *AliYunCDNRefreshStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewAliYunCDNRefreshStepBuilder 创建构建器
func NewAliYunCDNRefreshStepBuilder() *AliYunCDNRefreshStepBuilder {
	return &AliYunCDNRefreshStepBuilder{
		step: new(AliYunCDNRefreshStep),
	}
}

func (g *AliYunCDNRefreshStepBuilder) WithOpt(opt step.CreateStepOption) *AliYunCDNRefreshStepBuilder {
	g.opt = opt
	return g
}

func (g *AliYunCDNRefreshStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *AliYunCDNRefreshStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *AliYunCDNRefreshStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[AliYunCDNRefreshStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetAliYunImage(util.SetDefaultIfEmpty(config.AliYun, "1.7.18")),
		Caches: g.step.Config.Caches,
	}), nil
}

func buildAliYunCDNRefreshStepCmd(config AliYunCDNRefreshStepConfig, opt step.CreateStepOption) string {
	sb := strings.Builder{}
	// 获取阿里云凭证
	cert, _ := opt.StepContext.Credentials[config.Credential]
	entry := util.JSONStringToStruct[AliYunCertEntry](cert.Value)
	// check if need to flush cdn
	if config.ObjectPath != "" {
		sb.WriteString(
			fmt.Sprintf("aliyun cdn RefreshObjectCaches --ObjectPath %s --ObjectType=%s --region=%s --access-key-id=%s --access-key-secret=%s",
				config.ObjectPath, matchObjectType(config.ObjectType), config.Region, entry.AccessKeyID, entry.AccessKeySecret))
	}
	// hide key
	if entry.AccessKeyID != "" {
		opt.Logger.AddMask(entry.AccessKeyID)
	}
	if entry.AccessKeySecret != "" {
		opt.Logger.AddMask(entry.AccessKeySecret)
	}
	return sb.String()
}

// 执行命令
func (g *AliYunCDNRefreshStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildAliYunCDNRefreshStepCmd(g.step.Config, g.opt),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "刷新资源"), nil
}

// 清理工作
func (g *AliYunCDNRefreshStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *AliYunCDNRefreshStepBuilder) Build() (*AliYunCDNRefreshStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
