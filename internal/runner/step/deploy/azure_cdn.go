package deploy

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/internal/server/credential"
	"pipeline/pkg/runner/agent"
)

// 前端项目部署到 azure blob

type (
	AzureCDNRefreshStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Credential        string // oss凭证
		// 是否需要刷新CDN
		Directory string `json:"directory" mapstructure:"directory"` // 需要刷新的目录
		Group     string `json:"group" mapstructure:"group"`
		Profile   string `json:"profile" mapstructure:"profile"`
		Endpoint  string `json:"endpoint" mapstructure:"endpoint"`
	}
	// AzureCDNRefreshStep in Golang
	AzureCDNRefreshStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config AzureCDNRefreshStepConfig
	}
)

func (g AzureCDNRefreshStep) Actions() []action.Action {
	return g.Action
}

func (g AzureCDNRefreshStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g AzureCDNRefreshStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g AzureCDNRefreshStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g AzureCDNRefreshStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func buildAzureCDNRefreshStepCmd(config AzureCDNRefreshStepConfig,
	cert *credential.PipelineCredential, opt step.CreateStepOption) string {
	// get azure service principal, from cert value field
	asp := util.JSONStringToStruct[AzureServicePrincipal](cert.Value)
	// cmd
	sb := strings.Builder{}
	//make cmd
	opt.Logger.AddMask(asp.AppID)
	opt.Logger.AddMask(asp.Password)
	opt.Logger.AddMask(asp.Tenant)
	//az login --service-principal -u ${AZURE_APPID} -p ${AZURE_PASSWORD} --tenant ${AZURE_TENANT}
	sb.WriteString(fmt.Sprintf("az login --service-principal -u %s -p %s --tenant %s\n", asp.AppID, asp.Password, asp.Tenant))
	// 是否需要刷新CDN（全部配置不为空）
	if config.Directory != "" && config.Group != "" && config.Profile != "" && config.Endpoint != "" {
		//sh "az cdn endpoint purge -g ${AZURE_CDN_GROUP} -n ${AZURE_CDN_ENDPOINT} --profile-name ${AZURE_CDN_PROFILE} --content-paths '/'"
		sb.WriteString(fmt.Sprintf("az cdn endpoint purge -g %s -n %s --profile-name %s --content-paths '%s'\n",
			config.Group, config.Endpoint, config.Profile, config.Directory))
	}
	return sb.String()
}

func NewAzureCDNRefreshStep(opt step.CreateStepOption) (*AzureCDNRefreshStep, error) {
	builder := NewAzureCDNRefreshStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.exec,
			builder.clean,
		).Build()
}

// AzureCDNRefreshStepBuilder builder
type AzureCDNRefreshStepBuilder struct {
	step         *AzureCDNRefreshStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewAzureCDNRefreshStepBuilder 创建构建器
func NewAzureCDNRefreshStepBuilder() *AzureCDNRefreshStepBuilder {
	return &AzureCDNRefreshStepBuilder{
		step: new(AzureCDNRefreshStep),
	}
}

func (g *AzureCDNRefreshStepBuilder) WithOpt(opt step.CreateStepOption) *AzureCDNRefreshStepBuilder {
	g.opt = opt
	return g
}

func (g *AzureCDNRefreshStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *AzureCDNRefreshStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *AzureCDNRefreshStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[AzureCDNRefreshStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetAzureStepImage(util.SetDefaultIfEmpty(config.Version, "2.65.0")),
		Caches: g.step.Config.Caches,
	}), nil
}

// 刷新资源
func (g *AzureCDNRefreshStepBuilder) exec() (action.Action, error) {
	cert, ok := g.opt.StepContext.Credentials[g.step.Config.Credential]
	if !ok {
		return nil, fmt.Errorf("credential not found, credential: %s", g.step.Config.Credential)
	}
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildAzureCDNRefreshStepCmd(g.step.Config, cert, g.opt),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "刷新资源"), nil
}

// 清理工作
func (g *AzureCDNRefreshStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *AzureCDNRefreshStepBuilder) Build() (*AzureCDNRefreshStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
