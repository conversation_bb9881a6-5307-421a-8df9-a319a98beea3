package deploy

// 刷新Cloudflare缓存
import (
	"context"
	"fmt"
	"github.com/go-resty/resty/v2"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/util"
	"time"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	CloudflareCDNRefreshStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Credential        string   // 访问凭证
		Region            string   `json:"region" mapstructure:"region"` // cdn区域
		Type              string   `json:"type" mapstructure:"type"`     // 路径类型
		Paths             []string `json:"paths" mapstructure:"paths"`   // cdn刷新路径
	}
	CloudflareCDNRefreshStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config CloudflareCDNRefreshStepConfig
	}
	/*
		{
			"email": " <EMAIL>",
			"key": "xxxxx"
		}
	*/
	CloudflareCertEntry struct {
		Email string `json:"email"`
		Key   string `json:"key"`
	}
)

func (g CloudflareCDNRefreshStep) Actions() []action.Action {
	return g.Action
}

func (g CloudflareCDNRefreshStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g CloudflareCDNRefreshStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g CloudflareCDNRefreshStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g CloudflareCDNRefreshStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewCloudflareCDNRefreshStep(opt step.CreateStepOption) (*CloudflareCDNRefreshStep, error) {
	builder := NewCloudflareCDNRefreshStepBuilder()
	return builder.WithOpt(opt).WithActionFn(
		builder.refresh,
	).Build()
}

// CloudflareCDNRefreshStepBuilder builder
type CloudflareCDNRefreshStepBuilder struct {
	step         *CloudflareCDNRefreshStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewCloudflareCDNRefreshStepBuilder 创建构建器
func NewCloudflareCDNRefreshStepBuilder() *CloudflareCDNRefreshStepBuilder {
	return &CloudflareCDNRefreshStepBuilder{
		step: new(CloudflareCDNRefreshStep),
	}
}

func (g *CloudflareCDNRefreshStepBuilder) WithOpt(opt step.CreateStepOption) *CloudflareCDNRefreshStepBuilder {
	g.opt = opt
	return g
}

func (g *CloudflareCDNRefreshStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *CloudflareCDNRefreshStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

func (g *CloudflareCDNRefreshStepBuilder) refresh() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[CloudflareCDNRefreshStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewDynamicAction("刷新缓存", g.commonAction, refresh(config)), nil
}

// refresh 刷新缓存
func refresh(config CloudflareCDNRefreshStepConfig) func(ctx context.Context, ca *action.CommonAction) error {
	return func(ctx context.Context, ca *action.CommonAction) error {
		logFromCtx := logger.GetLoggerFromCtx(ctx)
		stepContext := common.GetStepContextFromCtx(ctx)
		logFromCtx.Infof("region: %s, type: %s, paths: %s", config.Region, config.Type, config.Paths)
		// 获取凭证
		cert, _ := stepContext.Credentials[config.Credential]
		entry := util.JSONStringToStruct[CloudflareCertEntry](cert.Value)
		// 请求
		client := resty.New()
		client.SetTimeout(30 * time.Second)
		client.SetHeader("X-Auth-Email", entry.Email)
		client.SetHeader("X-Auth-Key", entry.Key)
		client.SetHeader("Content-Type", "application/json")
		response, err := client.R().SetBody(map[string]interface{}{
			config.Type: config.Paths,
		}).Post(fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/purge_cache", config.Region))
		if err != nil {
			return err
		}
		if response.StatusCode() != 200 {
			return fmt.Errorf("刷新失败: %s", response.String())
		}
		logFromCtx.Successf("刷新成功: %s", response.String())
		return nil
	}
}

// Build 返回完整step
func (g *CloudflareCDNRefreshStepBuilder) Build() (*CloudflareCDNRefreshStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
