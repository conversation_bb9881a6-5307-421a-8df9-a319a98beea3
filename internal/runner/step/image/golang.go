package image

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	// GolangImageBuildConfig in Golang
	GolangImageBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact // 需要上传的构建物
		GoVersion         string           `json:"go_version" mapstructure:"go_version"`
		Credential        string           //docker凭证
	}
	// GolangImageBuildStep in Golang
	GolangImageBuildStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config GolangImageBuildConfig
	}
)

func (g GolangImageBuildStep) Actions() []action.Action {
	return g.Action
}

func (g GolangImageBuildStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g GolangImageBuildStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g GolangImageBuildConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g GolangImageBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

// GolangImageBuildStepBuilder builder
type GolangImageBuildStepBuilder struct {
	step         *GolangImageBuildStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
	//step.CacheBuilder
}

// NewGolangImageBuildStepBuilder 创建构建器
func NewGolangImageBuildStepBuilder() *GolangImageBuildStepBuilder {
	return &GolangImageBuildStepBuilder{
		step: new(GolangImageBuildStep),
	}
}

func (g *GolangImageBuildStepBuilder) WithOpt(opt step.CreateStepOption) *GolangImageBuildStepBuilder {
	g.opt = opt
	return g
}

func (g *GolangImageBuildStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *GolangImageBuildStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *GolangImageBuildStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[GolangImageBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:    common.GetGolangStepImage(g.step.Config.GoVersion),
		Services: []agent.Service{agent.DockerService}, //依赖的服务
		Caches:   g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *GolangImageBuildStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *GolangImageBuildStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell: g.step.Config.Shell,
		Command: fmt.Sprintf("%s\n%s\n%s", step.BuildGitInsteadOfCmd(g.opt),
			buildDockerLoginCmd(g.step.Config.Credential, g.opt), g.step.Config.Commands),
		Env: step.InjectEnv(g.opt), //注入环境变量
	}, actionName), nil
}

// 上传构建物
func (g *GolangImageBuildStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *GolangImageBuildStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *GolangImageBuildStepBuilder) Build() (*GolangImageBuildStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}

func NewGolangImageBuildStep(opt step.CreateStepOption) (*GolangImageBuildStep, error) {
	builder := NewGolangImageBuildStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}
