package image

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	// JavaImageBuildConfig in Golang
	JavaImageBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact // 需要上传的构建物
		JdkVersion        string           `json:"jdk_version" mapstructure:"jdk_version"`
		Credential        string           //docker凭证
	}
	// JavaImageBuildStep in Golang
	JavaImageBuildStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config JavaImageBuildConfig
	}
)

func (g JavaImageBuildStep) Actions() []action.Action {
	return g.Action
}

func (g JavaImageBuildStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g JavaImageBuildStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g JavaImageBuildConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g JavaImageBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewJavaImageBuildStep(opt step.CreateStepOption) (*JavaImageBuildStep, error) {
	builder := NewJavaImageBuildStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// JavaImageBuildStepBuilder builder
type JavaImageBuildStepBuilder struct {
	step         *JavaImageBuildStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewJavaImageBuildStepBuilder 创建构建器
func NewJavaImageBuildStepBuilder() *JavaImageBuildStepBuilder {
	return &JavaImageBuildStepBuilder{
		step: new(JavaImageBuildStep),
	}
}

func (g *JavaImageBuildStepBuilder) WithOpt(opt step.CreateStepOption) *JavaImageBuildStepBuilder {
	g.opt = opt
	return g
}

func (g *JavaImageBuildStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *JavaImageBuildStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *JavaImageBuildStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[JavaImageBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:    common.GetJavaStepImage(g.step.Config.JdkVersion),
		Services: []agent.Service{agent.DockerService}, //依赖的服务
		Caches:   g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *JavaImageBuildStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *JavaImageBuildStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: fmt.Sprintf("%s\n%s", buildDockerLoginCmd(g.step.Config.Credential, g.opt), g.step.Config.Commands),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, actionName), nil
}

// 上传构建物
func (g *JavaImageBuildStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *JavaImageBuildStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *JavaImageBuildStepBuilder) Build() (*JavaImageBuildStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
