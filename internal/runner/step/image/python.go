package image

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	// PythonImageBuildConfig in Golang
	PythonImageBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		//Commands          string
		Artifacts     []agent.Artifact // 需要上传的构建物
		PythonVersion string           `json:"python_version" mapstructure:"python_version"`
		//docker凭证
		Credential string //docker凭证
	}
	// PythonImageBuildStep in Node
	PythonImageBuildStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config PythonImageBuildConfig
	}
)

func (g PythonImageBuildStep) Actions() []action.Action {
	return g.Action
}

func (g PythonImageBuildStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g PythonImageBuildStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g PythonImageBuildConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g PythonImageBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewPythonImageBuildStep(opt step.CreateStepOption) (*PythonImageBuildStep, error) {
	builder := NewPythonImageBuildStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// PythonImageBuildStepBuilder builder
type PythonImageBuildStepBuilder struct {
	commonAction *action.CommonAction
	step         *PythonImageBuildStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
}

// NewPythonImageBuildStepBuilder 创建构建器
func NewPythonImageBuildStepBuilder() *PythonImageBuildStepBuilder {
	return &PythonImageBuildStepBuilder{
		step: new(PythonImageBuildStep),
	}
}

func (g *PythonImageBuildStepBuilder) WithOpt(opt step.CreateStepOption) *PythonImageBuildStepBuilder {
	g.opt = opt
	return g
}

func (g *PythonImageBuildStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *PythonImageBuildStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *PythonImageBuildStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[PythonImageBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:    common.GetPythonStepImage(g.step.Config.PythonVersion),
		Services: []agent.Service{agent.DockerService}, //依赖的服务
		Caches:   g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *PythonImageBuildStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *PythonImageBuildStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: fmt.Sprintf("%s\n%s", buildDockerLoginCmd(g.step.Config.Credential, g.opt), g.step.Config.Commands),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, actionName), nil
}

// 上传构建物
func (g *PythonImageBuildStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *PythonImageBuildStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *PythonImageBuildStepBuilder) Build() (*PythonImageBuildStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
