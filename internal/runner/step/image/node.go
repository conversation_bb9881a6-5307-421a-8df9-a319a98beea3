package image

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	// NodeImageBuildConfig in Golang
	NodeImageBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact // 需要上传的构建物
		NodeVersion       string           `json:"node_version" mapstructure:"node_version"`
		Credential        string           //docker凭证
	}
	// NodeImageBuildStep in Node
	NodeImageBuildStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config NodeImageBuildConfig
	}
)

func (g NodeImageBuildStep) Actions() []action.Action {
	return g.Action
}

func (g NodeImageBuildStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g NodeImageBuildStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g NodeImageBuildConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g NodeImageBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewNodeImageBuildStep(opt step.CreateStepOption) (*NodeImageBuildStep, error) {
	builder := NewNodeImageBuildStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// NodeImageBuildStepBuilder builder
type NodeImageBuildStepBuilder struct {
	step         *NodeImageBuildStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewNodeImageBuildStepBuilder 创建构建器
func NewNodeImageBuildStepBuilder() *NodeImageBuildStepBuilder {
	return &NodeImageBuildStepBuilder{
		step: new(NodeImageBuildStep),
	}
}

func (g *NodeImageBuildStepBuilder) WithOpt(opt step.CreateStepOption) *NodeImageBuildStepBuilder {
	g.opt = opt
	return g
}

func (g *NodeImageBuildStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *NodeImageBuildStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *NodeImageBuildStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[NodeImageBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:    common.GetNodeStepImage(g.step.Config.NodeVersion),
		Services: []agent.Service{agent.DockerService}, //依赖的服务
		Caches:   g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *NodeImageBuildStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *NodeImageBuildStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Env:     step.InjectEnv(g.opt), //注入环境变量
		Command: fmt.Sprintf("%s\n%s", buildDockerLoginCmd(g.step.Config.Credential, g.opt), g.step.Config.Commands),
		PATH:    []string{fmt.Sprintf("%s/node_modules/.bin", common.GetStepContainerWorkspace(g.opt.StepContext.App.Identity))},
	}, actionName), nil
}

// 上传构建物
func (g *NodeImageBuildStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *NodeImageBuildStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *NodeImageBuildStepBuilder) Build() (*NodeImageBuildStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
