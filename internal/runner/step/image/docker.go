package image

import (
	"context"
	"encoding/json"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"
	"strings"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

const actionName = "构建镜像"

type (
	BuildImageConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact //需要下载的构建物
		Credential        string           //docker凭证
		Dockerfile        string           //Dockerfile路径
		ContextPath       string           //./
		Image             string           //app/demo:latest
		Parameter         string           // af
	}
	// BuildImageStep in Golang
	BuildImageStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config BuildImageConfig
	}
	// BuildImageStepBuilder builder
	BuildImageStepBuilder struct {
		step         *BuildImageStep
		opt          step.CreateStepOption
		fs           []func() (action.Action, error)
		commonAction *action.CommonAction
	}
)

func NewBuildImageStep(opt step.CreateStepOption) (*BuildImageStep, error) {
	builder := NewBuildImageStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

func (g BuildImageStep) Actions() []action.Action {
	return g.Action
}

func (g BuildImageStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g BuildImageStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g BuildImageConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g BuildImageConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.Artifacts
}

// NewBuildImageStepBuilder 创建构建器
func NewBuildImageStepBuilder() *BuildImageStepBuilder {
	return &BuildImageStepBuilder{
		step: new(BuildImageStep),
	}
}

func (g *BuildImageStepBuilder) WithOpt(opt step.CreateStepOption) *BuildImageStepBuilder {
	g.opt = opt
	return g
}

func (g *BuildImageStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *BuildImageStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *BuildImageStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[BuildImageConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:    common.GetDockerStepImage(util.SetDefaultIfEmpty(config.Version, "27.3.1")),
		Services: []agent.Service{agent.DockerService}, //依赖的服务
		Caches:   g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *BuildImageStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

//docker buildx build -t devopps/hello-world-multi-arch:v1 \
//--push=true \
//--cache-from type=registry,ref=devopps/hello-world-multi-arch:cache \
//--cache-to type=registry,ref=devopps/hello-world-multi-arch:cache,mode=max \
//--platform linux/amd64,linux/arm64 .

func buildCmd(config BuildImageConfig, opt step.CreateStepOption) string {
	sb := strings.Builder{}
	sb.WriteString(buildDockerLoginCmd(config.Credential, opt))
	//获取访问秘钥
	if c, ok := opt.StepContext.Credentials[config.Credential]; ok {
		var entry agent.DockerCredentialEntry
		err := json.Unmarshal([]byte(c.Value), &entry)
		if err != nil {
			opt.Logger.Errorf("Unmarshal Credential error: %s", err)
			return ""
		}
		//构建命令
		sb.WriteString(fmt.Sprintf("docker buildx build -t %s/%s --push=true", entry.Server, config.Image))
		//自定义参数
		sb.WriteString(fmt.Sprintf(" %s\n", config.Dockerfile))
	} else {
		opt.Logger.Errorf("Credentials not found: %s", config.Credential)
	}
	return sb.String()
}

// 执行命令
func (g *BuildImageStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: buildCmd(g.step.Config, g.opt),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, actionName), nil
}

// 上传构建物
func (g *BuildImageStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *BuildImageStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *BuildImageStepBuilder) Build() (*BuildImageStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
