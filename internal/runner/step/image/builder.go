package image

import (
	"encoding/json"
	"fmt"
	"pipeline/internal/runner/step"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/runner/agent"
	"strings"
)

// injectRegistry injects the Docker registry into the step context environment variables.
func injectRegistry(stepCtx *pipeline.StepContext, credential agent.DockerCredentialEntry) {
	stepCtx.Env["IMAGE_REGISTRY"] = credential.Server
}

// getDockerCredential retrieves the Docker credential entry from the step context.
func getDockerCredential(credential string, opt step.CreateStepOption) agent.DockerCredentialEntry {
	var entry agent.DockerCredentialEntry
	credValue, exist := opt.StepContext.Credentials[credential]
	if exist {
		_ = json.Unmarshal([]byte(credValue.Value), &entry)
	}
	return entry
}

// buildDockerfileCmd builds the command to create a Dockerfile based on the provided context path and Dockerfile path.
func buildDockerLoginCmd(credentialKey string, opt step.CreateStepOption) string {
	var sb strings.Builder
	sb.WriteString("docker -v\n")
	entry := getDockerCredential(credentialKey, opt)
	loginCmd := fmt.Sprintf("echo \"%s\" | docker login --username=%s %s --password-stdin\n",
		entry.Password, entry.Username, entry.Server)
	// mask the password in the logs
	if len(entry.Password) > 0 {
		opt.Logger.AddMask(entry.Password)
	}
	sb.WriteString(loginCmd)
	// inject the registry into the step context
	injectRegistry(opt.StepContext, entry)
	// return the command string
	return sb.String()
}
