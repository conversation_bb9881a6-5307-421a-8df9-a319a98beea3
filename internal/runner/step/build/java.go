package build

import (
	"context"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	// JavaBuildConfig in Golang
	JavaBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact // 需要上传的构建物
		JdkVersion        string           `json:"jdk_version" mapstructure:"jdk_version"`
	}
	// JavaBuildStep in Golang
	JavaBuildStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config JavaBuildConfig
	}
)

func (g JavaBuildStep) Actions() []action.Action {
	return g.Action
}

func (g JavaBuildStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g JavaBuildStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g JavaBuildConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	return ret
}

func (g JavaBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewJavaBuildStep(opt step.CreateStepOption) (*JavaBuildStep, error) {
	builder := NewJavaBuildStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// JavaBuildStepBuilder builder
type JavaBuildStepBuilder struct {
	step         *JavaBuildStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewJavaBuildStepBuilder 创建构建器
func NewJavaBuildStepBuilder() *JavaBuildStepBuilder {
	return &JavaBuildStepBuilder{
		step: new(JavaBuildStep),
	}
}

func (g *JavaBuildStepBuilder) WithOpt(opt step.CreateStepOption) *JavaBuildStepBuilder {
	g.opt = opt
	return g
}

func (g *JavaBuildStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *JavaBuildStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *JavaBuildStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[JavaBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetJavaStepImage(g.step.Config.JdkVersion),
		Caches: g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *JavaBuildStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *JavaBuildStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: g.step.Config.Commands,
		Env:     step.InjectEnv(g.opt), //注入环境变量
		//PATH: []string{
		//	workspace, // for gradle or maven
		//},
	}, "编译构建"), nil
}

// 上传构建物
func (g *JavaBuildStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *JavaBuildStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *JavaBuildStepBuilder) Build() (*JavaBuildStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
