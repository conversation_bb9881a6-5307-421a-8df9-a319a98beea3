package build

import (
	"context"
	"github.com/jinzhu/copier"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/models"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
)

type (
	// KubernetesArtifactBuildConfig in Golang
	KubernetesArtifactBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Kubectl           string           // Kubectl版本
		Content           string           // 部署模板
		Artifacts         []agent.Artifact `json:"artifacts" mapstructure:"artifacts"` // 需要上传的构建物
	}
	// KubernetesArtifactBuildStep in Golang
	KubernetesArtifactBuildStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config KubernetesArtifactBuildConfig
	}
)

func (g KubernetesArtifactBuildStep) Actions() []action.Action {
	return g.Action
}

func (g KubernetesArtifactBuildStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g KubernetesArtifactBuildStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g KubernetesArtifactBuildConfig) GetCredentialKey() []string {
	return nil
}

func (g KubernetesArtifactBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func (g KubernetesArtifactBuildConfig) GetConfigPluginKey() []string {
	return []string{g.Content}
}

func NewKubernetesArtifactBuildStep(opt step.CreateStepOption) (*KubernetesArtifactBuildStep, error) {
	builder := NewKubernetesArtifactBuildStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.render,
			builder.upload,
			builder.clean,
		).Build()
}

// KubernetesArtifactBuildStepBuilder builder
type KubernetesArtifactBuildStepBuilder struct {
	step         *KubernetesArtifactBuildStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewKubernetesArtifactBuildStepBuilder 创建构建器
func NewKubernetesArtifactBuildStepBuilder() *KubernetesArtifactBuildStepBuilder {
	return &KubernetesArtifactBuildStepBuilder{
		step: new(KubernetesArtifactBuildStep),
	}
}

func (g *KubernetesArtifactBuildStepBuilder) WithOpt(opt step.CreateStepOption) *KubernetesArtifactBuildStepBuilder {
	g.opt = opt
	return g
}

func (g *KubernetesArtifactBuildStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *KubernetesArtifactBuildStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *KubernetesArtifactBuildStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[KubernetesArtifactBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetKubernetesStepImage(g.step.Config.Kubectl),
		Caches: g.step.Config.Caches,
	}), nil
}

// 渲染模板
func (g *KubernetesArtifactBuildStepBuilder) render() (action.Action, error) {
	return action.NewDynamicAction("渲染模板", g.commonAction, func(ctx context.Context, ca *action.CommonAction) error {
		stepContext := common.GetStepContextFromCtx(ctx)
		actionLogger := logger.GetLoggerFromCtx(ctx)
		data := make(map[string]any)
		for k, v := range stepContext.Env {
			data[k] = v
		}
		configEntry := stepContext.ConfigPluginMap[g.step.Config.Content]
		var entry models.AppConfigEntry
		err := copier.Copy(&entry, configEntry)
		if err != nil {
			actionLogger.Errorf("拷贝配置失败: %s", err)
			return err
		}
		content, err := util.RenderContent(entry.Content, data)
		if err != nil {
			actionLogger.Errorf("渲染模板失败: %s", err)
			return err
		}
		_, _ = actionLogger.Write([]byte(content + "\n"))
		return g.commonAction.Agent.Copy(ctx, agent.CopyOption{
			Files: []*agent.FileEntry{
				{
					Name: "resource.yaml",
					Body: content,
					Mode: 0o755,
				},
			},
		})
	}), nil
}

// 上传构建物
func (g *KubernetesArtifactBuildStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *KubernetesArtifactBuildStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *KubernetesArtifactBuildStepBuilder) Build() (*KubernetesArtifactBuildStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
