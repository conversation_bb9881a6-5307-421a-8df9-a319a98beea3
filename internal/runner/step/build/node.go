package build

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type ( // NodeBuildConfig in Golang
	NodeBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact // 需要上传的构建物
		NodeVersion       string           `json:"node_version" mapstructure:"node_version"`
		Credential        string           `json:"credential" mapstructure:"credential"` //docker凭证
	}
	// NodeBuildUploadStep in Golang
	NodeBuildUploadStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config NodeBuildConfig
	}
)

func (g NodeBuildUploadStep) Actions() []action.Action {
	return g.Action
}

func (g NodeBuildUploadStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g NodeBuildUploadStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g NodeBuildConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	ret = append(ret, g.Credential)
	return ret
}

func (g NodeBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewNodeBuildUploadStep(opt step.CreateStepOption) (*NodeBuildUploadStep, error) {
	builder := NewNodeBuildUploadStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// NodeBuildUploadStepBuilder builder
type NodeBuildUploadStepBuilder struct {
	step         *NodeBuildUploadStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewNodeBuildUploadStepBuilder 创建构建器
func NewNodeBuildUploadStepBuilder() *NodeBuildUploadStepBuilder {
	return &NodeBuildUploadStepBuilder{
		step: new(NodeBuildUploadStep),
	}
}

func (g *NodeBuildUploadStepBuilder) WithOpt(opt step.CreateStepOption) *NodeBuildUploadStepBuilder {
	g.opt = opt
	return g
}

func (g *NodeBuildUploadStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *NodeBuildUploadStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *NodeBuildUploadStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[NodeBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetNodeStepImage(g.step.Config.NodeVersion),
		Caches: g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *NodeBuildUploadStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *NodeBuildUploadStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: g.step.Config.Commands,
		Env:     step.InjectEnv(g.opt), //注入环境变量
		PATH: []string{
			fmt.Sprintf("%s/node_modules/.bin", common.GetStepContainerWorkspace(g.opt.StepContext.App.Identity)),
		},
	}, "构建产物"), nil
}

// 上传构建物
func (g *NodeBuildUploadStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *NodeBuildUploadStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *NodeBuildUploadStepBuilder) Build() (*NodeBuildUploadStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
