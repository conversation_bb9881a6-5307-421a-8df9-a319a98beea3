package build

import (
	"context"
	"fmt"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	// GolangBuildConfig in Golang
	GolangBuildConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Artifacts         []agent.Artifact `json:"artifacts" mapstructure:"artifacts"`
		GoVersion         string           `json:"go_version" mapstructure:"go_version"`
	}
	// GolangBuildStep in Golang
	GolangBuildStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config GolangBuildConfig
	}
)

func (g GolangBuildStep) Actions() []action.Action {
	return g.Action
}

func (g GolangBuildStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g GolangBuildStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g GolangBuildConfig) GetCredentialKey() []string {
	return nil
}

func (g GolangBuildConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

func NewGolangBuildStep(opt step.CreateStepOption) (*GolangBuildStep, error) {
	builder := NewGolangBuildStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// GolangBuildStepBuilder builder
type GolangBuildStepBuilder struct {
	step         *GolangBuildStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewGolangBuildStepBuilder 创建构建器
func NewGolangBuildStepBuilder() *GolangBuildStepBuilder {
	return &GolangBuildStepBuilder{
		step: new(GolangBuildStep),
	}
}

func (g *GolangBuildStepBuilder) WithOpt(opt step.CreateStepOption) *GolangBuildStepBuilder {
	g.opt = opt
	return g
}

func (g *GolangBuildStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *GolangBuildStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *GolangBuildStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[GolangBuildConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetGolangStepImage(g.step.Config.GoVersion),
		Caches: g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *GolangBuildStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 执行命令
func (g *GolangBuildStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: fmt.Sprintf("%s\n%s", step.BuildGitInsteadOfCmd(g.opt), g.step.Config.Commands),
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "构建应用"), nil
}

// 上传构建物
func (g *GolangBuildStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *GolangBuildStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *GolangBuildStepBuilder) Build() (*GolangBuildStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
