package exec

// 前端项目部署到oss
import (
	"context"
	factory "pipeline/internal/runner/agent"

	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	CustomImageExecShellStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Image             string
		Artifacts         []agent.Artifact //需要上传的构建物
		Credential        string           //docker拉取镜像凭证
		DependArtifact    []agent.Artifact //下载上游的构建物
		User              string           //执行命令的用户
		Clone             bool             //是否克隆代码
	}
	// CustomImageExecShellStep in Golang
	CustomImageExecShellStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config CustomImageExecShellStepConfig
	}
)

func (g CustomImageExecShellStep) Actions() []action.Action {
	return g.Action
}

func (g CustomImageExecShellStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g CustomImageExecShellStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g CustomImageExecShellStepConfig) GetCredentialKey() []string {
	ret := make([]string, 0)
	// 添加docker拉取镜像凭证
	if g.Credential != "" {
		ret = append(ret, g.Credential)
	}
	return ret
}

func (g CustomImageExecShellStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.DependArtifact
}

func NewCustomImageExecShellStep(opt step.CreateStepOption) (*CustomImageExecShellStep, error) {
	builder := NewCustomImageExecShellStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.download,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// CustomImageExecShellStepBuilder builder
type CustomImageExecShellStepBuilder struct {
	step         *CustomImageExecShellStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewCustomImageExecShellStepBuilder 创建构建器
func NewCustomImageExecShellStepBuilder() *CustomImageExecShellStepBuilder {
	return &CustomImageExecShellStepBuilder{
		step: new(CustomImageExecShellStep),
	}
}

func (g *CustomImageExecShellStepBuilder) WithOpt(opt step.CreateStepOption) *CustomImageExecShellStepBuilder {
	g.opt = opt
	return g
}

func (g *CustomImageExecShellStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *CustomImageExecShellStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *CustomImageExecShellStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[CustomImageExecShellStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  g.step.Config.Image,
		Caches: g.step.Config.Caches,
		User:   g.step.Config.User,
	}), nil
}

// 检出代码
func (g *CustomImageExecShellStepBuilder) checkout() (action.Action, error) {
	// 不需要克隆代码
	if !g.step.Config.Clone {
		return nil, nil
	}
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 下载制品
func (g *CustomImageExecShellStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.DependArtifact,
	}), nil
}

// 执行命令
func (g *CustomImageExecShellStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Shell:   g.step.Config.Shell,
		Command: g.step.Config.Commands,
		Env:     step.InjectEnv(g.opt), //注入环境变量
	}, "执行命令"), nil
}

// 上传构建物
func (g *CustomImageExecShellStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *CustomImageExecShellStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *CustomImageExecShellStepBuilder) Build() (*CustomImageExecShellStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
