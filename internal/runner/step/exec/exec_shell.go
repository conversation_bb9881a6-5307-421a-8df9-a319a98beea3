package exec

// 执行命令
import (
	"context"
	factory "pipeline/internal/runner/agent"
	"pipeline/pkg/util"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner/agent"
)

type (
	BuildExecShellStepConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		Clone             bool             `mapstructure:"clone"` //是否克隆代码
		Artifacts         []agent.Artifact //需要上传的构建物
		DependArtifact    []agent.Artifact //下载上游的构建物
	}
	// BuildExecShellStep in Golang
	BuildExecShellStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config BuildExecShellStepConfig
	}
)

func (g BuildExecShellStep) Actions() []action.Action {
	return g.Action
}

func (g BuildExecShellStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g BuildExecShellStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g BuildExecShellStepConfig) GetCredentialKey() []string {
	return nil
}

func (g BuildExecShellStepConfig) GetDownloadArtifacts() []agent.Artifact {
	return g.DependArtifact
}

func NewBuildExecShellStep(opt step.CreateStepOption) (*BuildExecShellStep, error) {
	builder := NewBuildExecShellStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.download,
			builder.exec,
			builder.upload,
			builder.clean,
		).Build()
}

// BuildExecShellStepBuilder builder
type BuildExecShellStepBuilder struct {
	step         *BuildExecShellStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewBuildExecShellStepBuilder 创建构建器
func NewBuildExecShellStepBuilder() *BuildExecShellStepBuilder {
	return &BuildExecShellStepBuilder{
		step: new(BuildExecShellStep),
	}
}

func (g *BuildExecShellStepBuilder) WithOpt(opt step.CreateStepOption) *BuildExecShellStepBuilder {
	g.opt = opt
	return g
}

func (g *BuildExecShellStepBuilder) WithActionFn(sf ...func() (action.Action, error)) *BuildExecShellStepBuilder {
	g.fs = append(g.fs, sf...)
	return g
}

// 准备环境
func (g *BuildExecShellStepBuilder) prepare() (action.Action, error) {
	// 获取配置
	config, err := step.GetStepConfig[BuildExecShellStepConfig](g.opt)
	if err != nil {
		return nil, err
	}
	// 配置解析
	g.step.Config = config
	// agent实例
	g.step.Agent = factory.NewAgent(config.Run.Agent)
	// 获取配置
	g.commonAction = action.NewCommonAction(g.step.Agent)
	// 返回准备action
	return action.NewPrepareAction(g.commonAction, agent.PrepareOption{
		Image:  common.GetExecStepImage(util.SetDefaultIfEmpty(config.Version, "22.04")),
		Caches: g.step.Config.Caches,
	}), nil
}

// 检出代码
func (g *BuildExecShellStepBuilder) checkout() (action.Action, error) {
	// 不需要克隆代码
	if !g.step.Config.Clone {
		return nil, nil
	}
	return step.NewDefaultCheckoutAction(g.commonAction, g.opt), nil
}

// 下载构建物
func (g *BuildExecShellStepBuilder) download() (action.Action, error) {
	return action.NewDownloadAction(g.commonAction, agent.DownloadOption{
		Artifacts: g.step.Config.DependArtifact,
	}), nil
}

// 执行命令
func (g *BuildExecShellStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(g.commonAction, agent.ExecOption{
		Env:     step.InjectEnv(g.opt), //注入环境变量
		Shell:   g.step.Config.Shell,
		Command: g.step.Config.Commands,
	}, "执行命令"), nil
}

// 上传构建物
func (g *BuildExecShellStepBuilder) upload() (action.Action, error) {
	return action.NewUploadArtifactAction(g.commonAction, agent.UploadOption{
		Artifacts: g.step.Config.Artifacts,
	}), nil
}

// 清理工作
func (g *BuildExecShellStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(g.commonAction), nil
}

// Build 返回完整step
func (g *BuildExecShellStepBuilder) Build() (*BuildExecShellStep, error) {
	for _, f := range g.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		g.step.Action = append(g.step.Action, act)
	}
	return g.step, nil
}
