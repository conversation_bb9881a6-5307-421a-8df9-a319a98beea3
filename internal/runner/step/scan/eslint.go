package scan

import (
	"archive/tar"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/runner/artifact"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner"
	"pipeline/pkg/util"
)

type (

	// ESLintScanConfig in Golang
	ESLintScanConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		//Commands          string               `json:"commands" mapstructure:"commands"`
		NodeVersion string               `json:"node_version" mapstructure:"node_version"`
		Checkpoints []runner.Checkpoints `json:"checkpoints" mapstructure:"checkpoints"`
	}

	// ESLintScanStep in Golang
	ESLintScanStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config ESLintScanConfig
	}

	ESLintReportEntry struct {
		FilePath            string `json:"filePath"`
		Messages            []any  `json:"messages"`
		SuppressedMessages  []any  `json:"suppressedMessages"`
		ErrorCount          int    `json:"errorCount"`
		FatalErrorCount     int    `json:"fatalErrorCount"`
		WarningCount        int    `json:"warningCount"`
		FixableErrorCount   int    `json:"fixableErrorCount"`
		FixableWarningCount int    `json:"fixableWarningCount"`
		UsedDeprecatedRules []any  `json:"usedDeprecatedRules"`
	}
)

func parseESLintReport(rc io.ReadCloser) (*ReportIssues, error) {
	defer func(rc io.ReadCloser) {
		_ = rc.Close()
	}(rc)
	// Create a new tar reader
	tr := tar.NewReader(rc)
	// Read the first (and only) file from the tar archive
	_, err := tr.Next()
	if err != nil {
		return nil, err
	}
	// Read the file content
	contentBytes, err := io.ReadAll(tr)
	if err != nil {
		return nil, err
	}
	var reportRows []ESLintReportEntry
	err = json.Unmarshal(contentBytes, &reportRows)
	if err != nil {
		return nil, err
	}
	//分类
	reportIssues := ReportIssues{}
	for _, issue := range reportRows {
		reportIssues.Blocker += issue.FatalErrorCount
		reportIssues.Critical += issue.ErrorCount
		reportIssues.Major += issue.WarningCount
	}
	reportIssues.Total = reportIssues.Blocker + reportIssues.Critical + reportIssues.Major
	return &reportIssues, nil
}

func (g ESLintScanStep) Actions() []action.Action {
	return g.Action
}

func (g ESLintScanStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g ESLintScanStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g ESLintScanConfig) GetCredentialKey() []string {
	return nil
}

// ... existing imports ...

// ESLintScanStepBuilder builder
type ESLintScanStepBuilder struct {
	step         *ESLintScanStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewESLintScanStepBuilder 创建构建器
func NewESLintScanStepBuilder() *ESLintScanStepBuilder {
	return &ESLintScanStepBuilder{
		step: new(ESLintScanStep),
	}
}

func (b *ESLintScanStepBuilder) WithOpt(opt step.CreateStepOption) *ESLintScanStepBuilder {
	b.opt = opt
	return b
}

func (b *ESLintScanStepBuilder) WithActionFn(fs ...func() (action.Action, error)) *ESLintScanStepBuilder {
	b.fs = append(b.fs, fs...)
	return b
}

// Build 返回完整step
func (b *ESLintScanStepBuilder) Build() (*ESLintScanStep, error) {
	for _, f := range b.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		b.step.Action = append(b.step.Action, act)
	}
	return b.step, nil
}

// 准备环境
func (b *ESLintScanStepBuilder) prepare() (action.Action, error) {
	config, err := step.GetStepConfig[ESLintScanConfig](b.opt)
	if err != nil {
		return nil, err
	}
	b.step.Config = config
	b.step.Agent = factory.NewAgent(config.Run.Agent)
	b.commonAction = action.NewCommonAction(b.step.Agent)
	return action.NewPrepareAction(b.commonAction, agent.PrepareOption{
		Image:  common.GetNodeStepImage(config.NodeVersion),
		Caches: config.Caches,
	}), nil
}

// 克隆代码
func (b *ESLintScanStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(b.commonAction, b.opt), nil
}

// 执行扫描
func (b *ESLintScanStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(b.commonAction, agent.ExecOption{
		Shell:   b.step.Config.Shell,
		Command: b.step.Config.Commands,
		PATH: []string{
			fmt.Sprintf("%s/node_modules/.bin", common.GetStepContainerWorkspace(b.opt.StepContext.App.Identity)),
		},
		Env: step.InjectEnv(b.opt),
	}, "代码扫描"), nil
}

// 上传报告
func (b *ESLintScanStepBuilder) upload() (action.Action, error) {
	indexKey := b.opt.StepContext.Index.GetIndexKey()
	return action.NewUploadArtifactAction(b.commonAction, agent.UploadOption{
		Artifacts: []agent.Artifact{
			{
				Name:  fmt.Sprintf("%s-report.json", indexKey),
				Path:  "report.json",
				UnZip: true,
			}, {
				Name:  fmt.Sprintf("%s-report.html", indexKey),
				Path:  "report.html",
				UnZip: true,
			},
		},
	}), nil
}

// 检查红线
func (b *ESLintScanStepBuilder) threshold() (action.Action, error) {
	return action.NewDynamicAction("检查红线", b.commonAction, func(ctx context.Context, ca *action.CommonAction) error {
		archive, err := ca.Agent.GetArchive(ctx, agent.ArchiveOption{Path: "report.json"})
		if err != nil {
			return err
		}
		logFromCtx := logger.GetLoggerFromCtx(ctx)
		report, err := parseESLintReport(archive)
		if err != nil {
			logFromCtx.Errorf("解析报告失败: %v", err)
			return err
		}
		// 报告地址
		report.ReportURL = artifact.FormatTempArtifactPath(b.opt.StepContext,
			fmt.Sprintf("%s-report.html/report.html", b.opt.StepContext.Index.GetIndexKey()))

		ca.Response.Content["REPORT"] = report
		logFromCtx.Infof("解析报告信息：%+v", report)

		for _, checkpoint := range b.step.Config.Checkpoints {
			logFromCtx.Infof("检查红线：%s %s %d", checkpoint.Key, checkpoint.RelationalOperator, checkpoint.Value)
			value := report.Mapping(checkpoint.Key)
			ok, err := util.Eval(checkpoint.RelationalOperator, value, checkpoint.Value)
			if err != nil {
				return err
			}
			if !ok {
				message := fmt.Sprintf("代码扫描红线检查无法通过 %s 实际值：%d %s 预期值：%d, ",
					checkpoint.Key, value, checkpoint.LogicalOperator, checkpoint.Value)
				logFromCtx.Error(message)
				return errors.New(message)
			}
		}
		logFromCtx.Success("检查红线通过")
		return nil
	}), nil
}

// 清理工作
func (b *ESLintScanStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(b.commonAction), nil
}

func NewESLintScanStep(opt step.CreateStepOption) (*ESLintScanStep, error) {
	builder := NewESLintScanStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.threshold,
			builder.clean,
		).Build()
}
