package scan

import (
	"archive/tar"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	factory "pipeline/internal/runner/agent"
	"pipeline/internal/runner/logger"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/runner/artifact"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner"
	"pipeline/pkg/util"
)

type (
	// GolangScanConfig in Golang
	GolangScanConfig struct {
		step.CommonConfig `mapstructure:",squash"`
		GoVersion         string               `json:"go_version" mapstructure:"go_version"`
		Checkpoints       []runner.Checkpoints `json:"checkpoints" mapstructure:"checkpoints"`
	}
	// GolangCodeScanStep in Golang
	GolangCodeScanStep struct {
		Agent  agent.Agent
		Action []action.Action
		Config GolangScanConfig
	}
)

func (g GolangCodeScanStep) Actions() []action.Action {
	return g.Action
}

func (g GolangCodeScanStep) Finalize(ctx context.Context) error {
	return g.Agent.Clean(ctx)
}

func (g GolangCodeScanStep) CommonConfig() *step.CommonConfig {
	return &g.Config.CommonConfig
}

func (g GolangScanConfig) GetCredentialKey() []string {
	return nil
}

type GolangScanStepBuilder struct {
	//step.CacheBuilder
	step         *GolangCodeScanStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
}

// NewGolangScanStepBuilder 创建构建器
func NewGolangScanStepBuilder() *GolangScanStepBuilder {
	return &GolangScanStepBuilder{
		step: new(GolangCodeScanStep),
	}
}

func (b *GolangScanStepBuilder) WithOpt(opt step.CreateStepOption) *GolangScanStepBuilder {
	b.opt = opt
	return b
}

func (b *GolangScanStepBuilder) WithActionFn(fs ...func() (action.Action, error)) *GolangScanStepBuilder {
	b.fs = append(b.fs, fs...)
	return b
}

// Build 返回完整step
func (b *GolangScanStepBuilder) Build() (*GolangCodeScanStep, error) {
	for _, f := range b.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		b.step.Action = append(b.step.Action, act)
	}
	return b.step, nil
}

// 准备环境
func (b *GolangScanStepBuilder) prepare() (action.Action, error) {
	config, err := step.GetStepConfig[GolangScanConfig](b.opt)
	if err != nil {
		return nil, err
	}
	b.step.Config = config
	b.step.Agent = factory.NewAgent(config.Run.Agent)
	b.commonAction = action.NewCommonAction(b.step.Agent)
	return action.NewPrepareAction(b.commonAction, agent.PrepareOption{
		Image:  common.GetGolangStepImage(config.GoVersion),
		Caches: config.Caches,
	}), nil
}

// 克隆代码
func (b *GolangScanStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(b.commonAction, b.opt), nil
}

// 执行扫描
func (b *GolangScanStepBuilder) exec() (action.Action, error) {
	return action.NewExecActionWithName(b.commonAction, agent.ExecOption{
		Shell:   b.step.Config.Shell,
		Command: step.BuildGitInsteadOfCmd(b.opt) + "\n" + b.step.Config.Commands,
		Env:     step.InjectEnv(b.opt),
	}, "代码扫描"), nil
}

// 上传报告
func (b *GolangScanStepBuilder) upload() (action.Action, error) {
	indexKey := b.opt.StepContext.Index.GetIndexKey()
	return action.NewUploadArtifactAction(b.commonAction, agent.UploadOption{
		Artifacts: []agent.Artifact{
			//{
			//	Name:  fmt.Sprintf("%s-report.json", indexKey),
			//	Path:  "report.json",
			//	UnZip: true,
			//},
			{
				Name:  fmt.Sprintf("%s-report.html", indexKey),
				Path:  "report.html",
				UnZip: true,
			},
		},
	}), nil
}

// 检查红线
func (b *GolangScanStepBuilder) threshold() (action.Action, error) {
	return action.NewDynamicAction("检查红线", b.commonAction, func(ctx context.Context, ca *action.CommonAction) error {
		archive, err := ca.Agent.GetArchive(ctx, agent.ArchiveOption{
			Path: "report.json",
		})
		if err != nil {
			return err
		}
		logFromCtx := logger.GetLoggerFromCtx(ctx)
		report, err := parseGolangScanReport(archive)
		if err != nil {
			return err
		}
		// 报告地址
		report.ReportURL = artifact.FormatTempArtifactPath(b.opt.StepContext,
			fmt.Sprintf("%s-report.html/report.html", b.opt.StepContext.Index.GetIndexKey()))

		ca.Response.Content["REPORT"] = report
		logFromCtx.Infof("解析报告信息：%+v", report)

		// 检查红线
		for _, checkpoint := range b.step.Config.Checkpoints {
			logFromCtx.Infof("检查红线：%s %s %d",
				checkpoint.Key, checkpoint.RelationalOperator, checkpoint.Value)
			value := report.Mapping(checkpoint.Key)
			ok, err := util.Eval(checkpoint.RelationalOperator, value, checkpoint.Value)
			if err != nil {
				return err
			}
			if !ok {
				message := fmt.Sprintf("代码扫描红线检查无法通过 %s 实际值：%d %s 预期值：%d, ",
					checkpoint.Key, value, checkpoint.LogicalOperator, checkpoint.Value)
				logFromCtx.Error(message)
				return errors.New(message)
			}
		}
		logFromCtx.Success("检查红线通过")
		return nil
	}), nil
}

// 清理工作
func (b *GolangScanStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(b.commonAction), nil
}

func NewGolangCodeScanStep(opt step.CreateStepOption) (*GolangCodeScanStep, error) {
	builder := NewGolangScanStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.exec,
			builder.upload,
			builder.threshold,
			builder.clean,
		).Build()
}

var checksMap = map[string][]string{
	//严重问题数
	"Blocker": {
		"errcheck",
		"gosec",
		"govet",
	},
	//主要问题数
	"Critical": {
		"gocyclo",
		"unparam",
		"staticcheck",
	},
	//次要问题数
	"Major": {
		"gosimple",
		"ineffassign",
		"unused",
		"typecheck",
		"bodyclose",
		"unconvert",
		"depguard",
		"goconst",
		"gomnd",
		"gofmt",
		"lll",
		"misspell",
	},
	//总数
	"Total": {},
}

type ReportIssues struct {
	Total     int    `json:"total"`
	Blocker   int    `json:"blocker"`
	Critical  int    `json:"critical"`
	Major     int    `json:"major"`
	ReportURL string `json:"reportURL"` //扫描报告地址
}

func (r ReportIssues) Mapping(key string) int {
	switch key {
	case "TOTAL":
		return r.Total
	case "BLOCKER":
		return r.Blocker
	case "CRITICAL":
		return r.Critical
	case "MAJOR":
		return r.Major
	default:
		return 0
	}
}

type GolangCILintReport struct {
	Issues []Issues `json:"Issues"`
	Report Report   `json:"Report"`
}

type Pos struct {
	Filename string `json:"Filename"`
	Offset   int    `json:"Offset"`
	Line     int    `json:"Line"`
	Column   int    `json:"Column"`
}

type Issues struct {
	FromLinter           string   `json:"FromLinter"`
	Text                 string   `json:"Text"`
	Severity             string   `json:"Severity"`
	SourceLines          []string `json:"SourceLines"`
	Replacement          any      `json:"Replacement"`
	Pos                  Pos      `json:"Pos"`
	ExpectNoLint         bool     `json:"ExpectNoLint"`
	ExpectedNoLintLinter string   `json:"ExpectedNoLintLinter"`
}

type Linters struct {
	Name             string `json:"Name"`
	Enabled          bool   `json:"Enabled,omitempty"`
	EnabledByDefault bool   `json:"EnabledByDefault,omitempty"`
}

type Report struct {
	Linters []Linters `json:"Linters"`
}

func parseGolangScanReport(rc io.ReadCloser) (*ReportIssues, error) {
	defer func(rc io.ReadCloser) {
		_ = rc.Close()
	}(rc)
	// Create a new tar reader
	tr := tar.NewReader(rc)
	// Read the first (and only) file from the tar archive
	_, err := tr.Next()
	if err != nil {
		return nil, err
	}
	// Read the file content
	contentBytes, err := io.ReadAll(tr)
	if err != nil {
		return nil, err
	}
	var report GolangCILintReport
	err = json.Unmarshal(contentBytes, &report)
	if err != nil {
		return nil, err
	}
	//分类
	reportIssues := ReportIssues{}
	for _, issue := range report.Issues {
		for key, checks := range checksMap {
			for _, check := range checks {
				if issue.FromLinter == check {
					switch key {
					case "Blocker":
						reportIssues.Blocker++
					case "Critical":
						reportIssues.Critical++
					case "Major":
						reportIssues.Major++
					}
				}
			}
		}
	}
	reportIssues.Total = reportIssues.Blocker + reportIssues.Critical + reportIssues.Major
	return &reportIssues, nil
}
