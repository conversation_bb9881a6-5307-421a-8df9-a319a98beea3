package scan

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/go-resty/resty/v2"
	"io"
	"net/http"
	"os"
	"path"
	factory "pipeline/internal/runner/agent"
	"strconv"
	"strings"
	"time"

	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/runner"
	"pipeline/pkg/runner/agent"

	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"github.com/magiconair/properties"
)

const (
	timeout        = 3600
	projectConfig  = "sonar-project.properties"
	scannerConfig  = "sonar-scanner.properties"
	reportFileName = "work/report-task.txt"
)

type SonarqubeConfig struct {
	step.CommonConfig `mapstructure:",squash"`
	QualityGates      []runner.Checkpoints `json:"quality-gates" mapstructure:"quality-gates"`
}

type HistoryScan struct {
	CoverageDelta    bool `json:"coverage-delta" mapstructure:"coverage-delta"`
	DuplicationDelta bool `json:"duplication-delta" mapstructure:"duplication-delta"`
}

type SonarQubeStep struct {
	Config SonarqubeConfig
	Env    []string
	Name   string
	Agent  agent.Agent
	Action []action.Action
}

type SonarServer struct {
	*Client
	Endpoint       string
	Token          string
	ProjectKey     string
	ProjectVersion string
	Workspace      string
	SonarWorkPath  string
	StepRepoPath   string
	Timeout        int
}

func (s SonarQubeStep) Actions() []action.Action {
	return s.Action
}

func (s SonarQubeStep) Finalize(ctx context.Context) error {
	return s.Agent.Clean(ctx)
}

func (s SonarQubeStep) CommonConfig() *step.CommonConfig {
	return &s.Config.CommonConfig
}

// GetCredentialKey get credential key, implement StepConfig interface
func (c SonarqubeConfig) GetCredentialKey() []string {
	return nil
}

func (c SonarqubeConfig) GetDownloadArtifacts() []agent.Artifact {
	return nil
}

// SonarQubeStepBuilder builder
type SonarQubeStepBuilder struct {
	step         *SonarQubeStep
	opt          step.CreateStepOption
	fs           []func() (action.Action, error)
	commonAction *action.CommonAction
	sonarServer  *SonarServer
}

// NewSonarQubeStepBuilder 创建构建器
func NewSonarQubeStepBuilder() *SonarQubeStepBuilder {
	return &SonarQubeStepBuilder{
		step: new(SonarQubeStep),
	}
}

func (b *SonarQubeStepBuilder) WithOpt(opt step.CreateStepOption) *SonarQubeStepBuilder {
	b.opt = opt
	return b
}

func (b *SonarQubeStepBuilder) WithActionFn(fs ...func() (action.Action, error)) *SonarQubeStepBuilder {
	b.fs = append(b.fs, fs...)
	return b
}

func NewSonarServer(opt step.CreateStepOption) *SonarServer {

	client := New()
	client.SetBaseUrl(opt.StepContext.Sonar.Endpoint)
	client.SetToken(opt.StepContext.Sonar.Token)
	endpoint := opt.StepContext.Sonar.Endpoint
	projectKey := opt.StepContext.App.Identity
	projectVersion := *opt.StepContext.Checkout.CheckoutSHA
	workspace := common.GetStepContainerWorkspace(opt.StepContext.App.Identity)
	stepRepoPath := common.GetStepWorkspace(opt.StepContext)

	return &SonarServer{
		Client:         client,
		Endpoint:       endpoint,
		Token:          opt.StepContext.Sonar.Token,
		ProjectKey:     projectKey,
		Workspace:      workspace,
		StepRepoPath:   stepRepoPath,
		ProjectVersion: projectVersion,
		Timeout:        timeout,
	}
}

// CheckQualityGate check quality gate result
func CheckQualityGate(ctx context.Context, report *QualityGateReport, checkpoints []runner.Checkpoints) (bool, error) {
	logFromCtx := logger.GetLoggerFromCtx(ctx)
	reportMap := map[string]int{
		"bugs":            report.After.Bugs,
		"vulnerability":   report.After.Vulnerabilities,
		"securityHotspot": report.After.SecurityHotspots,
		"codeSmell":       report.After.CodeSmells,
		"coverage":        int(report.After.Coverage),
		"duplication":     int(report.After.Duplications),
	}

	log.Info("check quality gate", log.Any("report", reportMap), log.Any("checkpoints", checkpoints))
	for _, checkpoint := range checkpoints {
		value := reportMap[checkpoint.Key]
		ok, err := util.Eval(checkpoint.RelationalOperator, value, checkpoint.Value)
		if err != nil {
			return false, err
		}
		if !ok {
			message := fmt.Sprintf("代码扫描检查失败 %s 实际值：%d  预期值：%d", checkpoint.Key, value, checkpoint.Value)
			logFromCtx.Error(fmt.Sprintf("==> ❌ %s", message))
			return false, nil
		}
	}
	return true, nil
}

// LoadPropertyConfig load config from property file
func LoadPropertyConfig(file string, obj any) error {
	content, err := os.ReadFile(file)
	if err != nil {
		log.Error("read config failed", log.Any("file", file), log.Any("err", err))
		return err
	}
	p, err := properties.LoadString(string(content))
	if err != nil {
		log.Error("load config failed", log.Any("err", err))
		return err
	}
	if m, ok := obj.(map[string]string); ok {
		for k, v := range p.Map() {
			m[k] = v
		}
	} else {
		err = p.Decode(obj)
		if err != nil {
			log.Error("decode config failed", log.Any("err", err))
			return err
		}
	}

	return nil
}

func (s *SonarServer) LoadReportFile() (*ReportInfo, error) {
	reportInfo := &ReportInfo{}
	reportFilePath := path.Join(s.StepRepoPath, reportFileName)
	if !util.IsFile(reportFilePath) {
		err := fmt.Errorf("sonar report file not found, path: %s", reportFilePath)
		log.Error("get ReportInfo error", log.Any("err", err))
		return nil, err
	}
	if err := LoadPropertyConfig(reportFilePath, reportInfo); err != nil {
		return nil, err
	}
	log.Info("get reportInfo success", log.Any("reportInfo", reportInfo))
	return reportInfo, nil
}

// Build 返回完整step
func (b *SonarQubeStepBuilder) Build() (*SonarQubeStep, error) {
	for _, f := range b.fs {
		act, err := f()
		if err != nil {
			return nil, err
		}
		if act == nil {
			continue
		}
		b.step.Action = append(b.step.Action, act)
	}
	return b.step, nil
}

// 准备环境
func (b *SonarQubeStepBuilder) prepare() (action.Action, error) {
	// step config
	config, err := step.GetStepConfig[SonarqubeConfig](b.opt)
	if err != nil {
		return nil, err
	}
	b.step.Config = config
	b.step.Agent = factory.NewAgent(config.Run.Agent)
	b.commonAction = action.NewCommonAction(b.step.Agent)
	// sonar server
	b.sonarServer = NewSonarServer(b.opt)

	return action.NewPrepareAction(b.commonAction, agent.PrepareOption{
		Image:  common.GetJavaStepImage(util.SetDefaultIfEmpty(config.Version, "17.0.2")),
		Caches: config.Caches,
	}), nil
}

// 克隆代码
func (b *SonarQubeStepBuilder) checkout() (action.Action, error) {
	return step.NewDefaultCheckoutAction(b.commonAction, b.opt), nil
}

// 配置扫描
func (b *SonarQubeStepBuilder) initialize() (action.Action, error) {
	return action.NewDynamicAction("配置扫描", b.commonAction,
		func(ctx context.Context, commonAction *action.CommonAction) error {
			logFromCtx := logger.GetLoggerFromCtx(ctx)
			logFromCtx.Info("config sonarqube ...")
			s := b.sonarServer
			config := map[string]string{
				"sonar.host.url":       s.Endpoint,
				"sonar.token":          s.Token,
				"sonar.sourceEncoding": "UTF-8",
				"sonar.sources":        s.Workspace,
				"sonar.projectKey":     s.ProjectKey,
				"sonar.projectVersion": s.ProjectVersion,
			}
			// merge the project configuration under the code root directory
			sourceConfigFile := path.Join(s.StepRepoPath, projectConfig)
			if util.IsFile(sourceConfigFile) {
				sourceConfig := map[string]string{}
				_ = LoadPropertyConfig(sourceConfigFile, sourceConfig)
				util.MergeMap(config, sourceConfig)
			}

			var builder strings.Builder
			for k, v := range config {
				builder.WriteString(fmt.Sprintf("%s=%s\n", k, v))
			}
			content := strings.Trim(builder.String(), "\n")
			if err := b.commonAction.Agent.Copy(ctx, agent.CopyOption{
				DestPath: b.sonarServer.Workspace,
				Files: []*agent.FileEntry{
					{
						Name: scannerConfig,
						Body: content,
						Mode: 0o644,
					},
				},
			}); err != nil {
				return err
			}
			logFromCtx.Success("write sonar config file success")
			return nil
		}), nil
}

// 执行扫描
func (b *SonarQubeStepBuilder) scan() (action.Action, error) {
	b.opt.StepContext.Env["SYS_CONFIG"] = path.Join(b.sonarServer.Workspace, scannerConfig)
	b.opt.StepContext.Env["WORK_PATH"] = path.Join(b.sonarServer.Workspace, "work")
	return action.NewExecActionWithName(b.commonAction, agent.ExecOption{
		Command: "sonar-scanner -Dproject.settings=\"${SYS_CONFIG}\" -Dsonar.working.directory=\"${WORK_PATH}\"",
		Env:     step.InjectEnv(b.opt),
	}, "代码扫描"), nil
}

// 等待结果
func (b *SonarQubeStepBuilder) wait() (action.Action, error) {
	return action.NewDynamicAction("等待结果", b.commonAction,
		func(ctx context.Context, ca *action.CommonAction) error {
			logFromCtx := logger.GetLoggerFromCtx(ctx)
			reportInfo, err := b.sonarServer.LoadReportFile()
			if err != nil {
				return err
			}

			logFromCtx.Info("waiting for sonar finish analysis ...")
			var details ComputeEngineTaskResponse
			reqData := GetCETaskOptions{}
			reqData.ID = reportInfo.TaskID
			reqData.ProjectKey = reportInfo.ProjectKey
			if reqData.ID == "" || reqData.ProjectKey == "" {
				err = errors.New("sonar report task id or project key is empty")
				logFromCtx.Error(fmt.Sprintf("wait sonar analisis error: %v", err))
				return err
			}

			to := time.NewTimer(time.Second * time.Duration(b.sonarServer.Timeout))
			tick := time.NewTicker(time.Second * 5)
			for {
				details, err = b.sonarServer.GetTaskStatus(ctx, reqData)
				if err != nil {
					logFromCtx.Error(fmt.Sprintf("get sonar task status error: %v", err))
					return err
				}
				switch details.Task.Status {
				case SuccessCeTaskStatus:
					logFromCtx.Success("sonar analysis success")
					// 获取扫描结果
					return b.threshold(ctx, ca)
				case FailedCeTaskStatus, CanceledCeTaskStatus:
					err = fmt.Errorf("sonar analysis failed/cancelled, status: %q", details.Task.ErrorMessage)
					logFromCtx.Error(fmt.Sprintf("sonar analysis failed: %v", err))
					return err
				default:
					log.Info("wait analysis ...", log.Any("projectKey", reqData.ProjectKey),
						log.Any("task id", details.Task.Id), log.Any("status", details.Task.Status))
				}

				select {
				case <-tick.C:
					continue
				case <-to.C:
					err = errors.New("wait sonar analysis timeout")
					log.Error("wait sonar analysis timeout", log.Any("projectKey", reqData.ProjectKey),
						log.Any("task id", details.Task.Id))
					return err
				}
			}
		},
	), nil
}

// 获取结果
func (b *SonarQubeStepBuilder) threshold(ctx context.Context, ca *action.CommonAction) error {
	logFromCtx := logger.GetLoggerFromCtx(ctx)

	reportInfo, err := b.sonarServer.LoadReportFile()
	if err != nil {
		return err
	}

	params := ComponentMeasuresOptions{}
	params.ProjectKey = reportInfo.ProjectKey
	params.MetricKeys = metricsKeys

	measure, err := b.sonarServer.ComponentMeasures(ctx, params)
	if err != nil {
		if !IsNotFoundErr(err) {
			logFromCtx.Error(fmt.Sprintf("get sonar measures error: %v", err))
		}
		return err
	}
	if len(measure.Component.Measures) == 0 {
		log.Warn("measures not found")
		return nil
	}

	report := ConvertToQualityGate(measure)
	reportMap := report.ToMap()
	reportMap["projectKey"] = reportInfo.ProjectKey
	reportMap["reportURL"] = reportInfo.DashboardURL
	reportMap["taskID"] = reportInfo.TaskID
	ca.Response.Content["REPORT"] = reportMap
	isPass, err := CheckQualityGate(ctx, report, b.step.Config.QualityGates)
	if err != nil {
		logFromCtx.Error(fmt.Sprintf("quality gate check error: %v", err))
		return err
	}
	if !isPass {
		logFromCtx.Error("quality gate check failed")
		return errors.New("quality gate check failed")
	}

	logFromCtx.Info("quality gate check passed")
	logFromCtx.Infof("sonar report: %v", log.Any("report", reportMap))
	return nil
}

// 清理工作
func (b *SonarQubeStepBuilder) clean() (action.Action, error) {
	return action.NewCleanAction(b.commonAction), nil
}

func NewSonarQubeStep(opt step.CreateStepOption) (*SonarQubeStep, error) {
	builder := NewSonarQubeStepBuilder()
	return builder.WithOpt(opt).
		WithActionFn(
			builder.prepare,
			builder.checkout,
			builder.initialize,
			builder.scan,
			builder.wait,
			builder.clean,
		).Build()
}

type Client struct {
	BaseUrl string
	Token   string
	*resty.Client
}

func New() *Client {
	return &Client{
		Client: resty.New().SetDisableWarn(true),
	}
}

func (c *Client) SetBaseUrl(url string) {
	c.BaseUrl = url
}

func (c *Client) SetToken(token string) {
	c.Token = base64.StdEncoding.EncodeToString([]byte(token + ":"))
}

func (c *Client) request(ctx context.Context) *resty.Request {
	return c.R().SetContext(ctx).
		SetHeader("Authorization", "Basic "+c.Token)
}

func (c *Client) url(subPath string) string {
	return fmt.Sprintf("%s/%s", c.BaseUrl, subPath)
}

func (c *Client) GetTaskStatus(ctx context.Context, opts GetCETaskOptions) (details ComputeEngineTaskResponse, err error) {
	resp, err := c.request(ctx).
		SetResult(&details).
		SetQueryParams(opts.GetQueryParams()).
		Get(c.url("api/ce/task"))
	return details, c.checkErr(resp, err)
}

func (c *Client) ComponentMeasures(ctx context.Context, opts ComponentMeasuresOptions) (measure Measure, err error) {
	resp, err := c.request(ctx).
		SetResult(&measure).
		SetQueryParams(opts.GetQueryParams()).
		Get(c.url("api/measures/component"))
	return measure, c.checkErr(resp, err)
}

// checkError checks response error.
func (c *Client) checkErr(resp *resty.Response, err error) error {
	if err != nil {
		return err
	}
	if resp == nil || resp.RawResponse == nil {
		return errors.New("empty response error")
	}

	if resp.RawResponse.StatusCode >= http.StatusBadRequest {
		data, _ := io.ReadAll(resp.RawResponse.Body)
		return &ResponseErr{
			StatusCode: resp.RawResponse.StatusCode,
			Message:    string(data),
		}
	}
	return nil
}

var metricsKeys = []string{
	"alert_status",

	// "new_reliability_rating", "new_maintainability_rating", "new_security_rating", "new_security_review_rating",
	"reliability_rating", "sqale_rating", "security_rating", "security_review_rating",

	"bugs", "code_smells", "vulnerabilities", "security_hotspots",
	"new_bugs", "new_code_smells", "new_vulnerabilities", "new_security_hotspots",

	"lines_to_cover", "coverage", "duplicated_lines", "duplicated_lines_density",
	"new_lines_to_cover", "new_coverage", "new_duplicated_lines", "new_duplicated_lines_density",
}

type Conditions struct {
	Bugs             int     `json:"bugs"`
	Vulnerabilities  int     `json:"vulnerabilities"`
	CodeSmells       int     `json:"codeSmells"`
	SecurityHotspots int     `json:"securityHotspots"`
	Duplications     float64 `json:"duplications"`
	DuplicatedLines  int     `json:"duplicatedLines"`
	Coverage         float64 `json:"coverage"`
	LinesToCover     int     `json:"linesToCover"`
}

type Rating struct {
	Value float64 `json:"rating"`
	Class string  `json:"class"`
}

type ReportInfo struct {
	ProjectKey   string `properties:"projectKey"`
	ServerUrl    string `properties:"serverUrl"`
	DashboardURL string `properties:"dashboardUrl"`
	TaskID       string `properties:"ceTaskId"`
	TaskURL      string `properties:"ceTaskUrl"`
}

type QualityGateReport struct {
	New   Conditions `json:"new"`
	After Conditions `json:"after"`

	Status   string `json:"status"`
	CodeSize int    `json:"codeSize"`

	// Ratings
	SecurityReviewRating  Rating `json:"securityReviewRating"`
	SecurityRating        Rating `json:"securityRating"`
	MaintainabilityRating Rating `json:"maintainabilityRating"`
	ReliabilityRating     Rating `json:"reliabilityRating"`
}

func (q *QualityGateReport) ToMap() map[string]string {
	if q == nil {
		return map[string]string{}
	}
	return map[string]string{
		// 总问题数
		"bugs": strconv.Itoa(q.After.Bugs),
		// 问题数评级
		"bugsRate": q.ReliabilityRating.Class,
		// 漏洞数
		"vulnerability": strconv.Itoa(q.After.Vulnerabilities),
		// 漏洞数评级
		"vulnerabilityRate": q.SecurityRating.Class,
		// 安全热点数
		"securityHotspot": strconv.Itoa(q.After.SecurityHotspots),
		// 安全热点数评级
		"securityHotspotRate": q.SecurityReviewRating.Class,
		// 代码异味数
		"codeSmell": strconv.Itoa(q.After.CodeSmells),
		// 代码异味数评级
		"codeSmellRate": q.MaintainabilityRating.Class,
		// 覆盖率
		"coverage": fmt.Sprintf("%.2f", q.After.Coverage),
		// 新增覆盖率
		"coverageNew": fmt.Sprintf("%.2f", q.New.Coverage),
		// 重复率
		"duplications": fmt.Sprintf("%.2f", q.After.Duplications),
		// 新增重复行数
		"duplicationsNew": fmt.Sprintf("%.2f", q.New.Duplications),
		// 行覆盖数
		"codeSizeLinesOfCode": strconv.Itoa(q.CodeSize),
	}
}

func ConvertToQualityGate(measures Measure) (data *QualityGateReport) {
	report := &QualityGateReport{}

	for _, measure := range measures.Component.Measures {
		switch measure.Metric {
		// status
		case "alert_status":
			report.Status = measure.Value
		// rating
		case "new_security_rating", "security_rating":
			report.SecurityRating = CalculateRating(measure)
		case "new_reliability_rating", "reliability_rating":
			report.ReliabilityRating = CalculateRating(measure)
		case "new_security_review_rating", "security_review_rating":
			report.SecurityReviewRating = CalculateRating(measure)
		case "new_maintainability_rating", "sqale_rating":
			report.MaintainabilityRating = CalculateRating(measure)

		// count metrics
		// new
		case "new_bugs":
			report.New.Bugs = CalculateIntValue(measure)
		case "new_code_smells":
			report.New.CodeSmells = CalculateIntValue(measure)
		case "new_vulnerabilities":
			report.New.Vulnerabilities = CalculateIntValue(measure)
		case "new_security_hotspots":
			report.New.SecurityHotspots = CalculateIntValue(measure)

		// count metrics
		// old or after merge
		case "bugs":
			report.After.Bugs = CalculateIntValue(measure)
		case "code_smells":
			report.After.CodeSmells = CalculateIntValue(measure)
		case "vulnerabilities":
			report.After.Vulnerabilities = CalculateIntValue(measure)
		case "security_hotspots":
			report.After.SecurityHotspots = CalculateIntValue(measure)

		// coverage and duplications
		// new
		case "new_coverage":
			report.New.Coverage = CalculateFloatValue(measure)
		case "new_lines_to_cover":
			report.New.LinesToCover = CalculateIntValue(measure)
		case "new_duplicated_lines":
			report.New.DuplicatedLines = CalculateIntValue(measure)
		case "new_duplicated_lines_density":
			report.New.Duplications = CalculateFloatValue(measure)

		// coverage and duplications
		// after
		case "coverage":
			report.After.Coverage = CalculateFloatValue(measure)
		case "lines_to_cover":
			report.After.LinesToCover = CalculateIntValue(measure)
		case "duplicated_lines":
			report.After.DuplicatedLines = CalculateIntValue(measure)
		case "duplicated_lines_density":
			report.After.Duplications = CalculateFloatValue(measure)
		}
	}

	return report
}

func CalculateRating(data MeasureItem) Rating {
	value := data.Value
	if data.Period != nil {
		value = data.Period.Value
	}
	score, _ := strconv.ParseFloat(value, 64)
	rating := Rating{Value: score}
	if score <= 1 {
		rating.Class = "A"
	} else if score <= 2 {
		rating.Class = "B"
	} else if score <= 3 {
		rating.Class = "C"
	} else if score <= 4 {
		rating.Class = "D"
	} else {
		rating.Class = "E"
	}
	return rating
}

func GetStrValue(data MeasureItem) (value string) {
	value = data.Value
	if value == "" && data.Period != nil {
		value = data.Period.Value
	}
	if value == "" && len(data.Periods) > 0 {
		value = data.Periods[0].Value
	}
	return
}

func CalculateIntValue(data MeasureItem) int {
	value, _ := strconv.Atoi(GetStrValue(data))
	return value
}

func CalculateFloatValue(data MeasureItem) float64 {
	value, _ := strconv.ParseFloat(GetStrValue(data), 64)
	return value
}

// ComputeEngineTaskStatus Compute Engine task status
type ComputeEngineTaskStatus string

const (
	CanceledCeTaskStatus   = "CANCELED"
	FailedCeTaskStatus     = "FAILED"
	SuccessCeTaskStatus    = "SUCCESS"
	PendingCeTaskStatus    = "PENDING"
	InProgressCeTaskStatus = "IN_PROGRESS"
)

// ComputeEngineTaskResponse Compute Engine task response
type ComputeEngineTaskResponse struct {
	Task ComputeEngineTask `json:"task,omitempty"`
}

// ComputeEngineTask Compute Engine task
type ComputeEngineTask struct {
	// task id
	Id          string `json:"id,omitempty"`
	Type        string `json:"type,omitempty"`
	ComponentId string `json:"componentId,omitempty"`
	// sonarqube component key
	ComponentKey       string `json:"componentKey,omitempty"`
	ComponentName      string `json:"componentName,omitempty"`
	ComponentQualifier string `json:"componentQualifier,omitempty"`

	ErrorMessage string `json:"errorMessage"`
	// analysis id
	AnalysisId string `json:"analysisId,omitempty"`
	// task status
	Status         ComputeEngineTaskStatus `json:"status,omitempty"`
	SubmittedAt    string                  `json:"submittedAt,omitempty"`
	SubmitterLogin string                  `json:"submitterLogin,omitempty"`
	// task start time
	StartedAt string `json:"startedAt,omitempty"`
	// task end time
	ExecutedAt string `json:"executedAt,omitempty"`
	// time consuming to execute
	ExecutionTimeMs   int  `json:"executionTimeMs,omitempty"`
	HasScannerContext bool `json:"hasScannerContext,omitempty"`
	// If the scanned object is a branch, here is the branch name
	Branch string `json:"branch,omitempty"`
}

type Measure struct {
	Component Component `json:"component"`
}

type Component struct {
	Key              string        `json:"key"`
	Name             string        `json:"name"`
	Qualifier        string        `json:"qualifier"`
	Visibility       string        `json:"visibility"`
	LastAnalysisDate string        `json:"lastAnalysisDate"`
	Revision         string        `json:"revision"`
	Measures         []MeasureItem `json:"measures"`
}

type MeasureItem struct {
	Metric  string   `json:"metric,omitempty"`
	Periods []Period `json:"periods"`
	Value   string   `json:"value,omitempty"`
	Period  *Period  `json:"period"`
}

type Period struct {
	Date      string `json:"date,omitempty"`
	Index     int64  `json:"index,omitempty"`
	Mode      string `json:"mode,omitempty"`
	Parameter string `json:"parameter,omitempty"`
	Value     string `json:"value,omitempty"`
}

type ProjectOptions struct {
	ProjectKey string
}

type GetCETaskOptions struct {
	ProjectOptions
	ID string

	AdditionalFields string `json:"additionalFields"`
}

func (opts GetCETaskOptions) GetQueryParams() map[string]string {
	return map[string]string{
		"id":               opts.ID,
		"additionalFields": opts.AdditionalFields,
	}
}

type ComponentMeasuresOptions struct {
	ProjectOptions
	MetricKeys []string
}

func (opts ComponentMeasuresOptions) GetQueryParams() map[string]string {
	base := map[string]string{
		"component":  opts.ProjectKey,
		"metricKeys": strings.Join(opts.MetricKeys, ","),
	}
	return base
}

// IsNotFoundErr returns true if the error is a NotFoundErr.
func IsNotFoundErr(err error) bool {
	if err == nil {
		return false
	}
	var e *ResponseErr
	if errors.As(err, &e) {
		return e.StatusCode == http.StatusNotFound
	}
	return false
}

// ResponseErr describe an error that contains status code and message of a response
type ResponseErr struct {
	StatusCode int
	Message    string
}

// Error implements error interface
func (r *ResponseErr) Error() string {
	if r == nil {
		return ""
	}
	return fmt.Sprintf("response error, status code: %d, msg: %s",
		r.StatusCode,
		r.Message,
	)
}
