package report

import (
	"context"
	"fmt"
	"slices"
	"sync"
	"sync/atomic"
	"time"

	"pipeline/internal/runner/client"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/pkg/pipeline"
	v1 "pipeline/pkg/proto/runner/v1"
	"pipeline/pkg/util"

	"connectrpc.com/connect"
	"git.makeblock.com/makeblock-go/log"
	"github.com/avast/retry-go"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Reporter struct {
	ctx       context.Context
	reportCtx context.Context
	cancel    context.CancelFunc
	task      *pipeline.StepContext

	client  client.Client
	clientM sync.Mutex
	closed  bool

	// 用于上报状态
	state   *v1.StepState
	stateMu sync.RWMutex
	outputs sync.Map
	// 用于记录上报状态的版本
	prevVersion   atomic.Int64
	latestVersion atomic.Int64
	// 当前正在运行的action索引
	actionIndex int

	// 日志
	*logger.Logger
}

func NewReporter(ctx context.Context, cancel context.CancelFunc, client client.Client, task *pipeline.StepContext) *Reporter {
	return &Reporter{
		ctx:       ctx,
		cancel:    cancel,
		client:    client,
		task:      task,
		state:     &v1.StepState{},
		Logger:    logger.NewLogger(task),
		reportCtx: context.Background(),
	}
}

func (r *Reporter) RunDaemon() {
	if r.closed {
		return
	}
	if r.ctx.Err() != nil {
		return
	}
	//log.Printf("reporter run daemon")
	//上报状态
	err := r.ReportState(false)
	if err != nil {
		log.Printf("report state error: %+v", err)
	}
	//上报日志
	_ = r.ReportLog(false)
	//if err != nil {
	//	log.Printf("report logger error: %+v", err)
	//}
	//每秒执行一次
	time.AfterFunc(time.Second, r.RunDaemon)
}

func (r *Reporter) ReportState(force bool) error {
	//log.Printf("report state")
	r.clientM.Lock()
	defer r.clientM.Unlock()

	var req = &v1.UpdateTaskRequest{
		TxUuid:     r.task.TxUUID,
		StageIdx:   uint32(r.task.Index.StageIdx),   //nolint:gosec
		StepRowIdx: uint32(r.task.Index.StepRowIdx), //nolint:gosec
		StepIdx:    uint32(r.task.Index.StepIdx),    //nolint:gosec
	}

	// 如果状态有变化
	latestVersion, prevVersion := r.latestVersion.Load(), r.prevVersion.Load()
	if latestVersion > prevVersion || force {
		r.stateMu.RLock()
		req.State = proto.Clone(r.state).(*v1.StepState)
		r.stateMu.RUnlock()
		// outputs
		outputs := make(map[string]string)
		r.outputs.Range(func(k, v any) bool {
			if val, ok := v.(string); ok {
				outputs[k.(string)] = val
			}
			return true
		})
		req.Outputs = outputs
		req.DoNext = force
	}

	//log.Printf("上报状态：%+v\n", req)

	response, err := r.client.UpdateTask(r.reportCtx, connect.NewRequest(req))
	if err != nil {
		return err
	}
	// 更新outputs（置空已经成功上报过的）
	for _, k := range response.Msg.SentOutputs {
		r.outputs.Store(k, struct{}{})
	}
	//log.Printf("上报完成: latestVersion=%d, prevVersion=%d\n", latestVersion, prevVersion)
	resp := response.Msg
	// 判断是否需要取消当前任务
	if resp.PipelineState == pipeline.Terminated || // 流水线被终止
		resp.StepState == pipeline.Cancel || // step被取消
		resp.StepState == pipeline.Skipped { // step被跳过
		r.cancel()
	}
	// 快速失败（其他step执行失败）
	if resp.PipelineState == pipeline.Failed && r.task.Strategy == pipeline.FastStrategy {
		r.cancel()
	}

	// update version
	r.prevVersion.Store(latestVersion)

	return nil
}

func (r *Reporter) ReportLog(stepNoMore bool) error {

	r.clientM.Lock()
	defer r.clientM.Unlock()

retry:
	// 获取日志
	actionLogger, err := r.Logger.GetReportLogs()

	if err != nil {
		return nil
	}

	actionLogger.LoggerMu.Lock()
	rows := actionLogger.LogRows
	actionNoMore := actionLogger.NoMore
	actionLogger.LoggerMu.Unlock()

	// 如果没有日志需要上报, 判断是否进行上报
	if len(rows) == 0 {
		// 如果当前action没有日志需要上报, 且不再有日志, 更新下一个上传action的日志索引
		if actionNoMore {
			// 检查是否需要更新到下一个action
			if actionLogger.ActionIndex < r.Logger.ActionsSize()-1 {
				// 更新到下一个action上报索引
				r.Logger.LoggerReportIndexIncr()
				log.Printf("current action no logs to report, try next action")
				// 不浪费本次上报机会，直接更新到下一个action进行上报，不返回错误
				goto retry
			}
			// 没有必要更新了, 说明当前step的所有action的日志都已经上报完毕，返回nil结束重试
			return nil
		}
		// 当前action还有日志需要上报, 但是没有日志, 说明日志还没有准备好, 等待下次上报
		return fmt.Errorf("current action no logs to report, wait next time")
	}

	// 上报日志
	req := &v1.UpdateLogRequest{
		TxUuid:     r.task.TxUUID,
		StageIdx:   uint32(r.task.Index.StageIdx),    //nolint:gosec
		StepRowIdx: uint32(r.task.Index.StepRowIdx),  //nolint:gosec
		StepIdx:    uint32(r.task.Index.StepIdx),     //nolint:gosec
		ActionIdx:  uint32(actionLogger.ActionIndex), //nolint:gosec
		Index:      int64(actionLogger.LogOffset),
		Rows:       rows,
		NoMore:     actionNoMore,
	}

	//log.Printf("上报日志：\n%+v", req)

	resp, err := r.client.UpdateLog(r.reportCtx, connect.NewRequest(req))

	//log.Printf("上报完成：%+v", resp)

	if err != nil {
		return err
	}

	// if current action logger oversize, update report index, report next action logs
	if resp.Msg.LogSizeLimit {
		actionLogger.ActionLoggerOversize()
		r.Logger.LoggerReportIndexIncr()
		return nil
	}

	ack := int(resp.Msg.AckIndex)
	if ack < actionLogger.LogOffset {
		return fmt.Errorf("submitted logs are lost")
	}

	actionLogger.LoggerMu.Lock()
	actionLogger.LogRows = actionLogger.LogRows[ack-actionLogger.LogOffset:]
	actionLogger.LogOffset = ack
	actionLogger.LoggerMu.Unlock()

	// 上报完当前action的日志且不再有日志, 更新下一个上传action的日志索引
	if actionNoMore {
		r.Logger.LoggerReportIndexIncr()
		// step 所有action的日志都已经写入完毕，上面更新上报索引后直接进行上报
		if stepNoMore {
			goto retry
		}
	}

	// 如果stepNoMore为true, 需要检查当前step的所有action的日志都已经上报完毕
	if stepNoMore && (actionLogger.ActionIndex < r.Logger.ActionsSize()-1 || !actionNoMore) {
		return fmt.Errorf("step not all logs are submitted")
	}

	// 正常结束(针对于close,不再进行上报)
	return nil
}

func (r *Reporter) Close() error {
	log.Printf("reporter try close")
	r.closed = true
	return retry.Do(func() error {
		if err := r.ReportLog(true); err != nil {
			//log.Printf("Close :: report log error: %+v", err)
			return err
		}
		err := r.ReportState(true)
		if err != nil {
			log.Printf("close report state error: %+v", err)
		}
		return err
	}, retry.Context(r.ctx),
		retry.Delay(time.Second),          // 间隔1秒
		retry.Attempts(20),                // 重试20次
		retry.DelayType(retry.FixedDelay), // 间隔时间一致
		retry.OnRetry(func(n uint, err error) { // 重试回调
			log.Printf("on retry#uuid:%s#%d: %s\n", r.task.TxUUID, n, err)
		}),
	)
}

func (r *Reporter) SetStepStatus(status string) {
	r.stateMu.Lock()
	defer r.stateMu.Unlock()
	r.state.State = status
	r.latestVersion.Add(1)
}

func (r *Reporter) SetStepFinalStatus(status string) {
	r.stateMu.Lock()
	defer r.stateMu.Unlock()
	r.state.State = status
	r.state.EndTime = timestamppb.Now().Seconds
	r.latestVersion.Add(1)
}

func (r *Reporter) InitStepState(s step.Step) {
	r.stateMu.Lock()
	defer r.stateMu.Unlock()
	r.state.State = pipeline.Running
	r.state.StartTime = timestamppb.Now().Seconds
	r.state.Actions = make([]*v1.ActionState, 0)
	for idx, act := range s.Actions() {
		r.state.Actions = append(r.state.Actions, &v1.ActionState{
			Name:  act.Name(),
			State: pipeline.Waiting,
			Index: uint32(idx), //nolint:gosec
		})
	}
	r.latestVersion.Add(1)
}

func (r *Reporter) StartAction(idx int) {
	r.stateMu.Lock()
	defer r.stateMu.Unlock()
	r.actionIndex = idx
	act := r.state.Actions[r.actionIndex]
	act.State = pipeline.Running
	act.StartTime = timestamppb.Now().Seconds
	r.state.Actions[r.actionIndex] = act
	r.latestVersion.Add(1)
	// start action logger
	r.Start(act.Name, idx)
}

// action execution completed

func (r *Reporter) SetActionResponse(response action.FireActionResponse) {
	r.stateMu.Lock()
	defer r.stateMu.Unlock()
	act := r.state.Actions[r.actionIndex]
	act.State = response.Status
	act.EndTime = timestamppb.Now().Seconds
	r.state.Actions[r.actionIndex] = act
	// merge content to step content
	for k, v := range response.Content {
		str := util.ToJSONString(v)
		r.outputs.Store(k, str)
	}
	// set action logger no more
	actionLogger := r.Logger.GetLogs(r.actionIndex)
	actionLogger.LoggerMu.Lock()
	// 这里写入一个空行，是为了解决noMore变化了, rows必须有值才会进行上报
	actionLogger.LogRows = append(actionLogger.LogRows, "")
	actionLogger.NoMore = true
	actionLogger.LoggerMu.Unlock()
	// set error message
	if response.Message != "" {
		r.state.Message = response.Message
	}
	// refresh version
	r.latestVersion.Add(1)
}

// RetryAction 重试 action 执行
func (r *Reporter) RetryAction(idx int, act action.Action) {
	r.stateMu.Lock()
	// 将action状态设置为waiting
	actions := slices.Insert(r.state.Actions, idx, &v1.ActionState{
		Name:  act.Name(), // maybe suffix with retry count
		State: pipeline.Waiting,
		Index: uint32(idx), //nolint:gosec
	})
	// 需要更新后续action的索引
	for i := idx + 1; i < len(actions); i++ {
		actions[i].Index = uint32(i) //nolint:gosec
	}
	r.state.Actions = actions
	r.stateMu.Unlock()
	r.latestVersion.Add(1)
	// report state
	_ = r.ReportState(false)
}

func (r *Reporter) GetCleanActionIndex() int {
	for i := len(r.state.Actions) - 1; i >= 0; i-- {
		act := r.state.Actions[i]
		if act.Name == action.CleanActionName {
			return int(act.Index)
		}
	}
	return -1
}
