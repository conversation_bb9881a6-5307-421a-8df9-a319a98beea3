package logger

import "pipeline/pkg/util"

func banner() []string {
	timePrefix := util.NowTime()
	ret := []string{
		"########################################################################################################",
		"#                       _        _     _            _             _                                    #",
		"#       _ __ ___   __ _| | _____| |__ | | ___   ___| | __      __| | _____   _____  _ __  ___          #",
		"#      | '_ ` _ \\ / _` | |/ / _ \\ '_ \\| |/ _ \\ / __| |/ /____ / _` |/ _ \\ \\ / / _ \\| '_ \\/ __|         #",
		"#      | | | | | | (_| |   <  __/ |_) | | (_) | (__|   <_____| (_| |  __/\\ V / (_) | |_) \\__ \\         #",
		"#      |_| |_| |_|\\__,_|_|\\_\\___|_.__/|_|\\___/ \\___|_|\\_\\     \\__,_|\\___| \\_/ \\___/| .__/|___/         #",
		"#                                                                                  |_|                 #",
		"#                                                                                                      #",
		"########################################################################################################",
	}
	for i, v := range ret {
		ret[i] = "[" + timePrefix + "] " + v
	}
	return ret
}
