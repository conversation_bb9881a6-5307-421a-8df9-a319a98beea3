package logger

import (
	"fmt"
	"os"
)

// ProgressReader 上传进度日志
type ProgressReader struct {
	TotalSize int64
	BytesRead int64
	Reader    *os.File
	Logger    *Logger
}

func NewProgressReader(reader *os.File, totalSize int64,
	logger *Logger) *ProgressReader {
	return &ProgressReader{
		TotalSize: totalSize,
		Reader:    reader,
		Logger:    logger,
	}
}

func (r *ProgressReader) Read(p []byte) (int, error) {
	n, err := r.Reader.Read(p)
	r.BytesRead += int64(n)
	r.Logger.Info(fmt.Sprintf("upload progress: %.2f%%",
		float64(r.BytesRead*100)/float64(r.TotalSize)))
	return n, err
}
