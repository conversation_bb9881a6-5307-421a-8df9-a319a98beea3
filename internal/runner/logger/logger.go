package logger

import (
	"bytes"
	"fmt"
	"io"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/util"
	"strings"
)

const logReplacerValue = "******"

type Logger struct {
	action        string
	actionIndex   int
	actionLoggers []*ActionLogger
	// for log hide
	logReplacer *strings.Replacer
	oldNew      []string
	// for log report
	loggerReportIndex int
	// for log write
	buffer bytes.Buffer
}

func NewLogger(task *pipeline.StepContext) *Logger {
	var oldNew = make([]string, 0)
	for _, v := range task.Secret {
		oldNew = append(oldNew, v.Value, logReplacerValue)
	}
	return &Logger{
		actionLoggers: make([]*ActionLogger, 0),
		oldNew:        oldNew,
		logReplacer:   strings.NewReplacer(oldNew...),
	}
}

func (s *Logger) AddMask(msg string) {
	s.oldNew = append(s.oldNew, msg, logReplacerValue)
	s.logReplacer = strings.NewReplacer(s.oldNew...)
}

func (s *Logger) Start(action string, actionIndex int) {
	// set action
	s.action = action
	s.actionIndex = actionIndex
	// fulfill actionLoggers
	for actionIndex >= len(s.actionLoggers) {
		s.actionLoggers = append(s.actionLoggers, NewActionLoggerWithBanner())
	}
	// set run status
	s.actionLoggers[actionIndex].Start()
}

func (s *Logger) GetLogs(idx int) *ActionLogger {
	return s.actionLoggers[idx]
}

func (s *Logger) ActionsSize() int {
	return len(s.actionLoggers)
}

// GetReportLogs 返回当前未上报的日志
func (s *Logger) GetReportLogs() (*ActionLogger, error) {
	if s.loggerReportIndex >= len(s.actionLoggers) {
		return nil, fmt.Errorf("no more logs")
	}
	ret := s.actionLoggers[s.loggerReportIndex]
	ret.ActionIndex = s.loggerReportIndex
	return ret, nil
}

func (s *Logger) ActionsLogsNoMore() {
	for _, v := range s.actionLoggers {
		v.LoggerMu.Lock()
		v.NoMore = true
		v.LoggerMu.Unlock()
	}
}

func (s *Logger) LoggerReportIndexIncr() {
	// 上报日志
	s.loggerReportIndex++
	for s.loggerReportIndex < len(s.actionLoggers) &&
		!s.actionLoggers[s.loggerReportIndex].Run {
		s.loggerReportIndex++
	}
}

func (s *Logger) Info(logger string) {
	logger = fmt.Sprintf("%s\n", logger)
	s.WriteLine(logger)
}

func (s *Logger) Infof(format string, args ...any) {
	s.Info(fmt.Sprintf(format, args...))
}

func (s *Logger) Debug(logger string) {
	s.Info(fmt.Sprintf("\x1b[33m%s\x1b[0m", logger))
}

func (s *Logger) Debugf(format string, args ...any) {
	s.Info(fmt.Sprintf("\x1b[90m%s\x1b[0m", fmt.Sprintf(format, args...)))
}

func (s *Logger) Error(logger string) {
	s.Info(fmt.Sprintf("\x1b[91m%s\x1b[0m", logger))
}

func (s *Logger) Errorf(format string, args ...any) {
	s.Info(fmt.Sprintf("\x1b[91m%s\x1b[0m", fmt.Sprintf(format, args...)))
}

func (s *Logger) Success(logger string) {
	s.Info(fmt.Sprintf("\x1b[92m%s\x1b[0m", logger))
}

func (s *Logger) Successf(format string, args ...any) {
	s.Info(fmt.Sprintf("\x1b[92m%s\x1b[0m", fmt.Sprintf(format, args...)))
}

// Write 写入日志
func (s *Logger) Write(p []byte) (n int, err error) {
	pBuf := bytes.NewBuffer(p)
	written := 0
	for {
		line, err := pBuf.ReadString('\n')
		w, _ := s.buffer.WriteString(line)
		written += w
		if err == nil {
			s.WriteLine(s.buffer.String()) // 写入日志
			s.buffer.Reset()
		} else if err == io.EOF {
			break
		} else {
			return written, err
		}
	}
	return written, nil
}

// WriteLine 写入一行日志
func (s *Logger) WriteLine(line string) {
	// index check
	for s.actionIndex >= len(s.actionLoggers) {
		s.actionLoggers = append(s.actionLoggers, NewActionLoggerWithBanner())
	}
	// not nil check
	if s.actionLoggers[s.actionIndex] == nil {
		s.actionLoggers[s.actionIndex] = NewActionLoggerWithBanner()
	}
	// oversize
	if s.actionLoggers[s.actionIndex].LogSizeLimit {
		return
	}
	// hide
	line = s.logReplacer.Replace(line)
	// when \n is last char, it leads to an empty string, need to remove it
	line = strings.TrimRight(line, "\n")
	// 非法字符替换（如果不替换序列化会失败）
	line = strings.ToValidUTF8(line, "?")
	// format
	line = s.formatLine(line)
	// write
	s.actionLoggers[s.actionIndex].Write(line)
}

// formatRows 格式化日志
func (s *Logger) formatLine(line string) string {
	// 空行不处理
	if line == "" {
		return line
	}
	// 如果是+开头的我们认为是用户输入的命令，加上高亮颜色
	if strings.HasPrefix(line, "+") {
		line = fmt.Sprintf("\033[1;36m%s\033[0m", line)
	}
	// 加上时间戳, 只需要时间即可，不需要日期
	return fmt.Sprintf("[%s] %s", util.NowTime(), line)
}
