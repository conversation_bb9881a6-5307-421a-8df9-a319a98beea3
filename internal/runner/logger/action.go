package logger

import "sync"

type ActionLogger struct {
	LoggerMu     sync.Mutex
	LogRows      []string
	LogOffset    int
	NoMore       bool
	ActionIndex  int
	Run          bool
	LogSizeLimit bool
}

func NewActionLogger() *ActionLogger {
	return &ActionLogger{
		LogRows: []string{},
	}
}

func NewActionLoggerWithBanner() *ActionLogger {
	return &ActionLogger{
		LogRows: banner(),
	}
}

func (a *ActionLogger) Start() {
	a.LoggerMu.Lock()
	defer a.LoggerMu.Unlock()
	a.Run = true
}

func (a *ActionLogger) Write(line string) {
	a.LoggerMu.Lock()
	defer a.LoggerMu.Unlock()
	a.LogRows = append(a.LogRows, line)
}

// ActionLoggerOversize set action logger oversize, do not write log
func (a *ActionLogger) ActionLoggerOversize() {
	a.LoggerMu.Lock()
	defer a.LoggerMu.Unlock()
	a.LogSizeLimit = true
}
