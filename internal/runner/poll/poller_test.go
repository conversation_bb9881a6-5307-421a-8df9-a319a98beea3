package poll

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	pb "pipeline/pkg/proto/runner/v1"
	"pipeline/pkg/util"
)

// 创建一个简化版的模拟Runner，只实现我们测试需要的方法
type mockRunner struct {
	mock.Mock
}

func (m *mockRunner) PipelineIsRunning(txUuid string) bool {
	args := m.Called(txUuid)
	return args.Bool(0)
}

func (m *mockRunner) GetToken() string {
	return "mock-token"
}

// 辅助函数：创建临时测试目录
func createTempTestDir(t *testing.T, path string) string {
	tempDir := filepath.Join(os.TempDir(), path)
	err := os.MkdirAll(tempDir, 0755)
	if err != nil {
		t.Fatalf("无法创建测试目录: %v", err)
	}
	return tempDir
}

// 辅助函数：检查目录是否存在
func dirExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// mockGetRunBasePath是一个简化的函数，用于测试目的
func mockGetRunBasePath(appIdentity string, pipelineId int64, buildNumber int64) string {
	return filepath.Join(os.TempDir(), "pipeline-test", appIdentity,
		fmt.Sprintf("%d", pipelineId), fmt.Sprintf("%d", buildNumber))
}

func TestPoller_PurgeTask(t *testing.T) {
	// 测试1：任务状态从运行中变为未运行，然后目录被清理
	t.Run("任务状态从运行中变为未运行", func(t *testing.T) {
		// 准备
		runner := new(mockRunner)

		// 创建临时测试目录
		appIdentity := "test-app"
		pipelineId := int64(123)
		buildNumber := int64(456)
		txUuid := "test-tx-uuid"

		testDir := createTempTestDir(t, filepath.Join("pipeline-test", appIdentity, "123", "456"))
		defer os.RemoveAll(filepath.Join(os.TempDir(), "pipeline-test")) // 清理

		// 模拟行为：第一次检查时任务正在运行，第二次检查时任务已结束
		runner.On("PipelineIsRunning", txUuid).Return(true).Once()
		runner.On("PipelineIsRunning", txUuid).Return(false).Once()

		// 执行
		task := &pb.PurgeTask{
			TxUuid:      txUuid,
			AppIdentity: appIdentity,
			PipelineId:  pipelineId,
			BuildNumber: buildNumber,
		}

		// 先验证目录存在
		assert.True(t, dirExists(testDir), "测试开始前目录应该存在")

		// 模拟重试逻辑
		testPurgeTask := func() {
			// 第一次尝试 - 任务正在运行
			util.RunWithRecover("purgeTask-attempt1", func() {
				workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
				if runner.PipelineIsRunning(task.TxUuid) {
					return
				}
				os.RemoveAll(workspace)
			})

			// 等待一段时间（模拟重试延迟）
			time.Sleep(100 * time.Millisecond)

			// 第二次尝试 - 任务已结束
			util.RunWithRecover("purgeTask-attempt2", func() {
				workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
				if runner.PipelineIsRunning(task.TxUuid) {
					return
				}
				os.RemoveAll(workspace)
			})
		}

		// 调用测试函数
		testPurgeTask()

		// 验证
		time.Sleep(200 * time.Millisecond) // 等待异步执行完成
		assert.False(t, dirExists(testDir), "目录应该在任务结束后被删除")
		runner.AssertExpectations(t)
	})

	// 测试2：任务一直运行中，多次重试后强制删除
	t.Run("任务一直运行中多次重试后强制删除", func(t *testing.T) {
		// 准备
		runner := new(mockRunner)

		// 创建临时测试目录
		appIdentity := "test-app"
		pipelineId := int64(789)
		buildNumber := int64(101)
		txUuid := "test-tx-uuid-running"

		testDir := createTempTestDir(t, filepath.Join("pipeline-test", appIdentity, "789", "101"))
		defer os.RemoveAll(filepath.Join(os.TempDir(), "pipeline-test")) // 清理

		// 模拟行为：任务一直在运行（达到最大重试次数）
		// 设置5次调用都返回true，表示任务一直在运行
		runner.On("PipelineIsRunning", txUuid).Return(true).Times(5)

		// 执行
		task := &pb.PurgeTask{
			TxUuid:      txUuid,
			AppIdentity: appIdentity,
			PipelineId:  pipelineId,
			BuildNumber: buildNumber,
		}

		// 模拟purgeTask重试逻辑
		testPurgeTaskWithRetry := func() {
			maxRetries := 5
			retryDelay := 50 * time.Millisecond // 缩短测试时间

			// 定义清理函数
			cleanFunc := func() error {
				if runner.PipelineIsRunning(task.TxUuid) {
					return fmt.Errorf("任务正在运行")
				}
				workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
				return os.RemoveAll(workspace)
			}

			// 执行重试逻辑
			var err error
			for i := 0; i < maxRetries; i++ {
				err = cleanFunc()
				if err == nil {
					break
				}
				time.Sleep(retryDelay)
			}

			// 如果达到最大重试次数仍失败，强制删除
			if err != nil {
				workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
				os.RemoveAll(workspace)
			}
		}

		// 调用测试函数
		testPurgeTaskWithRetry()

		// 验证：即使任务在运行，在达到最大重试次数后也应该强制删除目录
		time.Sleep(300 * time.Millisecond) // 确保有足够时间完成
		assert.False(t, dirExists(testDir), "目录应该在达到最大重试次数后被强制删除")
		runner.AssertExpectations(t)
	})

	// 测试3：任务在重试过程中状态变为未运行
	t.Run("任务在重试过程中状态变为未运行", func(t *testing.T) {
		// 准备
		runner := new(mockRunner)

		// 创建临时测试目录
		appIdentity := "test-app"
		pipelineId := int64(555)
		buildNumber := int64(666)
		txUuid := "test-tx-uuid-changing"

		testDir := createTempTestDir(t, filepath.Join("pipeline-test", appIdentity, "555", "666"))
		defer os.RemoveAll(filepath.Join(os.TempDir(), "pipeline-test")) // 清理

		// 模拟行为：前两次检查任务正在运行，第三次检查时任务已结束
		runner.On("PipelineIsRunning", txUuid).Return(true).Times(2)
		runner.On("PipelineIsRunning", txUuid).Return(false).Once()

		// 执行
		task := &pb.PurgeTask{
			TxUuid:      txUuid,
			AppIdentity: appIdentity,
			PipelineId:  pipelineId,
			BuildNumber: buildNumber,
		}

		// 模拟purgeTask重试逻辑
		testPurgeTaskWithRetry := func() {
			maxRetries := 5
			retryDelay := 50 * time.Millisecond

			// 定义清理函数
			cleanFunc := func() error {
				if runner.PipelineIsRunning(task.TxUuid) {
					return fmt.Errorf("任务正在运行")
				}
				workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
				return os.RemoveAll(workspace)
			}

			// 执行重试逻辑
			var err error
			for i := 0; i < maxRetries; i++ {
				err = cleanFunc()
				if err == nil {
					break
				}
				time.Sleep(retryDelay)
			}

			// 如果达到最大重试次数仍失败，强制删除
			if err != nil {
				workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
				os.RemoveAll(workspace)
			}
		}

		// 调用测试函数
		testPurgeTaskWithRetry()

		// 验证：目录应该在第三次重试时被删除
		time.Sleep(200 * time.Millisecond)
		assert.False(t, dirExists(testDir), "目录应该在任务状态变为未运行后被删除")
		runner.AssertExpectations(t)
	})

	// 测试4：多个任务混合状态（一个运行中，一个未运行）
	t.Run("多个任务混合状态", func(t *testing.T) {
		// 准备
		runner := new(mockRunner)

		// 创建多个临时测试目录
		appIdentity1 := "test-app-1"
		appIdentity2 := "test-app-2"

		testDir1 := createTempTestDir(t, filepath.Join("pipeline-test", appIdentity1, "1", "1"))
		testDir2 := createTempTestDir(t, filepath.Join("pipeline-test", appIdentity2, "2", "2"))
		defer os.RemoveAll(filepath.Join(os.TempDir(), "pipeline-test")) // 清理

		// 模拟行为：第一个任务未运行，第二个任务正在运行
		txUuid1 := "test-tx-uuid-1"
		txUuid2 := "test-tx-uuid-2"
		runner.On("PipelineIsRunning", txUuid1).Return(false)
		runner.On("PipelineIsRunning", txUuid2).Return(true)

		// 执行
		tasks := []*pb.PurgeTask{
			{
				TxUuid:      txUuid1,
				AppIdentity: appIdentity1,
				PipelineId:  int64(1),
				BuildNumber: int64(1),
			},
			{
				TxUuid:      txUuid2,
				AppIdentity: appIdentity2,
				PipelineId:  int64(2),
				BuildNumber: int64(2),
			},
		}

		// 模拟处理多个任务
		testPurgeTaskMulti := func() {
			for _, task := range tasks {
				util.RunWithRecover("purgeTask", func() {
					workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
					if runner.PipelineIsRunning(task.TxUuid) {
						return
					}
					os.RemoveAll(workspace)
				})
			}
		}

		// 调用测试函数
		testPurgeTaskMulti()

		// 验证：第一个任务的目录应该被删除，第二个任务的目录不应该被删除
		time.Sleep(200 * time.Millisecond)
		assert.False(t, dirExists(testDir1), "未运行任务的目录应该被删除")
		assert.True(t, dirExists(testDir2), "运行中任务的目录不应该被删除")
		runner.AssertExpectations(t)
	})

	// 测试5：任务ID不存在的情况（无法确认运行状态）
	t.Run("任务ID不存在", func(t *testing.T) {
		// 准备
		runner := new(mockRunner)

		// 创建临时测试目录
		appIdentity := "test-app"
		pipelineId := int64(999)
		buildNumber := int64(999)
		txUuid := "non-existent-tx-uuid"

		testDir := createTempTestDir(t, filepath.Join("pipeline-test", appIdentity, "999", "999"))
		defer os.RemoveAll(filepath.Join(os.TempDir(), "pipeline-test")) // 清理

		// 模拟行为：当任务ID不存在时，PipelineIsRunning返回false
		runner.On("PipelineIsRunning", txUuid).Return(false)

		// 执行
		task := &pb.PurgeTask{
			TxUuid:      txUuid,
			AppIdentity: appIdentity,
			PipelineId:  pipelineId,
			BuildNumber: buildNumber,
		}

		// 创建一个闭包来测试purgeTask
		testPurgeTask := func() {
			util.RunWithRecover("purgeTask", func() {
				workspace := mockGetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
				if runner.PipelineIsRunning(task.TxUuid) {
					return
				}
				os.RemoveAll(workspace)
			})
		}

		// 调用测试函数
		testPurgeTask()

		// 验证：目录应该被删除，因为任务不存在被视为未运行
		time.Sleep(200 * time.Millisecond)
		assert.False(t, dirExists(testDir), "不存在任务的目录应该被删除")
		runner.AssertExpectations(t)
	})
}
