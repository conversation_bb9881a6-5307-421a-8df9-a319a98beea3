package poll

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/avast/retry-go"

	"pipeline/config"
	"pipeline/internal/runner"
	"pipeline/internal/runner/client"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/git"
	"pipeline/pkg/pipeline"
	pb "pipeline/pkg/proto/runner/v1"
	"pipeline/pkg/util"

	"connectrpc.com/connect"
	"git.makeblock.com/makeblock-go/log"
	"golang.org/x/time/rate"
)

type Poller struct {
	cfg             *config.RunnerConfig
	runner          *runner.Runner
	client          client.Client
	pollingCtx      context.Context    // for polling
	shutdownPolling context.CancelFunc // for shutdown polling
	done            chan struct{}      // for signaling that we are done
}

func New(cfg *config.RunnerConfig, client client.Client, runner *runner.Runner) *Poller {
	pollingCtx, shutdownPolling := context.WithCancel(context.Background())
	done := make(chan struct{})
	return &Poller{
		client:          client,
		runner:          runner,
		cfg:             cfg,
		pollingCtx:      pollingCtx,
		shutdownPolling: shutdownPolling,
		done:            done,
	}
}

func (p *Poller) Poll(ctx context.Context) {
	limiter := rate.NewLimiter(rate.Every(p.cfg.Runner.FetchInterval), 1)
	wg := &sync.WaitGroup{}
	for i := 0; i < p.cfg.Runner.Capacity; i++ {
		wg.Add(1)
		go p.poll(ctx, wg, limiter)
	}
	wg.Wait()
	// signal that we are shutdown
	close(p.done)
}

func (p *Poller) Shutdown(ctx context.Context) error {
	// stop polling for new tasks
	p.shutdownPolling()
	// wait for all running jobs to finish
	select {
	// graceful shutdown completed successfully
	case <-p.done:
		return nil
	// timeout, force shutdown
	case <-ctx.Done():
		// if we are already force shutdown, return nil
		_, ok := <-p.done
		if !ok {
			return nil
		}
		// return an error to indicate that we were force shutdown
		return ctx.Err()
	}
}

func (p *Poller) poll(ctx context.Context, wg *sync.WaitGroup, limiter *rate.Limiter) {
	defer wg.Done()

	// prevent clean lock too frequently
	lastCleanTime := time.Now()
	purgeLockInterval := util.Coalesce(p.cfg.Runner.PurgeRepoLockInterval, time.Hour)

	for {
		if err := limiter.Wait(p.pollingCtx); err != nil {
			if p.pollingCtx.Err() != nil {
				log.Printf("limiter wait failed")
			}
			return
		}
		task, ok := p.fetchTask(p.pollingCtx)
		if !ok {
			// if no task, try to clean repository lock
			if time.Since(lastCleanTime) > purgeLockInterval {
				p.tryCleanRepoLocks()
				lastCleanTime = time.Now()
			}
			continue
		}
		p.runTaskWithRecover(ctx, task)
	}
}

func (p *Poller) runTaskWithRecover(ctx context.Context, task *pipeline.StepContext) {

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic: %v", r)
			log.ErrorE("panic in runTaskWithRecover %v", err)
		}
	}()

	ctx, cancel := context.WithTimeout(ctx, p.cfg.Runner.Timeout) // 这里做个兜底，避免服务端超时控制没有生效
	defer cancel()

	if err := p.runner.Run(ctx, task); err != nil {
		log.ErrorE("failed to run task: %v", err)
	}

}

func (p *Poller) fetchTask(ctx context.Context) (*pipeline.StepContext, bool) {

	reqCtx, cancel := context.WithTimeout(ctx, p.cfg.Runner.FetchTimeout)
	defer cancel()

	response, err := p.client.FetchTask(reqCtx, connect.NewRequest(&pb.FetchTaskRequest{
		Token: p.runner.GetToken(),
	}))

	if errors.Is(err, context.DeadlineExceeded) {
		err = nil
	}

	if err != nil {
		log.ErrorE("failed to fetch task", err)
		return nil, false
	}

	// clean task
	if response != nil && response.Msg != nil && len(response.Msg.PurgeTasks) > 0 {
		p.purgeTask(response.Msg.PurgeTasks)
	}

	if response == nil || response.Msg == nil || response.Msg.Content == "" {
		return nil, false
	}

	stepContext := util.JSONStringToStruct[pipeline.StepContext](response.Msg.Content)

	return &stepContext, true
}

func (p *Poller) purgeTask(tasks []*pb.PurgeTask) {
	// 使用util.RunWithRecover来捕获panic
	util.RunWithRecover("purgeTask", func() {
		// 重试次数
		attempts := uint(5)
		delay := time.Second * 3
		runningErr := errors.New("pipeline running")
		// 遍历所有任务
		for _, task := range tasks {
			// 获取工作目录
			workspace := common.GetRunBasePath(task.AppIdentity, task.PipelineId, task.BuildNumber)
			log.Printf("clean workspace: %s", workspace)
			// 定义清理函数
			cleanFunc := func() error {
				if p.runner.PipelineIsRunning(task.TxUuid) {
					return runningErr
				}
				return util.DeleteFolder(workspace)
			}
			// 尝试清理，重试几次如果管道还在运行
			err := retry.Do(
				cleanFunc,
				retry.Attempts(attempts),
				retry.Delay(delay),
				retry.RetryIf(func(err error) bool {
					return err != nil && errors.Is(err, runningErr)
				}),
			)
			// 如果失败且管道仍在运行，强制清理
			if err != nil {
				log.ErrorE("retry clean workspace err, will force clean", err)
				if err = retry.Do(func() error {
					return util.DeleteFolder(workspace)
				}, retry.Attempts(attempts)); err != nil {
					log.ErrorE("failed to clean workspace", err)
				}
			}
		}
	})
}

// tryCleanRepoLocks 尝试清理仓库锁
func (p *Poller) tryCleanRepoLocks() {
	// 检查是否有正在运行的任务
	tasks := p.runner.GetRunnerTasks()
	if len(tasks.Tasks) == 0 {
		// 没有正在运行的任务，清理所有仓库锁
		count := git.ClearAllLocks()
		if count > 0 {
			log.Printf("cleaned %d repository locks during idle time", count)
		}
	}
}
