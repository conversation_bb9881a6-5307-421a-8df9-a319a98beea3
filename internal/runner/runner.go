package runner

import (
	"context"
	"errors"
	"fmt"
	"github.com/robfig/cron/v3"
	"os"
	"path/filepath"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"
	"strings"
	"sync"
	"time"

	"pipeline/config"
	"pipeline/internal/runner/client"
	"pipeline/internal/runner/common"
	"pipeline/internal/runner/logger"
	"pipeline/internal/runner/metric"
	"pipeline/internal/runner/report"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/action"
	"pipeline/internal/runner/step/factory"
	"pipeline/pkg/pipeline"
	v1 "pipeline/pkg/proto/runner/v1"

	"connectrpc.com/connect"
	"git.makeblock.com/makeblock-go/log"
)

type Runner struct {
	uuid         string
	name         string
	token        string
	cfg          config.RunnerConfig
	client       client.Client
	labels       []string
	runningTasks sync.Map
}

func NewRunner(cfg config.RunnerConfig, reg *config.Registration, client client.Client) *Runner {
	return &Runner{
		uuid:   reg.UUID,
		name:   reg.Name,
		token:  reg.To<PERSON>,
		cfg:    cfg,
		client: client,
		labels: reg.Labels,
	}
}

func (s *Runner) GetToken() string {
	return s.token
}

func (s *Runner) GetLabels() []string {
	return s.labels
}

func (s *Runner) Run(ctx context.Context, stepContext *pipeline.StepContext) error {
	// decrypt step context secret
	if err := pipeline.DecryptStepContext(stepContext); err != nil {
		return err
	}
	// create reporter
	ctx, cancelFunc := context.WithCancel(ctx)
	reporter := report.NewReporter(ctx, cancelFunc, s.client, stepContext)
	// check if task is already running
	taskKey := stepContext.Index.GetTxUUIDWithIndexKey(stepContext.TxUUID)
	if _, ok := s.runningTasks.Load(taskKey); ok {
		return fmt.Errorf("task %s is already running", taskKey)
	}
	s.runningTasks.Store(taskKey, &v1.RunnerTask{
		Step:         stepContext.Step,
		TxUuid:       stepContext.TxUUID,
		StepIdx:      uint32(stepContext.Index.StepIdx),    //nolint:gosec
		StageIdx:     uint32(stepContext.Index.StageIdx),   //nolint:gosec
		StepRowIdx:   uint32(stepContext.Index.StepRowIdx), //nolint:gosec
		StartTime:    time.Now().Unix(),
		App:          stepContext.App.Name,
		Pipeline:     stepContext.Pipeline.Name,
		PipelineUuid: stepContext.Pipeline.UUID,
		BuildNumber:  stepContext.Pipeline.BuildNumber,
	})
	defer s.runningTasks.Delete(taskKey)
	log.Printf("tx: %s, runner run step: %v", stepContext.TxUUID, stepContext.Step)
	// inject logger and step context
	withLoggerContext := context.WithValue(ctx, logger.ContextLoggerKeyVal, reporter.Logger)
	withStepContext := context.WithValue(withLoggerContext, common.StepContextKey, stepContext)
	// run step
	return s.run(withStepContext, stepContext, reporter)
}

func (s *Runner) run(ctx context.Context, stepContext *pipeline.StepContext, reporter *report.Reporter) (err error) {

	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()

	// create step instance
	instance, err := factory.StepFactory.Create(step.CreateStepOption{
		StepContext: stepContext,
		Logger:      reporter.Logger,
	})

	if err != nil {
		return fmt.Errorf("error while create step instance option: %v", err)
	}

	// init step state
	reporter.InitStepState(instance)
	reporter.RunDaemon()
	finalStepState := pipeline.Success
	stepConfig := instance.CommonConfig()
	maxRetry := stepContext.ActionMaxRetries
	idx := 0
	// execute actions
	for _, act := range instance.Actions() {
		// skip clean action
		if act.Name() == action.CleanActionName {
			idx++
			continue
		}
		retryCount := uint(0)
	retry:
		// do execute action
		log.Printf("tx: %s, execute action: %v", stepContext.TxUUID, act.Name())
		fireResponse, fireErr := s.execute(ctx, reporter, idx, act)
		log.Printf("tx: %s, execute %d action: %s, status: %s", stepContext.TxUUID, idx, act.Name(), fireResponse.Status)
		idx++
		// cancel
		if errors.Is(fireErr, context.Canceled) {
			finalStepState = pipeline.Cancel
			break
		}
		// retry check
		if fireErr != nil && retryCount < maxRetry && retryCount < stepConfig.Strategy.Retry {
			time.Sleep(time.Second)
			log.Printf("retry action %s, retry count: %d", act.Name(), retryCount)
			reporter.RetryAction(idx, act)
			retryCount++
			goto retry
		}
		// failed
		if fireErr != nil {
			reporter.Error(fireErr.Error()) // write error log
			finalStepState = pipeline.Failed
			break
		}
	}

	// get clean action
	if ok, cleanActionIdx, act := s.GetCleanAction(instance.Actions(), reporter); ok {
		cleanContext := context.TODO() // because clean action should not be canceled
		withLoggerContext := context.WithValue(cleanContext, logger.ContextLoggerKeyVal, reporter.Logger)
		withStepContext := context.WithValue(withLoggerContext, common.StepContextKey, stepContext)
		if _, err = s.do(withStepContext, reporter, cleanActionIdx, act); err != nil {
			log.ErrorE("clean action failed", err)
		}
	}

	// refresh step state
	reporter.SetStepFinalStatus(finalStepState)

	reporter.ActionsLogsNoMore()

	// report final state
	_ = reporter.ReportState(false)

	// close reporter, report final state and logs
	_ = reporter.Close()

	log.Printf("tx: %s, step: %v, final state: %s", stepContext.TxUUID, stepContext.Step, finalStepState)

	return nil
}

func (s *Runner) execute(ctx context.Context, r *report.Reporter, idx int, act action.Action) (*action.FireActionResponse, error) {
	select {
	case <-ctx.Done():
		return &action.FireActionResponse{
			Status: pipeline.Cancel,
		}, ctx.Err()
	default:
		return s.do(ctx, r, idx, act)
	}
}

func (s *Runner) do(ctx context.Context, r *report.Reporter, idx int, act action.Action) (*action.FireActionResponse, error) {
	// set action state to running
	r.StartAction(idx)
	// execute action
	fireResponse, err := act.Fire(ctx)
	// set action response
	if err != nil {
		fireResponse.Message = err.Error()
	}
	r.SetActionResponse(fireResponse)
	return &fireResponse, err
}

func (s *Runner) GetCleanAction(actions []action.Action, reporter *report.Reporter) (bool, int, action.Action) {
	for _, act := range actions {
		if act.Name() == action.CleanActionName {
			// maybe some action retry, the index will be changed, so we need to get the latest index
			if cleanActionIndex := reporter.GetCleanActionIndex(); cleanActionIndex != -1 {
				return true, cleanActionIndex, act
			}
			return true, -1, act
		}
	}
	return false, -1, nil
}

func (s *Runner) GetRunnerTasks() *v1.RunnerTaskMetric {
	var tasks []*v1.RunnerTask
	s.runningTasks.Range(func(key, value any) bool {
		if task, ok := value.(*v1.RunnerTask); ok {
			tasks = append(tasks, task)
		}
		return true
	})
	return &v1.RunnerTaskMetric{
		Tasks: tasks,
	}
}

// HealthProbe is a goroutine to probe the health of the runner
func (s *Runner) HealthProbe(ctx context.Context) {
	log.Info("runner health probe start")
	defer func() {
		if r := recover(); r != nil {
			log.Error("panic in HealthProbe: ", log.Any("error", r))
		}
	}()

	req := &v1.MetricRequest{
		RunnerUuid: s.uuid,
		RunnerName: s.name,
		Labels:     strings.Join(s.labels, ","),
	}

	ticker := time.NewTicker(time.Duration(s.cfg.Runner.HealthUpdateFrequency) * time.Second)
	for {
		select {
		case <-ticker.C:
			req.Task = s.GetRunnerTasks()
			uploadMetric(ctx, s.client, req)
		case <-ctx.Done():
			return
		}
	}
}

// RunnerMetric is a goroutine to upload the metric of the runner
func (s *Runner) RunnerMetric(ctx context.Context) {
	log.Info("runner upload metric start")
	defer func() {
		if r := recover(); r != nil {
			log.Error("panic in HealthProbe: ", log.Any("error", r))
		}
	}()

	req := &v1.MetricRequest{
		RunnerUuid: s.uuid,
		RunnerName: s.name,
		Labels:     strings.Join(s.labels, ","),
	}

	ticker := time.NewTicker(time.Duration(s.cfg.Runner.MetricUpdateFrequency) * time.Second)
	for {
		select {
		case <-ticker.C:
			runnerMetric, err := metric.GenRunnerMetric()
			if err != nil {
				log.Error("generate runner metric failed: ", log.Any("error", err))
				continue
			}
			req.Metric = runnerMetric
			req.Metric.TaskCount = uint64(len(s.GetRunnerTasks().Tasks))
			uploadMetric(ctx, s.client, req)
		case <-ctx.Done():
			return
		}
	}
}

func uploadMetric(ctx context.Context, client client.Client, req *v1.MetricRequest) {
	_, err := client.Metrics(ctx, connect.NewRequest(req))
	if err != nil {
		log.Error("health probe failed: ", log.Any("error", err))
	}
}

// RunCacheClean 定时清理过期的缓存
// docs: https://makeblock.feishu.cn/docx/S3u3dS7muoWmKaxVnOxc4hDSnEJ
func (s *Runner) RunCacheClean(ctx context.Context) {
	c := cron.New(cron.WithSeconds())
	if _, err := c.AddFunc(config.GetRunnerConfig().Cache.AutomaticCleanupCron, func() {
		defer func() {
			if r := recover(); r != nil {
				log.Error("Panic in RunCacheClean:", log.Any("error", r))
			}
		}()
		s.cleanExpiredCache(ctx)
	}); err != nil {
		log.Error("Failed to add cache clean cron job:", log.Any("error", err),
			log.Any("cron", config.GetRunnerConfig().Cache.AutomaticCleanupCron))
		return
	}
	c.Start()

	go func() {
		<-ctx.Done()
		c.Stop()
	}()
}

// cleanExpiredCache 清理过期的缓存
func (s *Runner) cleanExpiredCache(ctx context.Context) {
	globalCachePaths := make(map[string]struct{})
	cacheRootPath := common.GetPipelineCacheRootPath()

	for k := range agent.CommonCacheMap {
		globalCachePath := fmt.Sprintf("%s/%s", cacheRootPath, util.RemoveChars(k, common.CharsToRemove))
		globalCachePaths[globalCachePath] = struct{}{}
	}

	entries, err := os.ReadDir(cacheRootPath)
	if err != nil {
		log.Error("Error reading cache root path:", log.Any("error", err), log.Any("path", cacheRootPath))
		return
	}

	// /{cacheRootPath}/{appIdentity}
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}
		dirPath := filepath.Join(cacheRootPath, entry.Name())
		// 只处理自定义缓存
		if _, exists := globalCachePaths[dirPath]; exists {
			continue
		}
		subEntries, err := os.ReadDir(dirPath)
		if err != nil {
			fmt.Println("Error reading custom cache path:", err)
			continue
		}
		// /{cacheRootPath}/{appIdentity}/{pipelineUUID}
		for _, subEntry := range subEntries {
			if !subEntry.IsDir() {
				continue
			}
			uuid := subEntry.Name()
			path := fmt.Sprintf("%s/%s", dirPath, uuid)
			log.Info("check path:", log.Any("path", path))

			response, err := s.client.CacheExpire(ctx, connect.NewRequest(&v1.CacheExpireRequest{PipelineUuid: uuid}))
			if err != nil {
				log.Error("Error checking cache expire:", log.Any("error", err))
				continue
			}
			if response.Msg.Expired && !s.PipelineIsRunning(uuid) {
				log.Info("remove path:", log.Any("path", path))
				if err = os.RemoveAll(path); err != nil {
					log.Error("Error removing cache path:", log.Any("error", err))
					continue
				}
			}
		}
	}
}

// PipelineIsRunning 判断流水线是否正在运行
func (s *Runner) PipelineIsRunning(uuid string) bool {
	running := false
	s.runningTasks.Range(func(key, value any) bool {
		if task, ok := value.(*v1.RunnerTask); ok {
			if task.PipelineUuid == uuid {
				running = true
				return false
			}
		}
		return true
	})
	return running
}
