package logger

import (
	"pipeline/pkg/common/model"
)

type InfoResponse struct {
	Id               int64       `json:"id"`                       //  主键
	Uuid             string      `json:"uuid"`                     //  UUID
	PipelineUuid     string      `json:"pipelineUuid"`             //  流水线uuid
	TriggerTime      model.Time  `json:"triggerTime"`              //  触发时间
	TriggerWay       string      `json:"triggerWay"`               //  触发方式
	TriggerUser      string      `json:"triggerUser"`              //  触发人
	TriggerContent   string      `json:"triggerContent"`           //  触发内容
	StartTime        *model.Time `json:"startTime"`                //  开始执行时间
	EndTime          *model.Time `json:"endTime"`                  //  完成时间
	Status           string      `json:"status"`                   //  最终执行状态
	BuildNumber      int64       `json:"buildNumber"`              //  最新构建号
	PipelineSnapshot string      `json:"pipelineSnapshot"`         //  流水线快照
	Creator          string      `json:"creator"`                  //  修改人
	GmtCreate        model.Time  `json:"gmtGreate"`                //  修改时间
	AppRelationShip  string      `json:"appRelationShip" gorm:"-"` //  应用逻辑关系
	Admin            bool        `json:"admin" gorm:"-"`           //  当前操作用户是否是管理员
	AppUUID          string      `json:"appUUID" gorm:"-"`         //  当前操作用户是否是管理员
}

type PipelineSnapshotResponse struct {
	ID      int64  `json:"id"`                // primary key
	UUID    string `json:"uuid"`              // pipeline uuid
	Content string `json:"content,omitempty"` // pipeline snapshot
	State   string `json:"state"`             // pipeline state
}
