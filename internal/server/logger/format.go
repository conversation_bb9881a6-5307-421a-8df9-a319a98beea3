package logger

import (
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
)

// StoragePrefix 存储的前缀
func StoragePrefix(txUUID string) string {
	// logger meta
	meta, err := NewLoggerRepo(mysql.GetDB()).LoadPipelineLogMeta(txUUID)
	if err != nil {
		log.Error("get logger meta failed", log.Any("error", err))
		return ""
	}
	return fmt.Sprintf("%s/%s/", meta.AppInfo.Identity, meta.PipelineInfo.UUID)
}
