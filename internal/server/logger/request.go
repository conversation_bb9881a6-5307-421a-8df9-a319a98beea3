package logger

import (
	"time"

	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
}

type PageRequest struct {
	request.PageRequest
	PipelineUuid     string    `json:"pipelineUuid"`      //  流水线uuid
	TriggerTime      time.Time `json:"trigger_time"`      //  触发时间
	TriggerWay       string    `json:"trigger_way"`       //  触发方式
	Content          string    `json:"content"`           //  存储日志数据正文
	StartTime        time.Time `json:"start_time"`        //  开始执行时间
	FinishTime       time.Time `json:"finish_time"`       //  完成时间
	Status           string    `json:"status"`            //  最终执行状态
	PipelineSnapshot string    `json:"pipeline_snapshot"` //  流水线快照
}

type AddRequest struct {
	PipelineUuid     string    `json:"pipeline_uuid"`     //  流水线uuid
	TriggerTime      time.Time `json:"trigger_time"`      //  触发时间
	TriggerWay       string    `json:"trigger_way"`       //  触发方式
	Content          string    `json:"content"`           //  存储日志数据正文
	StartTime        time.Time `json:"start_time"`        //  开始执行时间
	FinishTime       time.Time `json:"finish_time"`       //  完成时间
	Status           string    `json:"status"`            //  最终执行状态
	PipelineSnapshot string    `json:"pipeline_snapshot"` //  流水线快照
}

type UpdateRequest struct {
	ID               int64     `json:"id" form:"id"`
	PipelineUuid     string    `json:"pipeline_uuid"`     //  流水线uuid
	TriggerTime      time.Time `json:"trigger_time"`      //  触发时间
	TriggerWay       string    `json:"trigger_way"`       //  触发方式
	Content          string    `json:"content"`           //  存储日志数据正文
	StartTime        time.Time `json:"start_time"`        //  开始执行时间
	FinishTime       time.Time `json:"finish_time"`       //  完成时间
	Status           string    `json:"status"`            //  最终执行状态
	PipelineSnapshot string    `json:"pipeline_snapshot"` //  流水线快照
}

type InfoRequest struct {
	UUID         string `form:"uuid"`
	BuildNumber  int64  `form:"build_number"`
	PipelineUUID string `form:"pipeline_uuid"`
}
