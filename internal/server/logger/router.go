package logger

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/pipeline/logger")
	{
		//cAPI.Use(auth.Handler())
		cAPI.POST("/info", middleware.AuthIsAdmin(), middleware.AuthUToken(), Info)
		cAPI.POST("/page", middleware.AuthIsAdmin(), middleware.AuthUToken(), Page)
		cAPI.Use(middleware.AuthUToken())
		cAPI.POST("/list", List)
		cAPI.POST("/snapshot", Snapshot)
		cAPI.POST("/add", Add)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
	}
}
