package logger

import (
	"context"
	"fmt"
	"time"

	"pipeline/config"
	"pipeline/internal/server/pipeline"
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/oss"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

type LoggerService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewLoggerService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *LoggerService {
	svc := &LoggerService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *LoggerService) Info(req InfoRequest, email string, isAdmin bool) (*InfoResponse, error) {
	repo := NewLoggerRepo(s.db)
	// 查询记录
	var err error
	var m *models.PipelineLogger
	if req.UUID != "" {
		m, err = repo.GetByUUID(req.UUID)
		if err != nil {
			return nil, err
		}
	}
	if req.PipelineUUID != "" && req.BuildNumber > 0 {
		m, err = repo.GetByPipelineUUIDAndBuildNumber(req.PipelineUUID, req.BuildNumber)
		if err != nil {
			return nil, err
		}
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	// 查询权限关系
	if isAdmin {
		info.AppRelationShip = common.Principal
	} else {
		var pipelineEntry *models.PipelineInfo
		pipelineEntry, err = pipeline.NewInfoRepo(s.db).GetByUUID(m.PipelineUuid)
		if err != nil {
			return nil, err
		}
		info.AppUUID = pipelineEntry.AppUuid
		if err = s.db.Model(models.ProjectAppOfUser{}).
			Where("app_uuid = ? and user_uuid = ?",
				pipelineEntry.AppUuid, email).Pluck("type_of",
			&info.AppRelationShip).Error; err != nil {
			log.ErrorE("get app relation ship error", err)
		}
	}
	return &info, nil
}

func (s *LoggerService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewLoggerRepo(s.db)
	var m models.PipelineLogger
	err = copier.Copy(&m, &r)
	//m.Create(ops)
	if err != nil {
		return 0, err
	}
	return repo.Add(&m)
}

func (s *LoggerService) Delete(uuids []string, ops string, force bool) error {

	repo := NewLoggerRepo(s.db)

	// 如果是阻塞或者是正在执行的流水线，不允许删除
	if !force && !repo.IsFinishState(uuids) {
		return fmt.Errorf("pipeline is no finish, can't delete")
	}

	// 先查出执行记录需要删除的oss路径
	var ossPaths []string
	for _, uuid := range uuids {
		// 执行记录存储路径
		path := fmt.Sprintf("%s%s", StoragePrefix(uuid), uuid)
		ossPaths = append(ossPaths, path)
	}

	// 删除执行记录
	err := repo.Delete(uuids)
	if err != nil {
		return err
	}
	log.Info(fmt.Sprintf("user : %s, delete logger: %v", uuids, ops))

	// 清理action、step、stage、表
	engine := s.db
	if err = engine.Transaction(func(tx *gorm.DB) error {
		// stage
		if err = tx.Where("tx_uuid in (?)", uuids).Delete(models.StageState{}).Error; err != nil {
			return err
		}
		// step(需要先查出step的id作为删除action的条件)
		var stepIds []int64
		if err = tx.Model(models.StepState{}).Where("tx_uuid in (?)", uuids).Pluck("id", &stepIds).Error; err != nil {
			return err
		}
		// 再进行删除
		if err = tx.Where("tx_uuid in (?)", uuids).Delete(models.StepState{}).Error; err != nil {
			return err
		}
		// action
		if len(stepIds) > 0 {
			if err = tx.Where("step_id in (?)", stepIds).Delete(models.ActionState{}).Error; err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		return err
	}

	// 清理oss
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Error("panic in delete oss", log.Any("error", r))
			}
		}()
		bucket := config.Items().OSS.LoggerBucketName
		ctx := context.Background()
		for _, path := range ossPaths {
			log.Info("delete oss path", log.Any("path", path))
			if err = oss.GetService().Delete(ctx, bucket, path); err != nil {
				log.ErrorE("failed to delete oss file", err)
			}
		}
	}()

	return nil
}

func (s *LoggerService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewLoggerRepo(s.db)
	m, err := repo.Info(r.ID)
	if err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	return repo.Update(m)
}

func (s *LoggerService) Page(r PageRequest, admin bool) (*response.PageModelV2[InfoResponse], error) {
	repo := NewLoggerRepo(s.db)
	page, err := repo.Page(r)
	if err != nil {
		return nil, err
	}
	for i := range page.List {
		if admin {
			page.List[i].Admin = true
		}
		page.List[i].PipelineSnapshot = ""
	}
	return page, nil
}

func (s *LoggerService) List(r ListRequest) (results []*models.PipelineLogger, err error) {
	repo := NewLoggerRepo(s.db)
	return repo.List(r)
}

func (s *LoggerService) Create(m *models.PipelineLogger) (log *models.PipelineLogger, err error) {
	repo := NewLoggerRepo(s.db)
	_, err = repo.Add(m)
	if err != nil {
		return nil, err
	}
	return m, nil
}

func (s *LoggerService) Snapshot(uuid string) (*PipelineSnapshotResponse, error) {
	info, err := NewLoggerRepo(s.db).GetByUUID(uuid)
	if err != nil {
		return nil, err
	}
	return &PipelineSnapshotResponse{
		ID:      info.ID,
		UUID:    info.PipelineUuid,
		Content: info.PipelineSnapshot,
		State:   info.Status,
	}, nil
}

func (s *LoggerService) UpdateOption(uuid string, option map[string]any) error {
	repo := NewLoggerRepo(s.db)
	err := repo.UpdateOption(uuid, option)
	if err != nil {
		return err
	}
	return nil
}

func (s *LoggerService) UpdateOptionStartTimeAndStatus(uuid, status string, startTime time.Time) error {
	repo := NewLoggerRepo(s.db)
	err := repo.UpdateOption(uuid, map[string]any{
		"status":     status,
		"start_time": startTime,
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *LoggerService) UpdateOptionEndTimeAndStatus(uuid, status string, startTime time.Time) error {
	repo := NewLoggerRepo(s.db)
	err := repo.UpdateOption(uuid, map[string]any{
		"status":   status,
		"end_time": startTime,
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *LoggerService) GetByUUID(uuid string) (*models.PipelineLogger, error) {
	repo := NewLoggerRepo(s.db)
	return repo.GetByUUID(uuid)
}

func (s *LoggerService) SavePipelineLoggerContent(uuid string, content string) error {
	repo := NewLoggerRepo(s.db)
	return repo.SavePipelineLoggerContent(uuid, content)
}

func (s *LoggerService) GetByPipelineUUID(pipeUUID string) (int64, error) {
	repo := NewLoggerRepo(s.db)
	return repo.GetByPipelineUuid(pipeUUID)
}

func (s *LoggerService) GetLogUUIDByPipelineUUID(pipeUUID string, num int64) ([]string, error) {
	repo := NewLoggerRepo(s.db)
	return repo.GetLogUUidByPipelineUuid(pipeUUID, num)
}
