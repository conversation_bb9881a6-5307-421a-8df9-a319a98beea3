package logger

import (
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/pipeline"

	"git.makeblock.com/makeblock-go/mysql/v2"

	"gorm.io/gorm"
)

type loggerRepo struct {
	db *gorm.DB
}

func NewLoggerRepo(db *gorm.DB) *loggerRepo {
	return &loggerRepo{db: db}
}

func (repo *loggerRepo) Info(id int64) (*models.PipelineLogger, error) {
	var result models.PipelineLogger
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *loggerRepo) Add(m *models.PipelineLogger) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *loggerRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.PipelineLogger{}).Error
}

func (repo *loggerRepo) Update(m *models.PipelineLogger) (rowsAffected int64, err error) {
	db := repo.db.Model(models.PipelineLogger{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *loggerRepo) Page(r PageRequest) (*response.PageModelV2[InfoResponse], error) {
	pm := &response.PageModelV2[InfoResponse]{}
	query := repo.db.Model(models.PipelineLogger{}).Where("pipeline_uuid = ?", r.PipelineUuid)
	if err := query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []InfoResponse
	if err := query.Order("trigger_time desc").
		Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *loggerRepo) List(r ListRequest) (list []*models.PipelineLogger, err error) {
	if err := repo.db.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *loggerRepo) UpdateOption(uuid string, option map[string]any) error {
	return repo.db.Model(models.PipelineLogger{}).Where("uuid = ?", uuid).Updates(option).Error
}

func (repo *loggerRepo) GetByUUID(uuid string) (*models.PipelineLogger, error) {
	var result models.PipelineLogger
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *loggerRepo) SavePipelineLoggerContent(uuid string, content string) error {
	return repo.db.Model(models.PipelineLogger{}).
		Where("uuid = ?", uuid).Update("content", content).Error
}

func (repo *loggerRepo) GetByPipelineUuid(pipelineUuid string) (int64, error) {
	var num int64
	if err := repo.db.Model(models.PipelineLogger{}).
		Where("pipeline_uuid = ?", pipelineUuid).Count(&num).Error; err != nil {
		return num, err
	}
	return num, nil
}

func (repo *loggerRepo) GetLogUUidByPipelineUuid(pipelineUUid string, num int64) ([]string, error) {
	var uuidItems = make([]string, 0)
	if err := repo.db.Model(models.PipelineLogger{}).
		Where("pipeline_uuid = ?", pipelineUUid).
		Order("gmt_create asc").Limit(int(num)).
		Pluck("uuid", &uuidItems).Error; err != nil {
		return uuidItems, err
	}
	return uuidItems, nil
}

// IsFinishState 检查流水线是否是完成状态
func (repo *loggerRepo) IsFinishState(uuids []string) bool {
	var count int64
	repo.db.Model(models.PipelineLogger{}).Where("uuid in (?)", uuids).
		Where("status in (?)", []string{pipeline.Blocking, pipeline.Waiting, pipeline.Running}).Count(&count)
	return count == 0
}

// UpdateStatusWithColumns 更新流水线状态和其他列数据
func (repo *loggerRepo) UpdateStatusWithColumns(uuid string,
	expectedStatuses []string, updates map[string]any) (bool, error) {
	ret := repo.db.Model(models.PipelineLogger{}).
		Where("uuid = ? and status in (?)", uuid, expectedStatuses).
		UpdateColumns(updates)
	if ret.Error != nil {
		return false, ret.Error
	}
	return ret.RowsAffected > 0, nil
}

// GetByPipelineUUIDAndBuildNumber 根据InfoRequest查询PipelineLogger
func (repo *loggerRepo) GetByPipelineUUIDAndBuildNumber(pipelineUUID string,
	buildNumber int64) (*models.PipelineLogger, error) {
	var result models.PipelineLogger
	if err := repo.db.Where("pipeline_uuid = ? AND build_number = ?",
		pipelineUUID, buildNumber).
		First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *loggerRepo) LoadPipelineLogMeta(txUUID string) (*models.PipelineLoggerMeta, error) {
	engine := mysql.GetDB()
	// 查询流水线日志
	var loggerInfo models.PipelineLogger
	if err := engine.Where("uuid = ?", txUUID).
		First(&loggerInfo).Error; err != nil {
		return nil, err
	}
	// 查询流水线信息
	var pipelineInfo models.PipelineInfo
	if err := engine.Where("uuid = ?", loggerInfo.PipelineUuid).
		First(&pipelineInfo).Error; err != nil {
		return nil, err
	}
	// 查询应用信息
	var appInfo models.ProjectApp
	if err := engine.Where("uuid = ?", pipelineInfo.AppUuid).
		First(&appInfo).Error; err != nil {
		return nil, err
	}
	return &models.PipelineLoggerMeta{
		AppInfo:      &appInfo,
		PipelineInfo: &pipelineInfo,
		LoggerInfo:   &loggerInfo,
	}, nil
}
