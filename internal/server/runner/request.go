package runner

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	Disable            *bool    `json:"disable"`  //  是否启用
	UUID               string   `json:"uuid"`     //  UUID
	UUIDList           []string `json:"uuidList"` //  UUID列表
	CheckOfflineNotify bool     `json:"checkOfflineNotify"`
}

type PageRequest struct {
	request.PageRequest
	Name        string `json:"name"`        //  名称
	Endpoint    string `json:"endpoint"`    //  服务地址
	Concurrency int32  `json:"concurrency"` //  最大任务并发量
	Description string `json:"description"` //  描述
}

type UpdateRequest struct {
	UUID                      string   `json:"uuid" form:"uuid"`
	Name                      string   `json:"name"`                      //  名称
	Remark                    string   `json:"remark"`                    //  描述
	Disable                   bool     `json:"disable"`                   //  是否启用
	IsPublic                  bool     `json:"isPublic"`                  //  是否公开
	AllowedUserIds            []string `json:"allowedUserIds"`            //  允许访问的用户
	VersionUUID               string   `json:"versionUUID"`               //  绑定的版本
	OfflineNotifyUserEmails   []string `json:"offlineNotifyUserEmails"`   //  离线通知用户邮箱
	EnableOfflineNotification bool     `json:"enableOfflineNotification"` //  是否启用离线通知
}

type OnlineRunner struct {
	Label string `json:"label,omitempty"`
	Value string `json:"value,omitempty"`
}

type VersionRequest struct {
	request.PageRequest
	Name    string `form:"name"`
	Version string `form:"version"`
}

type VersionListRequest struct {
	request.PageRequest
	Name       string `form:"name"`
	RunnerUUID string `form:"runnerUUID"`
	UUID       string `form:"uuid"`
}

type UpdateVersionRequest struct {
	UUID        string `json:"uuid" form:"uuid" binding:"required"`
	Version     string `json:"version"`     //  版本
	OS          string `json:"os"`          // 系统
	Arch        string `json:"arch"`        // 架构
	Released    bool   `json:"released"`    //  发布
	DownloadURL string `json:"downloadUrl"` //  下载链接
	Description string `json:"description"` //  描述
}

type AddVersionRequest struct {
	Version     string `json:"version"`     //  版本
	OS          string `json:"os"`          // 系统
	Arch        string `json:"arch"`        // 架构
	Released    bool   `json:"released"`    //  发布
	DownloadURL string `json:"downloadUrl"` //  下载链接
	Description string `json:"description"` //  描述
}

type BatchUpdateRunnerVersionRequest struct {
	Version        string   `json:"version" binding:"required"`        //  版本
	RunnerUUIDList []string `json:"runnerUUIDList" binding:"required"` //  版本
}
