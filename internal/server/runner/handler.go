package runner

import (
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/middleware"
	"pipeline/pkg/models"
	"pipeline/pkg/runner/updater"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

// Info
// @Summary 获取Runner详情
// @Description 根据UUID获取Runner详细信息
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body request.InfoRequest true "请求体参数，包含Runner UUID"
// @Success 200 {object} response.APIModel{data=runner.InfoResponse} "成功返回Runner信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/info [post]
func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Update
// @Summary 更新Runner
// @Description 更新Runner信息
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.UpdateRequest true "请求体参数，包含更新信息"
// @Success 200 {object} response.APIModel "更新成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/update [put]
func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.Update(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}

// Page
// @Summary 分页查询Runner
// @Description 分页获取Runner列表，支持多条件筛选
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.PageRequest true "请求体参数，包含分页及筛选条件"
// @Success 200 {object} response.APIModel "成功返回分页Runner列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/page [post]
func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

// List
// @Summary 获取Runner列表
// @Description 获取所有Runner列表（不分页）
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.ListRequest true "请求体参数，包含筛选条件"
// @Success 200 {object} response.APIModel "成功返回Runner列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/list [post]
func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, response.PageModel{
		List: list,
	})
}

// Online
// @Summary 获取在线Runner标签
// @Description 获取当前在线的Runner标签列表
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.ListRequest true "请求体参数，包含筛选条件"
// @Success 200 {object} response.APIModel "成功返回在线Runner标签列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/online [post]
func Online(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.Online(req, c.GetString(common.UserEmail))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, list)
}

// Delete
// @Summary 删除Runner
// @Description 根据UUID批量删除Runner
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body request.DeleteRequest true "请求体参数，包含待删除Runner UUID列表"
// @Success 200 {object} response.APIModel "删除成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/delete [delete]
func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// Token
// @Summary 新增Runner注册Token
// @Description 新增Runner注册Token
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body request.AddRunnerRegisterTokenRequest true "请求体参数，包含Token信息"
// @Success 200 {object} response.APIModel "新增成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/token [post]
func Token(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.AddRunnerRegisterTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.AddRunnerRegisterToken(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}

// Metrics
// @Summary 查询Runner监控指标
// @Description 查询Runner的资源监控数据
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body request.MetricRequest true "请求体参数，包含监控参数"
// @Success 200 {object} response.APIModel "成功返回监控数据"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/metrics [post]
func Metrics(c *gin.Context) {
	var req *request.MetricRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	ms, err := GetMetricServer().Query(req)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, ms)
}

// Version
// @Summary 获取Runner最新版本
// @Description 获取Runner自升级的最新版本信息
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body updater.RunnerVersionRequest true "请求体参数，包含Runner UUID"
// @Success 200 {object} response.APIModel{data=updater.RunnerVersionResponse} "成功返回版本信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version [post]
func Version(c *gin.Context) {
	// 解析请求
	var req updater.RunnerVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// 查询runner
	var runner models.PipelineRunner
	if err := mysql.GetDB().Where("uuid = ?", req.RunnerUUID).First(&runner).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 如果runner被disable，则不返回版本信息
	if runner.Disable != nil && *runner.Disable {
		response.Err(c, response.UnknownError("runner disabled"))
		return
	}
	// 检查runner是否绑定了对应的版本
	if runner.VersionUUID == "" {
		response.Err(c, response.DBOperationError("runner not bind version"))
		return
	}
	// 查询发布的线上版本
	var prv models.PipelineRunnerVersion
	if err := mysql.GetDB().Where("uuid = ?", runner.VersionUUID).
		Order("gmt_create desc").First(&prv).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 对url做加密处理
	key := util.UUID()
	encryptDownloadURL, _ := util.Encrypt(key, prv.DownloadURL)
	// 响应
	c.JSONP(200, updater.RunnerVersionResponse{
		Version:     prv.Version,
		DownloadURL: encryptDownloadURL,
		SecretKey:   key,
	})
}

// Versions
// @Summary 分页查询Runner版本
// @Description 分页获取Runner版本列表
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.VersionRequest true "请求体参数，包含分页及筛选条件"
// @Success 200 {object} response.APIModel "成功返回版本分页信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version/page [post]
func Versions(context *gin.Context) {
	var req VersionRequest
	if err := context.ShouldBindJSON(&req); err != nil {
		response.Err(context, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(trace.NewTraceServiceFromGin(context), mysql.GetDB(), redis.GetClient())
	pm, err := svc.Versions(req)
	if err != nil {
		response.Err(context, response.DBOperationError(err.Error()))
		return
	}
	response.OK(context, pm)
}

// VersionList
// @Summary 查询Runner可用版本列表
// @Description 查询Runner可用版本列表
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.VersionListRequest true "请求体参数，包含筛选条件"
// @Success 200 {object} response.APIModel "成功返回可用版本列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version/list [post]
func VersionList(context *gin.Context) {
	var req VersionListRequest
	if err := context.ShouldBindJSON(&req); err != nil {
		response.Err(context, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(trace.NewTraceServiceFromGin(context), mysql.GetDB(), redis.GetClient())
	pm, err := svc.VersionList(req)
	if err != nil {
		response.Err(context, response.DBOperationError(err.Error()))
		return
	}
	response.OK(context, pm)
}

// UpdateVersion
// @Summary 更新Runner版本
// @Description 更新Runner的版本信息
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.UpdateVersionRequest true "请求体参数，包含版本信息"
// @Success 200 {object} response.APIModel "更新成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version/update [put]
func UpdateVersion(context *gin.Context) {
	var req UpdateVersionRequest
	if err := context.ShouldBindJSON(&req); err != nil {
		response.Err(context, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(trace.NewTraceServiceFromGin(context), mysql.GetDB(), redis.GetClient())
	err := svc.UpdateVersion(req)
	if err != nil {
		response.Err(context, response.DBOperationError(err.Error()))
		return
	}
	response.OK(context, nil)
}

// VersionInfo
// @Summary 查询Runner版本信息
// @Description 查询Runner版本详细信息
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body request.InfoRequest true "请求体参数，包含版本UUID"
// @Success 200 {object} response.APIModel "成功返回版本详情"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version/info [post]
func VersionInfo(context *gin.Context) {
	var req request.InfoRequest
	if err := context.ShouldBindJSON(&req); err != nil {
		response.Err(context, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(trace.NewTraceServiceFromGin(context), mysql.GetDB(), redis.GetClient())
	info, err := svc.VersionInfo(req.UUID)
	if err != nil {
		response.Err(context, response.DBOperationError(err.Error()))
		return
	}
	response.OK(context, info)
}

// AddVersion
// @Summary 新增Runner版本
// @Description 新增Runner版本
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.AddVersionRequest true "请求体参数，包含版本信息"
// @Success 200 {object} response.APIModel "新增成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version/add [post]
func AddVersion(context *gin.Context) {
	var req AddVersionRequest
	if err := context.ShouldBindJSON(&req); err != nil {
		response.Err(context, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(trace.NewTraceServiceFromGin(context), mysql.GetDB(), redis.GetClient())
	err := svc.AddVersion(req, middleware.GetUserName(context))
	if err != nil {
		response.Err(context, response.DBOperationError(err.Error()))
		return
	}
	response.OK(context, nil)
}

// DeleteVersion
// @Summary 删除Runner版本
// @Description 根据UUID批量删除Runner版本
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body request.DeleteRequest true "请求体参数，包含待删除版本UUID列表"
// @Success 200 {object} response.APIModel "删除成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version/delete [delete]
func DeleteVersion(context *gin.Context) {
	var req request.DeleteRequest
	if err := context.ShouldBindJSON(&req); err != nil {
		response.Err(context, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(trace.NewTraceServiceFromGin(context), mysql.GetDB(), redis.GetClient())
	err := svc.DeleteVersion(req.UUIDs, middleware.GetUserName(context))
	if err != nil {
		response.Err(context, response.DBOperationError(err.Error()))
		return
	}
	response.OK(context, nil)
}

// Tasks
// @Summary 查询Runner正在运行的任务
// @Description 查询Runner正在运行的任务
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body request.TasksRequest true "请求体参数，包含筛选条件"
// @Success 200 {object} response.APIModel "成功返回任务列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/tasks [post]
func Tasks(c *gin.Context) {
	var req *request.TasksRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(trace.NewTraceServiceFromGin(c), mysql.GetDB(), redis.GetClient())
	tasks, err := svc.Tasks(req)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, tasks)
}

// Batch
// @Summary 批量更新Runner版本
// @Description 批量更新Runner的版本信息
// @Tags 实例管理
// @Accept json
// @Produce json
// @Param data body runner.BatchUpdateRunnerVersionRequest true "请求体参数，包含批量更新参数"
// @Success 200 {object} response.APIModel "批量更新成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/runner/version/batch [post]
func Batch(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req BatchUpdateRunnerVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewRunnerService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.BatchUpdateRunnerVersion(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}
