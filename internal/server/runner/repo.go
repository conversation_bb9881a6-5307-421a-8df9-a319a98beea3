package runner

import (
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/util"

	"gorm.io/gorm"
)

type Repo struct {
	db *gorm.DB
}

func NewRunnerRepo(db *gorm.DB) *Repo {
	return &Repo{db: db}
}

func (repo *Repo) Info(id int64) (*models.PipelineRunner, error) {
	var result models.PipelineRunner
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *Repo) Add(m *models.PipelineRunner) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *Repo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.PipelineRunner{}).Error
}

func (repo *Repo) Update(m *models.PipelineRunner) (rowsAffected int64, err error) {
	db := repo.db.Model(models.PipelineRunner{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *Repo) Page(r PageRequest) (pm response.PageModelV2[*models.PipelineRunner], err error) {
	query := repo.db.Model(models.PipelineRunner{})
	if r.Name != "" {
		query = query.Where("name like ?", util.Like(r.Name))
	}
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*models.PipelineRunner
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *Repo) List(r ListRequest) (list []*models.PipelineRunner, err error) {
	query := repo.db.Model(models.PipelineRunner{})
	if r.Disable != nil {
		query = query.Where("disable = ?", r.Disable)
	}
	if r.UUID != "" {
		query = query.Where("uuid = ?", r.UUID)
	}
	if len(r.UUIDList) > 0 {
		query = query.Where("uuid in (?)", r.UUIDList)
	}
	if r.CheckOfflineNotify {
		query = query.Where("offline_notify_user_emails IS NOT NULL AND offline_notify_user_emails != '[]'")
	}
	if err := query.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *Repo) GetRunnerToken(token string) (*models.PipelineRunnerToken, error) {
	var result models.PipelineRunnerToken
	if err := repo.db.Where("token = ?", token).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *Repo) ActiveToken(token string) error {
	return repo.db.Model(models.PipelineRunnerToken{}).Where("token = ?", token).Update("active", true).Error
}

func (repo *Repo) GetRunnerByUUID(uuid string) (*models.PipelineRunner, error) {
	var result models.PipelineRunner
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *Repo) GetRunnerByToken(token string) (*models.PipelineRunner, error) {
	var result models.PipelineRunner
	if err := repo.db.Where("token = ?", token).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *Repo) VersionPage(req VersionRequest) (pm response.PageModelV2[*models.PipelineRunnerVersion], err error) {
	query := repo.db.Model(models.PipelineRunnerVersion{})
	if req.Name != "" {
		value := util.Like(req.Name)
		query = query.Where("os like ? or arch like ? or download_url like ?", value, value, value)
	}
	if req.Version != "" {
		query = query.Where("version = ?", req.Version)
	}
	query = query.Where("deleted = ?", false)
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	var list []*models.PipelineRunnerVersion
	if err = query.Order("gmt_create desc").Limit(limit).
		Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = req.Page
	pm.PageSize = req.PageSize
	return pm, nil
}

func (repo *Repo) GetRunnerVersionByUUID(uuid string) (*models.PipelineRunnerVersion, error) {
	var result models.PipelineRunnerVersion
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *Repo) UpdateVersion(runner *models.PipelineRunnerVersion) error {
	return repo.db.Model(models.PipelineRunnerVersion{}).Where("uuid = ?", runner.UUID).Updates(&runner).Error
}

func (repo *Repo) AddVersion(rv *models.PipelineRunnerVersion) error {
	return repo.db.Model(models.PipelineRunnerVersion{}).Create(rv).Error
}

func (repo *Repo) DeleteVersion(ds []string, name string) error {
	return repo.db.Model(models.PipelineRunnerVersion{}).
		Where("uuid in (?)", ds).UpdateColumns(map[string]any{
		"deleted":  true,
		"modifier": name,
	}).Error
}

// VersionList 查询runner可用版本列表
func (repo *Repo) VersionList(req VersionListRequest) ([]models.PipelineRunnerVersion, error) {
	var list []models.PipelineRunnerVersion
	query := repo.db.Model(models.PipelineRunnerVersion{})
	if req.RunnerUUID != "" {
		// 先查询runner
		var runnerModel models.PipelineRunner
		if err := repo.db.Where("uuid = ?", req.RunnerUUID).First(&runnerModel).Error; err != nil {
			return nil, err
		}
		// 根据runner查询版本
		query = query.Where("os = ? and arch = ?", runnerModel.OS, runnerModel.Arch)
	}
	if req.UUID != "" {
		query = query.Where("uuid = ?", req.UUID)
	}
	if req.Name != "" {
		query = query.Where("version like ?", util.Like(req.Name))
	}
	query = query.Where("deleted = ?", false)
	if err := query.Order("gmt_create desc").Limit(100).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *Repo) GetRunnerVersion(version string, os string, arch string) (*models.PipelineRunnerVersion, error) {
	var result models.PipelineRunnerVersion
	if err := repo.db.Where("version = ? and os = ? and arch = ?", version, os, arch).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}
