package runner

import (
	v1 "pipeline/pkg/proto/runner/v1"
	"time"
)

type InfoResponse struct {
	Id                        int64     `json:"id"`                        //  主键
	Uuid                      string    `json:"uuid"`                      //  UUID
	Name                      string    `json:"name"`                      //  名称
	Endpoint                  string    `json:"endpoint"`                  //  服务地址
	Concurrency               int32     `json:"concurrency"`               //  最大任务并发量
	Remark                    string    `json:"remark"`                    //  描述
	Disable                   bool      `json:"disable"`                   //  是否启用
	Labels                    string    `json:"labels"`                    //  标签
	Creator                   string    `json:"creator"`                   //  创建人
	Modifier                  string    `json:"modifier"`                  //  修改人
	Status                    string    `json:"status"`                    //  状态
	Version                   string    `json:"version"`                   //  状态
	GmtCreate                 time.Time `json:"gmtCreate"`                 //  创建时间
	GmtModified               time.Time `json:"gmtModified"`               //  修改时间
	Deleted                   bool      `json:"deleted"`                   //  逻辑删除
	IsPublic                  bool      `json:"isPublic"`                  //  是否公开
	AllowedUserIds            []string  `json:"allowedUserIds"`            //  允许访问的用户
	VersionUUID               string    `json:"versionUUID"`               //  绑定的版本
	OfflineNotifyUserEmails   []string  `json:"offlineNotifyUserEmails"`   //  离线通知用户邮箱
	EnableOfflineNotification bool      `json:"enableOfflineNotification"` //  是否启用离线通知
	Ip                        string    `json:"ip"`                        //  IP
}

type TasksResponse struct {
	Runner *InfoResponse    `json:"runner"`
	Tasks  []*v1.RunnerTask `json:"tasks"`
}
