package runner

import (
	"context"
	"os"
	"time"

	"pipeline/pkg/models"
	v1 "pipeline/pkg/proto/runner/v1"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Runner Service", func() {
	var (
		ctx      context.Context
		rediscli *redis.Client
		ms       *miniredis.Miniredis
		err      error
	)

	BeforeEach(func() {
		ctx = context.Background()
		ms, err = miniredis.Run()
		Expect(err).To(BeNil())
		rediscli = redis.NewClient(&redis.Options{
			Addr: ms.Addr(),
		})
	})

	Context("GetRunnerStatus", func() {
		// 每次执行完都要清理一下数据
		AfterEach(func() {
			ms.Del(models.RunnerHeartBeat)
		})

		When("runner is not found", func() {
			It("status should be offline", func() {
				status := GetRunnerStatus(ctx, rediscli, "uuid1")
				Expect(err).To(BeNil())
				Expect(status).To(Equal(v1.RunnerState_RUNNER_STATE_OFFLINE.String()))
			})
		})

		When("redis is down", func() {
			BeforeEach(func() {
				ms.Close()
			})
			It("should return error", func() {
				status := GetRunnerStatus(ctx, rediscli, "uuid1")
				Expect(status).To(Equal(v1.RunnerState_RUNNER_STATE_UNSPECIFIED.String()))
			})
		})

		When("runner upload is timeout", func() {
			var offlineRunner models.RunnerHealthEntry
			BeforeEach(func() {
				offline, err := os.ReadFile("../../../test/testdata/runners/runner_offline.json")
				Expect(err).To(BeNil())
				offlineRunner.Unmarshal(string(offline))

				ms.HSet(models.RunnerHeartBeat, offlineRunner.UUID, offlineRunner.Marshal())
			})
			It("status should be offline", func() {
				status := GetRunnerStatus(ctx, rediscli, "uuid1")
				Expect(status).To(Equal(v1.RunnerState_RUNNER_STATE_OFFLINE.String()))
			})
		})

		When("runner is idle", func() {
			var idleRunner models.RunnerHealthEntry
			BeforeEach(func() {
				idle, err := os.ReadFile("../../../test/testdata/runners/runner_active.json")
				Expect(err).To(BeNil())
				idleRunner.Unmarshal(string(idle))
				idleRunner.ActiveTime = time.Now()
				idleRunner.TaskCount = 0

				ms.HSet(models.RunnerHeartBeat, idleRunner.UUID, idleRunner.Marshal())
			})
			It("status should be idle", func() {
				status := GetRunnerStatus(ctx, rediscli, idleRunner.UUID)
				Expect(status).To(Equal(v1.RunnerState_RUNNER_STATE_IDLE.String()))
			})
		})

		When("runner is active", func() {
			var busyRunner models.RunnerHealthEntry
			BeforeEach(func() {
				busy, err := os.ReadFile("../../../test/testdata/runners/runner_active.json")
				Expect(err).To(BeNil())
				busyRunner.Unmarshal(string(busy))
				busyRunner.ActiveTime = time.Now()
				busyRunner.TaskCount = 1

				ms.HSet(models.RunnerHeartBeat, busyRunner.UUID, busyRunner.Marshal())
			})
			It("status should be busy", func() {
				status := GetRunnerStatus(ctx, rediscli, busyRunner.UUID)
				Expect(status).To(Equal(v1.RunnerState_RUNNER_STATE_ACTIVE.String()))
			})
		})
	})
})
