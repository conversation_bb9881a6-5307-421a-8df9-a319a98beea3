package runner

import (
	"context"
	"errors"
	"sort"
	"strings"
	"time"

	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	"pipeline/pkg/pipeline"
	v1 "pipeline/pkg/proto/runner/v1"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

type runnerService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewRunnerService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *runnerService {
	svc := &runnerService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *runnerService) Info(uuid string) (*InfoResponse, error) {
	repo := NewRunnerRepo(s.db)
	m, err := repo.GetRunnerByUUID(uuid)
	if err != nil {
		return nil, err
	}
	if m.AllowedUserIds == nil {
		m.AllowedUserIds = make([]string, 0)
	}
	if m.OfflineNotifyUserEmails == nil {
		m.OfflineNotifyUserEmails = make([]string, 0)
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *runnerService) Delete(ids []string, ops string) error {
	repo := NewRunnerRepo(s.db)
	err := repo.Delete(ids)
	if err != nil {
		log.Error("delete runner error", log.Any("error", err))
		return err
	}
	// 更新到redis缓存
	return eventbus.Publish(context.Background(), &events.PipelineRunnerDeleteEvent{
		IDs: ids,
	})
}

func (s *runnerService) Update(r UpdateRequest, ops string) error {
	repo := NewRunnerRepo(s.db)
	m, err := repo.GetRunnerByUUID(r.UUID)
	if err != nil {
		return err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return err
	}
	_, err = repo.Update(m)
	if err != nil {
		return err
	}
	// 更新到redis缓存
	return eventbus.Publish(context.Background(), &events.PipelineRunnerUpdateEvent{
		PipelineRunner:        m,
		SkipRefreshActiveTime: true,
	})
}

func (s *runnerService) Page(r PageRequest) (response.PageModel, error) {
	repo := NewRunnerRepo(s.db)
	page, err := repo.Page(r)
	if err != nil {
		return response.PageModel{}, err
	}
	list := make([]InfoResponse, 0)
	//查询状态
	for i := range page.List {
		from := page.List[i]
		var info InfoResponse
		err := copier.Copy(&info, from)
		if err != nil {
			return response.PageModel{}, err
		}
		info.Status = GetRunnerStatus(context.Background(), s.redisClient, info.Uuid)
		list = append(list, info)
	}
	return response.PageModel{
		List:     list,
		Page:     r.Page,
		PageSize: r.PageSize,
		Total:    page.Total,
	}, nil
}

func (s *runnerService) List(r ListRequest) (results []*models.PipelineRunner, err error) {
	repo := NewRunnerRepo(s.db)
	list, err := repo.List(r)
	if err != nil {
		return nil, err
	}
	// 隐藏敏感信息
	for i := range list {
		list[i].ID = 0
		list[i].Token = ""
		list[i].TokenSalt = ""
		list[i].TokenHash = ""
	}
	return list, nil
}

func (s *runnerService) Online(r ListRequest, ops string) (results []*OnlineRunner, err error) {
	repo := NewRunnerRepo(s.db)
	list, err := repo.List(r)
	if err != nil {
		return nil, err
	}

	tmp := make([]*models.PipelineRunner, 0)
	for _, item := range list {
		if *item.IsPublic {
			tmp = append(tmp, item)
		} else {
			for _, email := range item.AllowedUserIds {
				if email == ops {
					tmp = append(tmp, item)
					break
				}
			}
		}
	}

	ret, mset := make([]*OnlineRunner, 0), make(map[string]struct{}, 0)
	//追加一个随机
	ret = append(ret, &OnlineRunner{
		Label: "随机",
		Value: pipeline.Random,
	})
	for _, runner := range tmp {

		labelArr := strings.Split(runner.Labels, ",")

		for _, labelItem := range labelArr {
			if labelItem == pipeline.Random {
				continue
			}
			_, ok := mset[labelItem]
			if !ok {
				mset[labelItem] = struct{}{}
			}
		}

	}

	for item := range mset {
		ret = append(ret, &OnlineRunner{
			Label: item,
			Value: item,
		})
	}
	return ret, nil
}

func (s *runnerService) AddRunnerRegisterToken(req request.AddRunnerRegisterTokenRequest, ops string) error {
	t := &models.PipelineRunnerToken{
		Token:     req.Token,
		ExpiredAt: time.Now().Add(time.Hour), //默认1小时过期
	}
	t.Create(ops)
	return s.db.Model(models.PipelineRunnerToken{}).Create(t).Error
}

func GetRunnerStatus(ctx context.Context, redisCli *redis.Client, uuid string) string {
	entry := &models.RunnerHealthEntry{}

	entryStr, err := redisCli.HGet(ctx, models.RunnerHeartBeat, uuid).Result()
	if errors.Is(err, redis.Nil) {
		return v1.RunnerState_RUNNER_STATE_OFFLINE.String()
	} else if err != nil {
		log.Error("get runner status error", log.Any("error", err))
		return v1.RunnerState_RUNNER_STATE_UNSPECIFIED.String()
	}

	entry.Unmarshal(entryStr)
	// 超过1分钟未上报心跳，则认为离线
	if time.Since(entry.ActiveTime) > models.RunnerMonitorGracePeriod {
		return v1.RunnerState_RUNNER_STATE_OFFLINE.String()
	}
	// 无任务则认为空闲
	if entry.TaskCount == 0 {
		return v1.RunnerState_RUNNER_STATE_IDLE.String()
	}

	return v1.RunnerState_RUNNER_STATE_ACTIVE.String()
}

func (s *runnerService) Versions(req VersionRequest) (response.PageModelV2[*models.PipelineRunnerVersion], error) {
	repo := NewRunnerRepo(s.db)
	return repo.VersionPage(req)
}

func (s *runnerService) UpdateVersion(req UpdateVersionRequest) error {
	repo := NewRunnerRepo(s.db)
	// 通过uuid获取版本
	runner, err := repo.GetRunnerVersionByUUID(req.UUID)
	if err != nil {
		return err
	}
	// 更新版本
	err = copier.Copy(runner, &req)
	if err != nil {
		return err
	}
	return repo.UpdateVersion(runner)
}

func (s *runnerService) VersionInfo(uuid string) (*models.PipelineRunnerVersion, error) {
	repo := NewRunnerRepo(s.db)
	return repo.GetRunnerVersionByUUID(uuid)
}

func (s *runnerService) AddVersion(req AddVersionRequest, ops string) error {
	repo := NewRunnerRepo(s.db)
	rv := &models.PipelineRunnerVersion{}
	err := copier.Copy(rv, &req)
	if err != nil {
		return err
	}
	rv.Create(ops)
	return repo.AddVersion(rv)
}

func (s *runnerService) DeleteVersion(ds []string, name string) error {
	repo := NewRunnerRepo(s.db)
	return repo.DeleteVersion(ds, name)
}

func (s *runnerService) VersionList(req VersionListRequest) ([]models.PipelineRunnerVersion, error) {
	repo := NewRunnerRepo(s.db)
	return repo.VersionList(req)
}

func (s *runnerService) Tasks(r *request.TasksRequest) ([]*TasksResponse, error) {
	repo := NewRunnerRepo(s.db)
	list, err := repo.List(ListRequest{
		UUID: r.RunnerUUID,
	})
	if err != nil {
		return nil, err
	}
	ret := make([]*TasksResponse, 0)
	for _, runner := range list {
		// 查询任务
		hGet := s.redisClient.HGet(context.Background(), models.RunnerHeartBeat, runner.UUID)
		if errors.Is(hGet.Err(), redis.Nil) {
			continue
		}
		entry := &models.RunnerHealthEntry{}
		entry.Unmarshal(hGet.Val())
		var info InfoResponse
		err = copier.Copy(&info, runner)
		if err != nil {
			return nil, err
		}
		// 对任务进行按照开始时间戳排序
		sort.Slice(entry.Tasks, func(i, j int) bool {
			return entry.Tasks[i].StartTime < entry.Tasks[j].StartTime
		})
		ret = append(ret, &TasksResponse{
			Runner: &info,
			Tasks:  entry.Tasks,
		})
	}
	return ret, nil
}

func (s *runnerService) BatchUpdateRunnerVersion(req BatchUpdateRunnerVersionRequest, ops string) error {
	repo := NewRunnerRepo(s.db)
	// 查询所有的runner
	runnerList, err := repo.List(ListRequest{
		UUIDList: req.RunnerUUIDList,
	})
	if err != nil {
		return err
	}
	// 查询每个runner是否有对应的版本，如果有则更新，如果没有跳过
	for _, runner := range runnerList {
		// 查询版本
		var version *models.PipelineRunnerVersion
		version, err = repo.GetRunnerVersion(req.Version, runner.OS, runner.Arch)
		if err != nil {
			log.Error("get runner version error", log.Any("error", err))
			continue
		}
		// 更新runner
		runner.VersionUUID = version.UUID
		runner.Update(ops)
		_, err = repo.Update(runner)
		if err != nil {
			return err
		}
	}
	return nil
}
