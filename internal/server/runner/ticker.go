package runner

import (
	"context"
	"fmt"
	"git.makeblock.com/makeblock-go/redis"
	"pipeline/config"
	"pipeline/pkg/notify"
	"pipeline/pkg/notify/types"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"pipeline/pkg/distribute"
	"pipeline/pkg/models"
	v1 "pipeline/pkg/proto/runner/v1"
)

const (
	ExpireTime       = 30 * time.Second
	LeaseTime        = 10 * time.Second
	RunnerTickerLock = "runner:ticker:lock"
	// 离线通知重复的时间间隔
	//OfflineNotifyInterval = 30 * time.Minute
	// ticker 周期
	//TickerInterval = 3 * time.Minute
)

func StartRunnerStatusTicker(ctx context.Context) {
	log.Info("start runner status ticker")
	defer func() {
		if err := recover(); err != nil {
			log.Error("panic error", log.Any("error", err))
		}
	}()
	distributeLock, _ := distribute.NewDistributeLock(ctx, redis.GetClient(), RunnerTickerLock, ExpireTime, LeaseTime)
	for {
		lock, lockErr := distributeLock.Lock()
		if lockErr != nil {
			log.Error("lock error", log.Any("error", lockErr))
			return
		}
		if lock {
			log.Info("runner status ticker get lock success")
			break
		}
		time.Sleep(ExpireTime)
	}
	defer func() {
		if unlockErr := distributeLock.Unlock(); unlockErr != nil {
			log.Error("unlock distribute lock error", log.Any("error", unlockErr))
		}
	}()
	go distributeLock.Lease()

	ticker := time.NewTicker(config.Items().RunnerNotify.TickerInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			//log.Info("runner status ticker check")
			checkRunnerStatus()
		case <-distributeLock.ErrChan:
			log.Error("runner status ticker get lock error", log.Any("error", distributeLock.ErrChan))
			return
		case <-ctx.Done():
			return
		}
	}
}

func checkRunnerStatus() {
	redisCli := redis.GetClient()
	svc := NewRunnerService(nil, mysql.GetDB(), redisCli)
	disable := false
	req := ListRequest{
		Disable:            &disable,
		CheckOfflineNotify: true,
	}
	list, err := svc.List(req)
	if err != nil {
		log.Error("get runner list error", log.Any("error", err))
		return
	}
	for _, r := range list {
		status := GetRunnerStatus(context.Background(), redisCli, r.UUID)
		entry := &models.RunnerHealthEntry{}
		entryStr, err := redisCli.HGet(context.Background(), models.RunnerHeartBeat, r.UUID).Result()
		if err != nil {
			log.Error("from redis get status error", log.Any("error", err), log.Any("uuid", r.UUID))
			continue
		}
		entry.Unmarshal(entryStr)
		if status == v1.RunnerState_RUNNER_STATE_OFFLINE.String() {
			if r.EnableOfflineNotification != nil && !*r.EnableOfflineNotification &&
				r.OfflineNotifyUserEmails != nil && len(r.OfflineNotifyUserEmails) > 0 {
				continue
			}
			// 上次发送时间未超过间隔则忽略
			if time.Now().Unix()-entry.OfflineNotifyTime.Unix() < int64(config.Items().RunnerNotify.OfflineNotifyInterval.Seconds()) {
				continue
			}
			// 通知用户
			log.Info("runner offline notify", log.Any("runner", r.UUID))
			data := types.NotifyData{
				Type:         types.FeishuPerson,
				Receivers:    r.OfflineNotifyUserEmails,
				TemplateType: types.RunnerOfflineNotify,
				DataSource: map[string]any{
					"name":    r.Name,
					"version": r.Version,
					"remark":  r.Remark,
					"url":     config.Items().FrontEndEndpoint + fmt.Sprintf(config.Items().Pipeline.RunnerSearchUrl, r.Name),
				},
			}
			err := notify.SendNotification(context.Background(), data)
			if err != nil {
				log.Error("send notification error", log.Any("error", err))
				continue
			}
			// 更新离线通知时间
			entry.OfflineNotifyTime = time.Now()
			redisCli.HSet(context.Background(), models.RunnerHeartBeat, r.UUID, entry.Marshal())
		}

	}

}
