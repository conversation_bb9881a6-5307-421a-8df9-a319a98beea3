package runner

import (
	"strconv"
	"time"

	"pipeline/config"
	"pipeline/pkg/common/request"
	"pipeline/pkg/influxdb"
	v1 "pipeline/pkg/proto/runner/v1"

	"git.makeblock.com/makeblock-go/log"
	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
)

type MetricServer struct {
	InfluxDB    *influxdb.InfluxDB
	Measurement string
}

type Metric struct {
	Total struct {
		Mem  uint64 `json:"mem"`
		Cpu  uint64 `json:"cpu"`
		Disk uint64 `json:"disk"`
	} `json:"total"`
	Time    []string `json:"time"`
	Metrics struct {
		Mem  []uint64 `json:"mem"`
		Cpu  []uint64 `json:"cpu"`
		Disk []uint64 `json:"disk"`
		Task []uint64 `json:"task"`
	} `json:"metrics"`
}

func GetMetricServer() *MetricServer {
	baseUrl := config.Items().InfluxDB.Url
	token := config.Items().InfluxDB.Token

	cfg := &influxdb.BaseConfig{
		BaseUrl: baseUrl,
		Token:   token,
	}

	influxDB := influxdb.NewInfluxDB(cfg).CreateOrgAndBucket()
	return &MetricServer{
		InfluxDB:    influxDB,
		Measurement: "runner_metric",
	}
}

// Save saves the metric to influxdb
func (m *MetricServer) Save(metric *v1.MetricRequest) {
	taskRow := influxdb2.NewPointWithMeasurement(m.Measurement).
		AddTag("runner_uuid", metric.RunnerUuid).
		AddTag("runner_name", metric.RunnerName).
		AddField("mem_used", metric.Metric.Memory.Used).
		AddField("mem_total", metric.Metric.Memory.Total).
		AddField("cpu_used", metric.Metric.Cpu.Used).
		AddField("cpu_total", metric.Metric.Cpu.Total).
		AddField("disk_used", metric.Metric.Disk.Used).
		AddField("disk_total", metric.Metric.Disk.Total).
		AddField("task_total", metric.Metric.TaskCount).
		SetTime(time.Now())
	if err := m.InfluxDB.Write(taskRow); err != nil {
		log.Error("write metric to influxdb failed", log.Any("error", err))
	}
}

// Query queries the metric from influxdb
func (m *MetricServer) Query(req *request.MetricRequest) (*Metric, error) {
	log.Info("query metric from influxdb", log.Any("request", req))

	opt := RequestToQueryOpt(req)
	opt.Measurement = m.Measurement
	result, err := m.InfluxDB.Query(opt)
	if err != nil {
		log.Error("query metric from influxdb failed", log.Any("error", err))
		return nil, err
	}
	defer result.Close()

	metric := &Metric{}
	fieldToMetric := map[string]*uint64{
		"cpu_total":  &metric.Total.Cpu,
		"mem_total":  &metric.Total.Mem,
		"disk_total": &metric.Total.Disk,
	}

	fieldToMetricSlice := map[string]*[]uint64{
		"cpu_used":   &metric.Metrics.Cpu,
		"mem_used":   &metric.Metrics.Mem,
		"disk_used":  &metric.Metrics.Disk,
		"task_total": &metric.Metrics.Task,
	}

	for result.Next() {

		record := result.Record()
		field := record.Field()

		if field == "task_total" {
			metric.Time = append(metric.Time, UTCToLocal(record.Time()))
		}

		value := record.Value().(uint64)

		if ptr, ok := fieldToMetric[field]; ok {
			*ptr = value
		} else if ptr, ok := fieldToMetricSlice[field]; ok {
			*ptr = append(*ptr, value)
		}
	}

	return metric, nil
}

func RequestToQueryOpt(req *request.MetricRequest) *influxdb.QueryOption {
	tags := make([]influxdb.QueryTag, 0)

	tags = append(tags, influxdb.QueryTag{
		Key:   "runner_uuid",
		Value: req.RunnerUUID,
	})

	return &influxdb.QueryOption{
		Start: strconv.FormatInt(req.StartTime, 10),
		Stop:  strconv.FormatInt(req.EndTime, 10),
		Tags:  tags,
	}
}

func UTCToLocal(utc time.Time) string {
	location, _ := time.LoadLocation("Asia/Shanghai")
	return utc.In(location).Format("2006/01/02 15:04:05")
}
