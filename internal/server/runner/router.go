package runner

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"pipeline/pkg/middleware"
	"pipeline/pkg/runner/updater"

	"github.com/gin-gonic/gin"
)

// 自定义限流参数
var rl = middleware.RateLimit(
	middleware.WithLimit(5),
	middleware.WithRateLimitKey(func(c *gin.Context) string {
		var req updater.RunnerVersionRequest
		bodyBytes, _ := io.ReadAll(c.Request.Body)
		_ = json.Unmarshal(bodyBytes, &req)
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		return fmt.Sprintf("%s::%s::%s::%s::%s", middleware.RedisPrefix,
			c.ClientIP(), req.RunnerUUID, c.Request.URL.Path, c.Request.Method)
	}))

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/pipeline/runner/")
	{
		cAPI.POST("/version", rl, Version)
		cAPI.POST("/version/add", AddVersion) // 添加版本
		cAPI.Use(middleware.AuthUToken())
		cAPI.POST("/token", Token)
		cAPI.POST("/info", Info)
		cAPI.POST("/page", Page)
		cAPI.POST("/list", List)
		cAPI.POST("/online", Online)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
		cAPI.POST("/metrics", Metrics)
		cAPI.POST("/tasks", Tasks) // 查询正在运行的任务
		// version
		cAPI.POST("/version/info", VersionInfo)
		cAPI.POST("/version/page", Versions)
		cAPI.POST("/version/list", VersionList)
		cAPI.PUT("/version/update", UpdateVersion)
		cAPI.DELETE("/version/delete", DeleteVersion)
		cAPI.POST("/version/batch", Batch) // 批量更新runner版本
	}
}
