package group

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
	Enable *bool `json:"enable"` //  是否启用
}

type PageRequest struct {
	request.PageRequest
	Name     string `json:"name"`     //  分组名称
	Identity string `json:"identity"` //  分组标识
	Order    int32  `json:"order"`    //  排序
	Enable   bool   `json:"enable"`   //  是否启用
}

type AddRequest struct {
	Name     string `json:"name"`     //  分组名称
	Identity string `json:"identity"` //  分组标识
	Order    int32  `json:"order"`    //  排序
	Enable   bool   `json:"enable"`   //  是否启用
	Remark   string `json:"remark"`   //  备注
}

type UpdateRequest struct {
	UUID     string `json:"uuid" form:"uuid"`
	Name     string `json:"name"`     //  分组名称
	Identity string `json:"identity"` //  分组标识
	Order    int32  `json:"order"`    //  排序
	Enable   *bool  `json:"enable"`   //  是否启用
	Online   *bool  `json:"online"`   //  是否启用
	Remark   string `json:"remark"`   //  备注
}

type ListGroupStepsRequest struct {
	Enable *bool `json:"enable"`
	Online *bool `json:"online"` //  是否上线
}
