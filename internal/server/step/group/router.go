package group

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/pipeline/group")
	{
		//cAPI.Use(auth.Handler())
		cAPI.Use(middleware.AuthUToken())
		cAPI.POST("/info", Info)
		cAPI.POST("/page", Page)
		cAPI.POST("/list", List)
		cAPI.POST("/steps", Steps)
		cAPI.POST("/add", Add)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
	}
}
