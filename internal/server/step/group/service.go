package group

import (
	"context"
	"sort"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"pipeline/internal/server/step/step"
	"pipeline/pkg/common/response"
)

type groupService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewGroupService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *groupService {
	svc := &groupService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *groupService) Info(uuid string) (*InfoResponse, error) {
	repo := NewGroupRepo(s.db)
	m, err := repo.GetByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *groupService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewGroupRepo(s.db)
	var m PipelineStepGroup
	err = copier.Copy(&m, &r)
	m.Create(ops)
	if err != nil {
		return 0, err
	}
	return repo.Add(&m)
}

func (s *groupService) Delete(ids []string, ops string) error {
	repo := NewGroupRepo(s.db)
	return repo.Delete(ids)
}

func (s *groupService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewGroupRepo(s.db)
	m, err := repo.GetByUUID(r.UUID)
	if err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	return repo.Update(m)
}

func (s *groupService) Page(r PageRequest) (response.PageModel, error) {
	repo := NewGroupRepo(s.db)
	return repo.Page(r)
}

func (s *groupService) List(r ListRequest) (results []*PipelineStepGroup, err error) {
	repo := NewGroupRepo(s.db)
	return repo.List(r)
}

func (s *groupService) GroupSteps(req ListGroupStepsRequest) ([]StepsResponse, error) {
	repo := NewGroupRepo(s.db)
	groups, err := repo.List(ListRequest{
		Enable: req.Enable,
	})
	if err != nil {
		return nil, err
	}
	groupMap := make(map[string]StepsResponse, 0)
	for _, group := range groups {
		groupMap[group.UUID] = StepsResponse{
			PipelineStepGroup: *group,
		}
	}
	stepRepo := step.NewStepRepo(s.db)
	list, err := stepRepo.List(step.ListRequest{
		Online: req.Online,
	})
	if err != nil {
		return nil, err
	}
	//notGroup := StepsResponse{
	//	PipelineStepGroup: PipelineStepGroup{
	//		Name:  "未知分组",
	//		Order: 100000000,
	//	},
	//	List: make([]step.PipelineStep, 0),
	//}
	ret := make([]StepsResponse, 0)
	for _, step := range list {
		if g, ok := groupMap[step.Group]; ok {
			g.List = append(g.List, *step)
			groupMap[step.Group] = g
		}
	}
	//构造列表
	for _, stepsResponse := range groupMap {
		ret = append(ret, stepsResponse)
	}
	//if len(notGroup.List) > 0 {
	//	ret = append(ret, notGroup)
	//}
	//排序
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].Order < ret[j].Order
	})
	//组内排序
	for _, steps := range ret {
		sort.Slice(steps.List, func(i, j int) bool {
			return steps.List[i].Order < steps.List[j].Order
		})
	}
	return ret, nil
}
