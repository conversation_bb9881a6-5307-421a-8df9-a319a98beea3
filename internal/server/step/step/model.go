package step

import "pipeline/pkg/common/model"

// PipelineStep  pipeline_step
type PipelineStep struct {
	model.BaseModel
	Group       string `gorm:"column:group" db:"group" json:"group"`                   //  所属分组
	Name        string `gorm:"column:name" db:"name" json:"name"`                      //  流水线名称
	Identity    string `gorm:"column:identity" db:"identity" json:"identity"`          //  流水线标识
	Content     string `gorm:"column:content" db:"content" json:"content"`             //  step表单配置正文
	Description string `gorm:"column:description" db:"description" json:"description"` //  描述
	Icon        string `gorm:"column:icon" db:"icon" json:"icon"`                      //  图标
	Order       int32  `gorm:"column:order" db:"order" json:"order"`                   //  排序
	Enable      *bool  `gorm:"column:enable" db:"enable" json:"enable"`                //  是否启用
	Online      *bool  `gorm:"column:online" db:"online" json:"online"`                //  是否启用
}

func (PipelineStep) TableName() string {
	return "pipeline_step"
}
