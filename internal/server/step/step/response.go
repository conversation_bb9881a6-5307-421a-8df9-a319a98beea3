package step

import "time"

type InfoResponse struct {
	Id          int64     `json:"id"`           //  主键
	Uuid        string    `json:"uuid"`         //  UUID
	Group       string    `json:"group"`        //  所属分组
	Name        string    `json:"name"`         //  流水线名称
	Identity    string    `json:"identity"`     //  流水线标识
	Content     string    `json:"content"`      //  step表单配置正文
	Description string    `json:"description"`  //  描述
	Icon        string    `json:"icon"`         //  图标
	Order       int32     `json:"order"`        //  排序
	Enable      bool      `json:"enable"`       //  是否启用
	Online      bool      `json:"online"`       //  是否启用
	Creator     string    `json:"creator"`      //  创建人
	Modifier    string    `json:"modifier"`     //  修改人
	GmtCreate   time.Time `json:"gmt_create"`   //  创建时间
	GmtModified time.Time `json:"gmt_modified"` //  修改时间
	Deleted     string    `json:"deleted"`      //  逻辑删除
}
