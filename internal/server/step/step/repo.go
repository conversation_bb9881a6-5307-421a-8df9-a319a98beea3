package step

import (
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"
)

type stepRepo struct {
	db *gorm.DB
}

func NewStepRepo(db *gorm.DB) *stepRepo {
	return &stepRepo{db: db}
}

func (repo *stepRepo) Info(id int64) (*PipelineStep, error) {
	var result PipelineStep
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *stepRepo) GetByUUID(uuid string) (*PipelineStep, error) {
	var result PipelineStep
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *stepRepo) Add(m *PipelineStep) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *stepRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&PipelineStep{}).Error
}

func (repo *stepRepo) Update(m *PipelineStep) (rowsAffected int64, err error) {

	db := repo.db.Model(PipelineStep{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *stepRepo) Page(r PageRequest) (pm response.PageModel, err error) {
	query := repo.db.Model(PipelineStep{})
	if r.Group != "" {
		query = query.Where("`group` = ?", r.Group)
	}
	if r.Name != "" {
		query = query.Where("name like ?", util.Like(r.Name))
	}
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*PipelineStep
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *stepRepo) List(r ListRequest) (list []*PipelineStep, err error) {
	query := repo.db.Model(PipelineStep{})
	if r.Online != nil {
		query = query.Where("online = ?", r.Online)
	}
	if err := query.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *stepRepo) GetByIdentity(identity string) (p *PipelineStep, err error) {
	err = repo.db.Where("identity = ?", identity).First(&p).Error
	if err != nil {
		return nil, err
	}
	return p, nil
}
