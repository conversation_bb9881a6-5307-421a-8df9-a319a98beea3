package step

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
	Enable *bool `json:"enable"` //  是否启用
	Online *bool `json:"online"` //  是否启用
}

type PageRequest struct {
	request.PageRequest
	Group       string `json:"group"`       //  所属分组
	Name        string `json:"name"`        //  流水线名称
	Identity    string `json:"identity"`    //  流水线标识
	Content     string `json:"content"`     //  step表单配置正文
	Description string `json:"description"` //  描述
	Icon        string `json:"icon"`        //  图标
	Order       int32  `json:"order"`       //  排序
	Enable      *bool  `json:"enable"`      //  是否启用
	Online      *bool  `json:"online"`      //  是否启用
}

type AddRequest struct {
	Group       string `json:"group"`       //  所属分组
	Name        string `json:"name"`        //  流水线名称
	Identity    string `json:"identity"`    //  流水线标识
	Content     string `json:"content"`     //  step表单配置正文
	Description string `json:"description"` //  描述
	Icon        string `json:"icon"`        //  图标
	Order       int32  `json:"order"`       //  排序
	Enable      bool   `json:"enable"`      //  是否启用
	Online      bool   `json:"online"`      //  是否启用
}

type UpdateRequest struct {
	UUID        string `json:"uuid" form:"uuid"`
	Group       string `json:"group"`       //  所属分组
	Name        string `json:"name"`        //  流水线名称
	Identity    string `json:"identity"`    //  流水线标识
	Content     string `json:"content"`     //  step表单配置正文
	Description string `json:"description"` //  描述
	Icon        string `json:"icon"`        //  图标
	Order       int32  `json:"order"`       //  排序
	Enable      bool   `json:"enable"`      //  是否启用
	Online      bool   `json:"online"`      //  是否启用
}
