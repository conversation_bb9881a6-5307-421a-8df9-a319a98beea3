package step

import (
	"context"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
)

type stepService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewStepService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *stepService {
	svc := &stepService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *stepService) Info(uuid string) (*InfoResponse, error) {
	repo := NewStepRepo(s.db)
	m, err := repo.GetByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *stepService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewStepRepo(s.db)
	var m PipelineStep
	err = copier.Copy(&m, &r)
	m.Create(ops)
	if err != nil {
		return 0, err
	}
	return repo.Add(&m)
}

func (s *stepService) Delete(ids []string, ops string) error {
	repo := NewStepRepo(s.db)
	return repo.Delete(ids)
}

func (s *stepService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewStepRepo(s.db)
	m, err := repo.GetByUUID(r.UUID)
	if err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	return repo.Update(m)
}

func (s *stepService) Page(r PageRequest) (response.PageModel, error) {
	repo := NewStepRepo(s.db)
	return repo.Page(r)
}

func (s *stepService) List(r ListRequest) (results []*PipelineStep, err error) {
	repo := NewStepRepo(s.db)
	return repo.List(r)
}

func (s *stepService) Content(identity string) (any, error) {
	repo := NewStepRepo(s.db)
	step, err := repo.GetByIdentity(identity)
	if err != nil {
		return nil, err
	}
	var ret any
	//解析step成json
	err = yaml.Unmarshal([]byte(step.Content), &ret)
	if err != nil {
		return nil, err
	}
	return ret, nil
}
