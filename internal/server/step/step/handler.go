package step

import (
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
)

func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewStepService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewStepService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Add(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewStepService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Update(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewStepService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewStepService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, response.PageModel{
		List: list,
	})
}

func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewStepService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

func Content(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.ContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewStepService(ts, mysql.GetDB(), redis.GetClient())
	content, err := svc.Content(req.Identity)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, content)
}
