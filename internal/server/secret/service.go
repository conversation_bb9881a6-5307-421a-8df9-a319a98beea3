package secret

import (
	"context"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
)

type SecretService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewSecretService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *SecretService {
	svc := &SecretService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *SecretService) Page(r PageRequest) (response.PageModel, error) {
	repo := NewSecretRepo(s.db)
	return repo.Page(r)
}

func (s *SecretService) List(r ListRequest) (results []*PipelineSecret, err error) {
	repo := NewSecretRepo(s.db)
	list, err := repo.List(r)
	if err != nil {
		return nil, err
	}
	if r.<PERSON>Value {
		return list, nil
	}
	//hide secret value
	for i := 0; i < len(list); i++ {
		list[i].Value = ""
	}
	return list, nil
}
