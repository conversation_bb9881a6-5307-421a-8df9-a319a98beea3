package secret

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	// 流水线uuid
	PipelineUuid string `json:"pipelineUuid"`
	// 是否解密
	Decrypt bool `json:"-"`
	// 是否展示value
	ShowValue bool `json:"-"`
}

type PageRequest struct {
	request.PageRequest
	PipelineUuid string `json:"pipeline_uuid"` //  所属流水线
	Key          string `json:"key"`           //  环境变量名
	Value        string `json:"value"`         //  环境变量值
}

type AddRequest struct {
	PipelineUuid string `json:"pipeline_uuid"` //  所属流水线
	Key          string `json:"key"`           //  环境变量名
	Value        string `json:"value"`         //  环境变量值
}

type UpdateRequest struct {
	ID           int64  `json:"id" form:"id"`
	PipelineUuid string `json:"pipeline_uuid"` //  所属流水线
	Key          string `json:"key"`           //  环境变量名
	Value        string `json:"value"`         //  环境变量值
}
