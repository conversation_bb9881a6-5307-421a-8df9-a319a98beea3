package secret

import (
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
	"pipeline/pkg/common/response"
)

func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSecretService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSecretService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req)
	//conv
	formatList := make([]InfoResponse, 0)
	for _, secret := range list {
		formatList = append(formatList, InfoResponse{
			Key:   secret.Key,
			Value: secret.Value,
		})
	}
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, response.PageModel{
		List: formatList,
	})
}
