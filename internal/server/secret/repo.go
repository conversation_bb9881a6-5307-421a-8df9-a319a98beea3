package secret

import (
	"gorm.io/gorm"
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
)

type secretRepo struct {
	db *gorm.DB
}

func NewSecretRepo(db *gorm.DB) *secretRepo {
	return &secretRepo{db: db}
}

func (repo *secretRepo) Info(id int64) (*PipelineSecret, error) {
	var result PipelineSecret
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *secretRepo) Add(secrets ...*PipelineSecret) error {
	// 加密
	var err error
	for i, s := range secrets {
		if secrets[i].Value, err = common.DefaultEncrypt(s.Value); err != nil {
			return err
		}
	}
	return repo.db.Create(secrets).Error
}

func (repo *secretRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&PipelineSecret{}).Error
}

func (repo *secretRepo) Update(m *PipelineSecret) (rowsAffected int64, err error) {
	db := repo.db.Model(PipelineSecret{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *secretRepo) Page(r PageRequest) (pm response.PageModel, err error) {
	query := repo.db.Model(PipelineSecret{})
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*PipelineSecret
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *secretRepo) List(r ListRequest) (list []*PipelineSecret, err error) {
	if err := repo.db.Where("pipeline_uuid = ?", r.PipelineUuid).Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	// decrypt
	if r.Decrypt {
		for _, item := range list {
			value, err := common.DefaultDecrypt(item.Value)
			if err != nil {
				return nil, err
			}
			item.Value = value
		}
	}
	return list, nil
}

func (repo *secretRepo) Save(secrets ...*PipelineSecret) error {
	// 加密
	var err error
	for i, s := range secrets {
		if secrets[i].Value, err = common.DefaultEncrypt(s.Value); err != nil {
			return err
		}
	}
	return repo.db.Save(secrets).Error
}

func (repo *secretRepo) DeleteByPipelineUUID(ids []string) error {
	return repo.db.Where("pipeline_uuid in (?)", ids).Delete(&PipelineSecret{}).Error
}
