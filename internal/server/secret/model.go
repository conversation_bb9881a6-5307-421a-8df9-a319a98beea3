package secret

import (
	"time"

	"pipeline/pkg/util"
)

// PipelineSecret  pipeline_secret
type PipelineSecret struct {
	ID           int64     `gorm:"column:id" db:"id" json:"id" form:"id"`                                           //  主键
	UUID         string    `gorm:"column:uuid" db:"uuid" json:"uuid" form:"uuid"`                                   //  所属流水线
	PipelineUUID string    `gorm:"column:pipeline_uuid" db:"pipeline_uuid" json:"pipelineUUID" form:"pipelineUUID"` //  所属流水线
	Key          string    `gorm:"column:key" db:"key" json:"key"`                                                  //  环境变量名
	Value        string    `gorm:"column:value" db:"value" json:"value"`                                            //  环境变量值
	Creator      string    `gorm:"column:creator" db:"creator" json:"creator" form:"creator"`                       //  创建人
	Modifier     string    `gorm:"column:modifier" db:"modifier" json:"modifier" form:"modifier"`                   //  修改人
	GmtCreate    time.Time `gorm:"column:gmt_create" db:"gmt_create" json:"gmtCreate" form:"gmt_create"`            //  创建时间
	GmtModified  time.Time `gorm:"column:gmt_modified" db:"gmt_modified" json:"gmtModified" form:"gmt_modified"`    //  修改时间
}

func (b *PipelineSecret) Create(ops string) {
	b.Creator = ops
	b.GmtCreate = time.Now()
	b.UUID = util.UUID()
}

func (b *PipelineSecret) Update(ops string) {
	b.Modifier = ops
	b.GmtModified = time.Now()
}

func (*PipelineSecret) TableName() string {
	return "pipeline_secret"
}
