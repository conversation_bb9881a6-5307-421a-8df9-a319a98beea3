package pipeline

import (
	"time"

	"pipeline/pkg/common/model"
)

type InfoResponse struct {
	ID           int64       `gorm:"column:id" db:"id" json:"id" form:"id"`         //  主键
	UUID         string      `gorm:"column:uuid" db:"uuid" json:"uuid" form:"uuid"` //  uuid
	AppUuid      string      `json:"appUuid"`                                       //  所属应用
	Name         string      `json:"name"`                                          //  流水线名称
	Content      string      `json:"content"`                                       //  流水线正文
	TriggerTime  *model.Time `json:"triggerTime"`                                   //  最新触发时间
	LatestStatus string      `json:"latestStatus"`                                  //  最新执行状态
	BuildNumber  int64       `json:"buildNumber"`                                   //  最新构建号
	Description  string      `json:"description"`                                   //  描述
	Creator      string      `json:"creator"`                                       //  创建人
	Modifier     string      `json:"modifier"`                                      //  修改人
	GmtCreate    time.Time   `json:"gmtCreate"`                                     //  创建时间
	GmtModified  time.Time   `json:"gmtModified"`                                   //  修改时间
}

type StateResponse struct {
	Active bool   `json:"active"`
	State  string `json:"state,omitempty"`
}
