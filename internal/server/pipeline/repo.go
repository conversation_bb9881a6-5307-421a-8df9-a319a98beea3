package pipeline

import (
	"time"

	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/util"

	"gorm.io/gorm"
)

type infoRepo struct {
	db *gorm.DB
}

func NewInfoRepo(db *gorm.DB) *infoRepo {
	return &infoRepo{db: db}
}

func (repo *infoRepo) Info(id int64) (*models.PipelineInfo, error) {
	var result models.PipelineInfo
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *infoRepo) InfoByUUID(uuid string) (*models.PipelineInfo, error) {
	var result models.PipelineInfo
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *infoRepo) Add(m *models.PipelineInfo) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *infoRepo) AddBatch(m []*models.PipelineInfo) error {
	db := repo.db.Create(m)
	if err := db.Error; err != nil {
		return err
	}
	return nil
}

func (repo *infoRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.PipelineInfo{}).Error
}

func (repo *infoRepo) SoftDelete(ids []string) error {
	return repo.db.Model(models.PipelineInfo{}).Where("uuid in (?)", ids).Update("deleted", true).Error
}

func (repo *infoRepo) SoftDeleteByAppUUID(uuid string) error {
	return repo.db.Model(models.PipelineInfo{}).Where("app_uuid = ?", uuid).Update("deleted", true).Error
}

func (repo *infoRepo) Update(m *models.PipelineInfo) (rowsAffected int64, err error) {
	db := repo.db.Model(models.PipelineInfo{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *infoRepo) Page(r PageRequest) (pm response.PageModelV2[InfoResponse], err error) {
	query := repo.db.Model(models.PipelineInfo{})
	if r.AppUuid != "" {
		query = query.Where("app_uuid = ?", r.AppUuid)
	}
	if r.Name != "" {
		query = query.Where("name like ? or uuid = ?", util.Like(r.Name), r.Name)
	}
	// 默认查询未删除的
	query = query.Where("deleted = false")

	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []InfoResponse
	if err = query.Order("gmt_create desc").Limit(limit).
		Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *infoRepo) List(r ListRequest) (list []*models.PipelineInfo, err error) {
	query := repo.db.Model(models.PipelineInfo{})
	if r.AppUUID != "" {
		query.Where("app_uuid = ?", r.AppUUID)
	}
	if r.Deleted != nil {
		query.Where("deleted = ?", *r.Deleted)
	}
	if r.Order != "" {
		query.Order(r.Order)
	} else {
		query.Order("gmt_create desc")
	}
	if r.PageRequest.Page > 0 && r.PageRequest.PageSize > 0 {
		offset := (r.PageRequest.Page - 1) * r.PageRequest.PageSize
		query = query.Offset(offset).Limit(r.PageRequest.PageSize)
	}
	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *infoRepo) UpdateLatestTriggerTime(uuid string, t time.Time) error {
	return repo.db.Model(models.PipelineInfo{}).Where("uuid = ?", uuid).Update("trigger_time", t).Error
}

func (repo *infoRepo) GetByUUID(uuid string) (*models.PipelineInfo, error) {
	var result models.PipelineInfo
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *infoRepo) IncrementBuildNumber(uuid, status string) (int64, error) {
	now := time.Now()
	// 先更新build_number
	if err := repo.db.Model(models.PipelineInfo{}).Where("uuid = ?", uuid).UpdateColumns(map[string]any{
		"build_number":  gorm.Expr("build_number + ?", 1),
		"trigger_time":  now,
		"latest_status": status,
	}).Error; err != nil {
		return 0, err
	}
	// 然后获取更新后的build_number
	var buildNumber int64
	if err := repo.db.Model(models.PipelineInfo{}).Where("uuid = ?", uuid).Pluck("build_number", &buildNumber).Error; err != nil {
		return 0, err
	}
	// 返回更新后的build_number
	return buildNumber, nil
}

func (repo *infoRepo) GetAppByUUID(uuid string) (*models.ProjectApp, error) {
	var result models.ProjectApp
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *infoRepo) GetAppByPipelineUUID(uuid string) (*models.ProjectApp, error) {
	var appUUID string
	if err := repo.db.Model(models.PipelineInfo{}).
		Where("uuid = ?", uuid).
		Pluck("app_uuid", &appUUID).Error; err != nil {
		return nil, err
	}
	return repo.GetAppByUUID(appUUID)
}

// UpdateLatestStatus 更新流水线最新状态
func (repo *infoRepo) UpdateLatestStatus(pipelineUUID string, buildNumber int64, status string) error {
	return repo.db.Model(models.PipelineInfo{}).
		Where("uuid = ? and build_number = ?", pipelineUUID, buildNumber).
		Update("latest_status", status).Error
}
