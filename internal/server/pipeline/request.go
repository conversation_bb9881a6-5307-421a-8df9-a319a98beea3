package pipeline

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
	AppUUID string `json:"appUuid"` //  所属应用
	Deleted *bool
}

type PageRequest struct {
	request.PageRequest
	AppUuid string `json:"appUuid" binding:"required"` //  所属应用
	Name    string `json:"name"`                       //  流水线名称
}

type AddRequest struct {
	UUID        string        `json:"uuid" binding:"required"`    //  由前端生成uuid
	AppUuid     string        `json:"appUuid" binding:"required"` //  所属应用
	Name        string        `json:"name" binding:"required"`    //  流水线名称
	Content     string        `json:"content" binding:"required"` //  流水线正文
	Description string        `json:"description"`                //  描述
	Secret      []SecretEntry `json:"secret"`                     //  描述
}

type AddByTemplateRequest struct {
	UUID    string         `json:"uuid" binding:"required"`    //  Template uuid
	AppUuid string         `json:"appUuid" binding:"required"` //  所属应用
	Param   map[string]any `json:"param"`                      //  描述
}

type UpdateRequest struct {
	request.UpdateRequest
	AppUuid     string        `json:"appUuid" binding:"required"` //  所属应用
	Name        string        `json:"name" binding:"required"`    //  流水线名称
	Content     string        `json:"content" binding:"required"` //  流水线正文
	Description string        `json:"description"`                //  描述
	Secret      []SecretEntry `json:"secret"`                     //  秘钥
}

type CopyPipelineRequest struct {
	UUID string `json:"uuid"`
}

type SecretEntry struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
