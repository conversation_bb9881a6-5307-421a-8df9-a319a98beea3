package pipeline

import (
	"context"
	"fmt"
	"pipeline/config"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/oss"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
)

// 注册到事件总线
func init() {
	eventbus.Register(CronJobUpdateEventHandler)
	eventbus.Register(DeletePipelineEventHandler)
}

// CronJobUpdateEventHandler handles cron job update events
func CronJobUpdateEventHandler(ctx context.Context, event *events.PipelineCronJobUpdateEvent) error {

	svc := NewInfoService(nil, mysql.GetDB(), redis.GetClient())
	// get pipeline by uuid
	pipeline, err := svc.GetByUUID(event.UUID)
	if err != nil {
		log.Error("get pipeline failed", log.Any("pipeline uuid", event.UUID), log.Any("error", err))
		return err
	}
	event.Pipeline = pipeline

	// 因为cronjob是单例执行的,所以需要发布到redis,由worker去处理新增、更新、删除等操作
	err = redis.GetClient().Publish(ctx, CronJobEventChannel, event.Marshal()).Err()
	if err != nil {
		log.Error("publish cron job event failed", log.Any("error", err))
		return err
	}

	return nil
}

// DeletePipelineEventHandler 流水线删除事件处理器
func DeletePipelineEventHandler(ctx context.Context, event *events.DeletePipelineEvent) error {
	// 1. 清理掉cronjob,如果有
	if err := eventbus.Publish(context.Background(),
		events.NewPipelineDeletedEvent(event.UUID)); err != nil {
		log.Error("publish pipeline deleted event failed", log.Any("error", err))
	}
	// 2. 清理历史执行记录
	appInfo, err := NewInfoRepo(mysql.GetDB()).GetAppByPipelineUUID(event.UUID)
	if err != nil {
		log.Error("get app info failed", log.Any("error", err))
		return err
	}
	pipelineStoragePath := fmt.Sprintf("%s/%s", appInfo.Identity, event.UUID)
	return oss.GetService().Delete(ctx, config.Items().OSS.LoggerBucketName, pipelineStoragePath)
}
