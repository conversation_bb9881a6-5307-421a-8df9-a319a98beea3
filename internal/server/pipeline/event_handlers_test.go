package pipeline

import (
	"context"

	"pipeline/pkg/common/model"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("PipelineEventHandlers", func() {
	var (
		ctx      context.Context
		rediscli *redis.Client
		ms       *miniredis.Miniredis
		err      error
	)

	BeforeEach(func() {
		ctx = context.TODO()
		ms, err = miniredis.Run()
		Expect(err).Should(BeNil())
		rediscli = redis.NewClient(&redis.Options{
			Addr: ms.Addr(),
		})
	})

	Context("PipelineEventHandler", func() {
		var event *events.PipelineCronJobUpdateEvent
		When("Publish pipeline Event", func() {
			BeforeEach(func() {
				event = &events.PipelineCronJobUpdateEvent{
					Type: events.PipelineCreatedType,
					Pipeline: &models.PipelineInfo{
						BaseModel: model.BaseModel{
							UUID: "93f101be493d11ecb7c254e1ad134d77",
						},
						Name: "test",
					},
				}

			})
			It("should publish pipeline event", func() {
				started := make(chan struct{})
				// subscribe event
				go func() {
					sub := rediscli.Subscribe(ctx, CronJobEventChannel)
					ch := sub.Channel()

					// Check if the subscription is successful
					_, err := sub.Receive(ctx)
					if err != nil {
						return
					}
					// Signal that the subscription is ready
					close(started)

					msg, ok := <-ch
					Expect(ok).Should(BeTrue())
					Expect(msg.Payload).Should(Equal(event.Marshal()))
				}()

				// Wait for the subscription goroutine to start
				<-started

				subNum := ms.Publish(CronJobEventChannel, event.Marshal())
				Expect(subNum).Should(Equal(1))
			})
		})
	})

})
