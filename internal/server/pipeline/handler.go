package pipeline

import (
	"context"
	"git.makeblock.com/makeblock-go/log"

	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Add(req, c.GetString(common.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}

	if err = eventbus.Publish(context.Background(),
		events.NewPipelineCreatedEvent(info.UUID)); err != nil {
		response.ErrorResponse(c, err)
		return
	}

	response.OK(c, info)
}

func AddByTemplate(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddByTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.AddByTemplate(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}

func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Update(req, c.GetString(common.UserName))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	// 发布更新事件
	if err = eventbus.Publish(context.Background(),
		events.NewPipelineUpdatedEvent(req.UUID)); err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, info)
}

func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, response.PageModel{
		List: list,
	})
}

func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// main thread not affected by error
	for _, uuid := range req.UUIDs {
		if err = eventbus.Publish(context.Background(),
			&events.DeletePipelineEvent{UUID: uuid}); err != nil {
			log.ErrorE("failed to publish delete pipeline event", err)
		}
	}
	response.OK(c, nil)
}

func Copy(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req CopyPipelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Copy(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	// 发布更新事件
	if err = eventbus.Publish(context.Background(),
		events.NewPipelineCreatedEvent(info.UUID)); err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, info)
}

// State get pipeline latest state
func State(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	pipelineUUID := c.Param("uuid")
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.State(pipelineUUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}
