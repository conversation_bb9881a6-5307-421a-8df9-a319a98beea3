package pipeline

import (
	"bytes"
	"context"
	"fmt"
	"pipeline/internal/server/app/plugins"
	"pipeline/pkg/util"
	textTemplate "text/template"
	"time"

	"pipeline/internal/server/secret"
	"pipeline/internal/server/template/template"
	"pipeline/pkg/common/model"
	"pipeline/pkg/common/response"
	"pipeline/pkg/pipeline"

	"pipeline/pkg/models"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"
)

type InfoService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewInfoService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *InfoService {
	svc := &InfoService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *InfoService) GetByID(id int64) (*models.PipelineInfo, error) {
	repo := NewInfoRepo(s.db)
	return repo.Info(id)
}

func (s *InfoService) Info(uuid string) (*InfoResponse, error) {
	repo := NewInfoRepo(s.db)
	m, err := repo.InfoByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *InfoService) Add(r AddRequest, ops string) (*models.PipelineInfo, error) {
	repo := NewInfoRepo(s.db)
	var m models.PipelineInfo
	if err := copier.Copy(&m, &r); err != nil {
		return nil, err
	}
	m.Create(ops)
	// 使用前端生成的uuid
	m.UUID = r.UUID
	// Validate the pipeline
	if isValid, err := validatePipeline(m.Content); err != nil || !isValid {
		return nil, err
	}
	// 处理秘钥
	secures := make([]*secret.PipelineSecret, 0)
	for _, entry := range r.Secret {
		se := &secret.PipelineSecret{
			Key:          entry.Key,
			Value:        entry.Value,
			PipelineUUID: m.UUID,
		}
		se.Create(ops)
		secures = append(secures, se)
	}
	// 保存秘钥
	if err := secret.NewSecretRepo(s.db).Add(secures...); err != nil {
		return nil, err
	}
	// 保存流水线
	if _, err := repo.Add(&m); err != nil {
		return nil, err
	}
	return &m, nil
}

func (s *InfoService) AddByTemplate(r AddByTemplateRequest, ops string) error {
	repo := NewInfoRepo(s.db)
	templateRepo := template.NewTemplateRepo(s.db)
	templateInfo, err := templateRepo.Info(r.UUID)
	if err != nil {
		return err
	}
	// 渲染模板
	parse, err := textTemplate.New("pipeline").Parse(templateInfo.Content)
	if err != nil {
		return err
	}
	// 查询应用
	appInfo, err := repo.GetAppByUUID(r.AppUuid)
	if err != nil {
		return err
	}
	// 从模板中解析出配置插件
	var configPluginMaps map[string]any
	configPluginMaps, err = plugins.CreateAppConfigPlugin(templateInfo.UUID,
		plugins.CreateConfigPluginRequest{
			Ops:      ops,
			App:      appInfo,
			Params:   r.Param,
			Template: templateInfo.UUID,
		})
	if err != nil {
		return nil
	}
	r.Param = util.MergeMapAny(r.Param, configPluginMaps)
	// 执行渲染
	var buf bytes.Buffer
	err = parse.Execute(&buf, r.Param)
	if err != nil {
		return err
	}
	// 可能是多个流水线,按照yaml分割
	pipelines := bytes.Split(buf.Bytes(), []byte("---"))
	pipelineModel := make([]*models.PipelineInfo, 0)
	type PipelineDefine struct {
		Name string
		Desc string
	}
	for _, content := range pipelines {
		if len(content) == 0 {
			continue
		}
		define := string(content)
		var parsePipeline PipelineDefine
		err := yaml.Unmarshal([]byte(define), &parsePipeline)
		if err != nil {
			return err
		}
		m := &models.PipelineInfo{
			AppUuid: r.AppUuid,
			Content: define,
			//从yaml中解析出name和description
			Name:        parsePipeline.Name,
			Description: parsePipeline.Desc,
		}
		m.Create(ops)
		pipelineModel = append(pipelineModel, m)
	}

	return repo.AddBatch(pipelineModel)
}

// Check if all stages exist and are in the correct order
// Check for any missing or incorrect configuration
// Return true if the pipeline is valid, false otherwise
func validatePipeline(pipelineContent string) (bool, error) {
	// Parse the pipeline content
	pipelineDefine, err := pipeline.ParsePipeline(pipelineContent)
	if err != nil {
		return false, err
	}
	// must have at least one stage in the pipeline
	if len(pipelineDefine.Stages) == 0 {
		return false, response.PipelineInvalidError("pipeline must have at least one stage")
	}
	// must have at least one step in each stage
	for idx, stage := range pipelineDefine.Stages {
		if len(stage.Steps) == 0 {
			return false, response.PipelineInvalidError(fmt.Sprintf("stage %d must have at least one step", idx+1))
		}
	}
	// All step must have a name and exist in the pipeline step table

	// TODO:需要验证制品名称不能重复

	return true, nil
}

func (s *InfoService) Delete(ids []string, _ string) error {
	repo := NewInfoRepo(s.db)
	return repo.SoftDelete(ids)
}

func (s *InfoService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewInfoRepo(s.db)
	m, err := repo.InfoByUUID(r.UUID)
	if err != nil {
		return 0, err
	}
	// 验证版本是否一致
	if err = model.CheckVersionConflict(r.GmtModified, m.GmtModified); err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	// Validate the pipeline
	if isValid, validateErr := validatePipeline(m.Content); validateErr != nil || !isValid {
		return 0, validateErr
	}
	//更新秘钥
	updateSecretes := make([]*secret.PipelineSecret, 0)
	secureMaps := make(map[string]*secret.PipelineSecret)
	deleteIds := make([]string, 0)
	sr := secret.NewSecretRepo(s.db)
	list, err := sr.List(secret.ListRequest{
		PipelineUuid: m.UUID,
	})
	if err != nil {
		return 0, err
	}
	for i, pipelineSecret := range list {
		secureMaps[pipelineSecret.Key] = list[i]
	}
	for _, entry := range r.Secret {
		if _, ok := secureMaps[entry.Key]; ok {
			//更新(只有不是空值才会更新)
			if entry.Value != "" {
				secureMaps[entry.Key].Update(ops)
				secureMaps[entry.Key].Value = entry.Value
				updateSecretes = append(updateSecretes, secureMaps[entry.Key])
			}
			delete(secureMaps, entry.Key)
		} else {
			//新增
			add := &secret.PipelineSecret{
				Key:          entry.Key,
				Value:        entry.Value,
				PipelineUUID: m.UUID,
			}
			add.Create(ops)
			updateSecretes = append(updateSecretes, add)
		}
	}
	//保存or更新
	if len(updateSecretes) > 0 {
		err = sr.Save(updateSecretes...)
		if err != nil {
			return 0, err
		}
	}
	//map中剩余的是需要删除的
	for _, v := range secureMaps {
		deleteIds = append(deleteIds, v.UUID)
	}
	//删除
	if len(deleteIds) > 0 {
		err = sr.Delete(deleteIds)
		if err != nil {
			return 0, err
		}
	}
	return repo.Update(m)
}

func (s *InfoService) Page(r PageRequest) (*response.PageModelV2[InfoResponse], error) {
	repo := NewInfoRepo(s.db)
	page, err := repo.Page(r)
	if err != nil {
		return nil, err
	}
	// remove entry content, because it's too long and not necessary
	for i := range page.List {
		page.List[i].Content = ""
	}
	return &page, nil
}

func (s *InfoService) List(r ListRequest) (results []*models.PipelineInfo, err error) {
	repo := NewInfoRepo(s.db)
	return repo.List(r)
}

func (s *InfoService) UpdateLatestStatus(uuid string, buildNumber int64, status string) error {
	repo := NewInfoRepo(s.db)
	return repo.UpdateLatestStatus(uuid, buildNumber, status)
}

func (s *InfoService) UpdateLatestTriggerTime(uuid string, t time.Time) error {
	repo := NewInfoRepo(s.db)
	return repo.UpdateLatestTriggerTime(uuid, t)
}

func (s *InfoService) Copy(r CopyPipelineRequest, ops string) (*models.PipelineInfo, error) {
	repo := NewInfoRepo(s.db)
	m, err := repo.GetByUUID(r.UUID)
	if err != nil {
		return nil, err
	}
	m.Create(ops)
	m.ID = 0
	m.Name = m.Name + "-copy"
	m.BuildNumber = 0
	m.LatestStatus = ""
	m.TriggerTime = nil
	if _, err = repo.Add(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (s *InfoService) GetByUUID(uuid string) (*models.PipelineInfo, error) {
	repo := NewInfoRepo(s.db)
	return repo.GetByUUID(uuid)
}

// IncrementBuildNumber 增加构建号
func (s *InfoService) IncrementBuildNumber(pipelineUUID, status string) (int64, error) {
	repo := NewInfoRepo(s.db)
	return repo.IncrementBuildNumber(pipelineUUID, status)
}

func (s *InfoService) State(uuid string) (*StateResponse, error) {
	repo := NewInfoRepo(s.db)
	var activeCount int64
	if err := repo.db.Model(models.PipelineLogger{}).
		Where("pipeline_uuid = ? and status in (?)", uuid,
			[]string{pipeline.Waiting, pipeline.Blocking, pipeline.Running}).
		Count(&activeCount).Error; err != nil {
		return nil, err
	}
	return &StateResponse{
		Active: activeCount > 0,
	}, nil
}
