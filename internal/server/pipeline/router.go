package pipeline

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/pipeline/info")
	{
		cAPI.GET("/state/:uuid", middleware.RateLimit(), State) // 查询流水线最新状态(对外接口)
		// 鉴权
		cAPI.Use(middleware.AuthUToken())
		cAPI.POST("/info", Info)
		cAPI.POST("/page", Page)
		cAPI.POST("/list", List)
		cAPI.POST("/add", Add)
		cAPI.POST("/add/template", AddByTemplate)
		cAPI.POST("/copy", Copy)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
	}
}
