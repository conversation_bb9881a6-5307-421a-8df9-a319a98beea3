package pipeline

import (
	"context"
	"time"

	ct "pipeline/internal/server/trigger/cron"
	"pipeline/pkg/common/model"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	tmock "pipeline/test/mock"

	pkgpipeline "pipeline/pkg/pipeline"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"
)

var _ = Describe("cron job service", func() {
	var (
		ctx       context.Context
		rediscli  *redis.Client
		ms        *miniredis.Miniredis
		db        *gorm.DB
		mock      sqlmock.Sqlmock
		svc       *CronJobService
		err       error
		pipelines []*models.PipelineInfo
		schedule  string
	)

	BeforeEach(func() {
		ctx = context.Background()
		ms, err = miniredis.Run()
		Expect(err).To(BeNil())
		rediscli = redis.NewClient(&redis.Options{
			Addr: ms.Addr(),
		})

		db, mock, err = tmock.GetDBMock()
		Expect(err).To(BeNil())

		svc = &CronJobService{
			Ctx:    ctx,
			DB:     db,
			Client: rediscli,
			Ctrl:   ct.GetCronJobCtrl(ctx),
		}

		pipelines = getPipelines()
		schedule = "* * * * *"
	})

	AfterEach(func() {
		svc.Ctrl.EntryCtrl.CleanAll()
		svc.Ctrl.EntryIds = make(map[string]ct.PipelineEntry)
	})

	Context("run cron job service", func() {
		When("run cron job service", func() {
			It("should cancel the current operations when the distribute lock error occurs", func() {
				canceled := false
				svc.Cancel = func() {
					canceled = true
				}
				go svc.run()
				// 模拟分布式锁错误
				ms.Close()
				time.Sleep(5 * time.Second)
				Expect(canceled).To(BeTrue())
			})
		})
	})

	Context("GetPipelineSchedule", func() {
		When("schedule is nil", func() {
			It("should return error", func() {
				schedule, err := GetPipelineSchedule(pipelines[0])
				Expect(err).To(BeNil())
				Expect(schedule).To(BeEmpty())
			})
		})
		When("schedule has second", func() {
			It("should return minute schedule", func() {
				schedule, err := GetPipelineSchedule(pipelines[1])
				Expect(err).To(BeNil())
				Expect(schedule).To(Equal(schedule))
			})
		})
		When("schedule is correct", func() {
			It("should return schedule", func() {
				schedule, err := GetPipelineSchedule(pipelines[2])
				Expect(err).To(BeNil())
				Expect(schedule).To(Equal("*/3 * * * *"))
			})
		})
		When("shedule is empty", func() {
			It("should return error", func() {
				schedule, err := GetPipelineSchedule(pipelines[3])
				Expect(err).ToNot(BeNil())
				Expect(schedule).To(BeEmpty())
			})
		})
	})

	Context("InitCronJob", func() {
		When("init cron job", func() {
			BeforeEach(func() {
				mock.ExpectQuery("SELECT * FROM `pipeline_info` WHERE deleted = ? ORDER BY gmt_create desc LIMIT 100").
					WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "content"}).
						AddRow(pipelines[0].UUID, pipelines[0].Name, pipelines[0].Content).
						AddRow(pipelines[1].UUID, pipelines[1].Name, pipelines[1].Content).
						AddRow(pipelines[2].UUID, pipelines[2].Name, pipelines[2].Content).
						AddRow(pipelines[3].UUID, pipelines[3].Name, pipelines[3].Content))
			})
			It("should return error", func() {
				err := svc.initCronJob()
				Expect(err).To(BeNil())
				Expect(svc.Ctrl.EntryIds).To(HaveLen(2))
				Expect(svc.Ctrl.EntryIds[pipelines[1].UUID].Schedule).To(Equal("* * * * *"))
				Expect(svc.Ctrl.EntryIds[pipelines[2].UUID].Schedule).To(Equal("*/3 * * * *"))
			})
		})
	})

	Context("HandlePipelineEvent Create pipeline", func() {
		When("schedule is nil", func() {
			It("should not create cron job", func() {
				event := &events.PipelineCronJobUpdateEvent{
					Type:     events.PipelineCreatedType,
					Pipeline: pipelines[0],
				}

				svc.HandlePipelineEvent(event.Marshal())
				Expect(svc.Ctrl.EntryIds).To(HaveLen(0))
			})
		})
		When("schedule is correct", func() {
			It("should create cron job", func() {
				event := &events.PipelineCronJobUpdateEvent{
					Type:     events.PipelineCreatedType,
					Pipeline: pipelines[2],
				}

				svc.HandlePipelineEvent(event.Marshal())
				Expect(svc.Ctrl.EntryIds).To(HaveLen(1))
				Expect(svc.Ctrl.EntryIds[pipelines[2].UUID].Schedule).To(Equal("*/3 * * * *"))
			})
		})
	})

	Context("HandlePipelineEvent Delete pipeline", func() {
		When("pipeline event is deleted", func() {
			var p *models.PipelineInfo
			JustBeforeEach(func() {
				p = pipelines[1]
				svc.Ctrl.AddPipelineCronJob(p.UUID, p.Name, schedule, func() {})
			})
			It("should remove cron job", func() {
				event := &events.PipelineCronJobUpdateEvent{
					Type:     events.PipelineDeletedType,
					Pipeline: p,
				}
				svc.HandlePipelineEvent(event.Marshal())
				Expect(svc.Ctrl.EntryIds).To(HaveLen(0))
			})
		})
	})

	Context("HandlePipelineEvent Update pipeline", func() {
		AfterEach(func() {
			svc.Ctrl.EntryCtrl.CleanAll()
			svc.Ctrl.EntryIds = make(map[string]ct.PipelineEntry)
		})

		When("schedule is empty and pipeline cron job exists", func() {
			var p *models.PipelineInfo
			JustBeforeEach(func() {
				p = pipelines[0]
				svc.Ctrl.AddPipelineCronJob(p.UUID, p.Name, schedule, func() {})
			})
			It("should remove cron job", func() {
				event := &events.PipelineCronJobUpdateEvent{
					Type:     events.PipelineUpdatedType,
					Pipeline: p,
				}

				svc.HandlePipelineEvent(event.Marshal())
				Expect(svc.Ctrl.EntryIds).To(HaveLen(0))
			})
		})
		When("schedule is not empty and pipeline cron job not exists", func() {
			JustBeforeEach(func() {
				p := pipelines[0]
				svc.Ctrl.AddPipelineCronJob(p.UUID, p.Name, schedule, func() {})
			})
			It("should add cron job", func() {
				event := &events.PipelineCronJobUpdateEvent{
					Type:     events.PipelineUpdatedType,
					Pipeline: pipelines[2],
				}

				svc.HandlePipelineEvent(event.Marshal())
				Expect(svc.Ctrl.EntryIds).To(HaveLen(2))
				Expect(svc.Ctrl.EntryIds[pipelines[2].UUID].Schedule).To(Equal("*/3 * * * *"))
			})
		})
		When("schedule is not equal and pipeline cron job exists", func() {
			var p *models.PipelineInfo
			JustBeforeEach(func() {
				p = pipelines[2]
				svc.Ctrl.AddPipelineCronJob(p.UUID, p.Name, schedule, func() {})
			})
			It("should update cron job", func() {
				event := &events.PipelineCronJobUpdateEvent{
					Type:     events.PipelineUpdatedType,
					Pipeline: p,
				}
				Expect(svc.Ctrl.EntryIds[p.UUID].Schedule).To(Equal(schedule))

				svc.HandlePipelineEvent(event.Marshal())
				Expect(svc.Ctrl.EntryIds).To(HaveLen(1))
				Expect(svc.Ctrl.EntryIds[p.UUID].Schedule).To(Equal("*/3 * * * *"))
			})
		})
	})
})

func getPipelines() []*models.PipelineInfo {
	test2Schedule := "*/6 * * * * *"
	test3Schedule := "*/3 * * * *"
	test4Schedule := ""

	result := []*models.PipelineInfo{
		{
			BaseModel: model.BaseModel{
				UUID: "93f101be493d11ecb7c254e1ad134d71",
			},
			Name:    "test1",
			Content: getContent("test1", nil),
		},
		{
			BaseModel: model.BaseModel{
				UUID: "93f101be493d11ecb7c254e1ad134d72",
			},
			Name:    "test2",
			Content: getContent("test2", &test2Schedule),
		},
		{
			BaseModel: model.BaseModel{
				UUID: "93f101be493d11ecb7c254e1ad134d73",
			},
			Name:    "test3",
			Content: getContent("test3", &test3Schedule),
		},
		{
			BaseModel: model.BaseModel{
				UUID: "93f101be493d11ecb7c254e1ad134d74",
			},
			Name:    "test4",
			Content: getContent("test4", &test4Schedule),
		},
	}
	return result
}

func getContent(name string, schedule *string) string {
	pipelineDefine := &pkgpipeline.PipelineDefine{
		Name: name,
		Triggers: pkgpipeline.Triggers{
			Cron: schedule,
		},
	}

	defineByte, _ := yaml.Marshal(pipelineDefine)
	return string(defineByte)
}
