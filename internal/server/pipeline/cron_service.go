package pipeline

import (
	"context"
	"errors"
	"strings"
	"time"

	"pipeline/internal/server/trigger/cron"
	"pipeline/pkg/common/request"
	"pipeline/pkg/distribute"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	"pipeline/pkg/util"

	"pipeline/pkg/pipeline"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	mbredis "git.makeblock.com/makeblock-go/redis"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

const (
	// ExpireTime lock time
	ExpireTime = 30 * time.Second
	LeaseTime  = 10 * time.Second

	// CronJobDistributeLock cronjob redis distribute key
	CronJobDistributeLock = "cronjob:distribute:lock"

	CronJobEventChannel = "cronjob_event"
)

type CronJobService struct {
	*gorm.DB
	*redis.Client
	Ctx    context.Context
	Cancel context.CancelFunc
	Ctrl   *cron.CronJobController
}

func NewCronJobService(ctx context.Context) (*CronJobService, error) {
	ctx, cancel := context.WithCancel(ctx)
	svc := &CronJobService{
		Ctx:    ctx,
		Cancel: cancel,
		DB:     mysql.GetDB(),
		Client: mbredis.GetClient(),
		Ctrl:   cron.GetCronJobCtrl(ctx),
	}

	return svc, nil
}

func (s *CronJobService) ResetContext(ctx context.Context) {
	s.Ctx, s.Cancel = context.WithCancel(ctx)
}

// Run LoadCronJob loads cron job from database
func (s *CronJobService) Run(ctx context.Context) {
	log.With(log.Any("CronJob Service", "Run")).Info("start load cron job")
	for {
		select {
		// parent context done, exit cron job service
		case <-ctx.Done():
			log.Info("context done, exit cron job service")
			return
		default:
			// reset context to confirm the context is not done
			s.ResetContext(ctx)
			s.run()
			log.Info("cron job service exit, wait for next time")
			time.Sleep(30 * time.Second)
		}
	}
}

func (s *CronJobService) run() {
	log.Info("Cronjob service start to run")
	defer func() {
		if err := recover(); err != nil {
			log.Error("panic error", log.Any("error", err))
		}
	}()

	distributeLock, err := distribute.NewDistributeLock(s.Ctx,
		s.Client, CronJobDistributeLock, ExpireTime, LeaseTime)
	if err != nil {
		log.Error("new distribute lock error", log.Any("error", err))
		return
	}

	defer func() {
		unlockErr := distributeLock.Unlock()
		if unlockErr != nil {
			log.ErrorE("unlock distribute lock error", unlockErr)
		}
		s.Ctrl.EntryCtrl.CleanAll()
		s.Cancel()
	}()

	// 监听分布式锁错误，如果续约错误，调用s.Cancel()取消当前run()方法所有的操作
	go func() {
		log.Info("start to listen distribute lock error")
		for {
			select {
			case err = <-distributeLock.ErrChan:
				log.Error("distribute lock error", log.Any("error", err))
				s.Cancel()
				return
			case <-s.Ctx.Done():
				return
			}
		}
	}()

	for {
		lock, lockErr := distributeLock.Lock()
		if lockErr != nil {
			log.Error("lock error", log.Any("error", lockErr))
			return
		}
		if lock {
			log.Info("Cronjob service get lock success")
			break
		}
		log.Info("Cronjob service lock has been used, wait for next time")
		time.Sleep(ExpireTime)
	}

	// 开启续约
	go distributeLock.Lease()

	s.Ctrl.EntryCtrl.Start()

	// 初始化cron job
	_ = s.initCronJob()

	// 订阅事件
	s.SubPipelineEvent()
}

// initCronJob initializes cron job from database
func (s *CronJobService) initCronJob() error {
	log.Info("init cron job")
	repo := NewInfoRepo(s.DB)

	isDeleted := false
	req := ListRequest{
		Deleted: &isDeleted,
		PageRequest: request.PageRequest{
			Page:     1,
			PageSize: 100,
		},
	}

	for {
		pipelines, err := repo.List(req)
		if err != nil {
			log.Error("load cron job failed", log.Any("error", err))
			return err
		}

		for _, pipeline := range pipelines {
			p := pipeline
			schedule, err := GetPipelineSchedule(p)
			if err != nil {
				log.Error("get pipeline schedule failed", log.Any("error", err))
				continue
			}
			if len(schedule) == 0 {
				log.Info("pipeline schedule is empty, ignore it",
					log.Any("pipeline name", p.Name), log.Any("pipeline uuid", p.UUID))
				continue
			}

			log.Info("create pipeline cron job", log.Any("pipelineID", p.UUID),
				log.Any("pipelineName", p.Name), log.Any("schedule", schedule))
			if err := s.Ctrl.AddPipelineCronJob(p.UUID, p.Name, schedule, func() {
				CreatePipelineRun(p)
			}); err != nil {
				log.Error("add pipeline cron job failed", log.Any("error", err))
				continue
			}
		}

		if len(pipelines) < req.PageSize {
			break
		}
		req.Page++
	}

	return nil
}

// SubPipelineEvent subscribes pipeline event
func (s *CronJobService) SubPipelineEvent() {
	log.Info("subscribe pipeline event")
	sub := s.Client.Subscribe(s.Ctx, CronJobEventChannel)
	ch := sub.Channel()

	for {
		select {
		case msg, ok := <-ch:
			if !ok {
				log.Info("redis sub channel closed")
				return
			}
			s.HandlePipelineEvent(msg.Payload)
		case <-s.Ctx.Done():
			log.Info("context done, close subscribe")
			sub.Close()
			return
		}
	}
}

// HandlePipelineEvent handles pipeline event
func (s *CronJobService) HandlePipelineEvent(payload string) {
	var event events.PipelineCronJobUpdateEvent
	if err := event.Unmarshal(payload); err != nil {
		log.Error("Unmarshal cron job event failed", log.Any("error", err))
		return
	}

	switch event.Type {
	case events.PipelineCreatedType:
		s.handleCreatePipelineEvent(event.Pipeline)
	case events.PipelineDeletedType:
		s.Ctrl.RemovePipelineCronJob(event.Pipeline.UUID)
	case events.PipelineUpdatedType:
		s.handleUpdatePipelineEvent(event.Pipeline)
	}
}

// handleCreatePipelineEvent handles create pipeline event
func (s *CronJobService) handleCreatePipelineEvent(pipeline *models.PipelineInfo) {
	log.With(log.Any("func", "handleCreatePipelineEvent"),
		log.Any("uuid", pipeline.UUID), log.Any("name", pipeline.Name))

	schedule, err := GetPipelineSchedule(pipeline)
	if err != nil {
		log.Error("Get pipeline schedule failed", log.Any("error", err))
		return
	}

	if len(schedule) == 0 {
		log.Info("Handle pipeline event schedule is empty",
			log.Any("pipeline name", pipeline.Name), log.Any("pipeline uuid", pipeline.UUID))
		return
	}

	if err = s.Ctrl.AddPipelineCronJob(pipeline.UUID, pipeline.Name, schedule, func() {
		log.Info("Create pipeline run with cron job", log.Any("pipeline uuid", pipeline.UUID))
		CreatePipelineRun(pipeline)
	}); err != nil {
		log.Error("add pipeline cron job failed", log.Any("error", err))
		return
	}
}

// handleUpdatePipelineEvent handles update pipeline event
func (s *CronJobService) handleUpdatePipelineEvent(pipeline *models.PipelineInfo) {
	log.With(log.Any("func", "handleUpdatePipelineEvent"),
		log.Any("uuid", pipeline.UUID), log.Any("name", pipeline.Name))

	schedule, err := GetPipelineSchedule(pipeline)
	if err != nil {
		log.Error("get pipeline schedule failed", log.Any("error", err))
		return
	}

	pipeEntry, ok := s.Ctrl.EntryIds[pipeline.UUID]

	if len(schedule) == 0 {
		// delete the cron job if schedule is empty and found the cron job in the map
		if ok {
			log.Info("pipeline cron job exists and schedule is empty, remove it")
			s.Ctrl.RemovePipelineCronJob(pipeline.UUID)
			return
		}
	} else {
		// schedule is not empty and not found the cron job in the map, crate a new cron job
		if !ok {
			if err := s.Ctrl.AddPipelineCronJob(pipeline.UUID, pipeline.Name, schedule, func() {
				log.Info("Create pipeline run with cron job")
				CreatePipelineRun(pipeline)
			}); err != nil {
				log.Error("add pipeline cron job failed", log.Any("error", err))
				return
			}
		} else if ok && schedule != pipeEntry.Schedule {
			// schedule is not empty and found the cron job in the map, compare the schedule, if not equal, update the cron job
			if err := s.Ctrl.UpdatePipelineCronJob(pipeline.UUID, schedule, func() {
				log.Info("Update pipelinerun with cron job")
				CreatePipelineRun(pipeline)
			}); err != nil {
				log.Error("update pipeline cron job failed", log.Any("error", err))
				return
			}
		}
	}
}

// CreatePipelineRun creates a pipeline run event
func CreatePipelineRun(pipelineInfo *models.PipelineInfo) {
	log.Info("Create pipeline run with cron job", log.Any("pipeline name", pipelineInfo.Name))
	_ = eventbus.Publish(context.Background(), &events.RunPipelineEvent{
		Opts: pipeline.NewPipelineContextOption(
			pipeline.WithTxUUID(util.UUID()),
			pipeline.WithTriggerWay(pipeline.Cron),
			pipeline.WithTriggerUser(pipeline.Cron),
			pipeline.WithPipelineUUID(pipelineInfo.UUID),
		),
	})
}

// GetPipelineSchedule gets the pipeline schedule from pipeline content
// need to split the second option in schedule, e.g. '1 2 * * * *' -> '2 * * * *'
func GetPipelineSchedule(pipelineInfo *models.PipelineInfo) (string, error) {
	var schedule string

	pipeDefine, err := pipeline.ParsePipeline(pipelineInfo.Content)
	if err != nil {
		log.Error("parse pipeline content failed", log.Any("error", err))
		return "", err
	}

	if pipeDefine.Triggers.Cron != nil {
		cronSpec := *pipeDefine.Triggers.Cron
		schedule, err = removeScheduleSecond(cronSpec)
		if err != nil {
			log.Error("parse schedule error", log.Any("error", err), log.Any("schedule", cronSpec))
			return "", err
		}
	}
	return schedule, nil
}

// removeScheduleSchedule removes the second option in schedule
func removeScheduleSecond(schedule string) (string, error) {
	scheduleArr := strings.Split(schedule, " ")
	if len(scheduleArr) == 6 {
		return strings.Join(scheduleArr[1:], " "), nil
	} else if len(scheduleArr) == 5 {
		return schedule, nil
	}
	return "", errors.New("invalid schedule format")
}
