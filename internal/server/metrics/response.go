package metrics

type QueueTaskSeries struct {
	Name  string   `json:"name"`  // 对应的 step name
	Value []string `json:"value"` // array of queue task count, index is the same as labels, empty use '-' to fill
}

type QueueTaskResponse struct {
	Steps  []string          `json:"steps"`  // step name（对应series中的name）
	Labels []string          `json:"labels"` // x轴(queue labels)
	Series []QueueTaskSeries `json:"series"` // 总共队列列表（labels）
	Time   string            `json:"time"`   //latest refresh time
}
