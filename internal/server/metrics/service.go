package metrics

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"pipeline/pkg/util"
	"sort"
	"strings"
	"time"

	"git.makeblock.com/makeblock-go/log"
	rc "git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"pipeline/config"
	"pipeline/internal/server/runner"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/queue"
)

type analyticsService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewAnalyticsService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *analyticsService {
	svc := &analyticsService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *analyticsService) Task() (*QueueTaskResponse, error) {
	list, err := runner.NewRunnerRepo(s.db).List(runner.ListRequest{})
	if err != nil {
		return nil, err
	}
	// get labels
	labelsMap := make(map[string]struct{})
	for _, item := range list {
		labels := strings.Split(item.Labels, ",")
		for _, label := range labels {
			labelsMap[label] = struct{}{}
		}
	}

	labelsArray := make([]string, 0, len(labelsMap))
	for label := range labelsMap {
		if label != pipeline.Random {
			labelsArray = append(labelsArray, label)
		}
	}
	sort.Slice(labelsArray, func(i, j int) bool {
		return labelsArray[i] < labelsArray[j]
	})
	// 将 "random" 添加到排序后的数组的开始
	labelsArray = append([]string{pipeline.Random}, labelsArray...)

	ret := QueueTaskResponse{}
	ctx := context.Background()
	cli := rc.GetClient()

	type TaskEntry struct {
		Time   time.Time
		Step   string
		TxUUID string
	}
	// step->label(queue)->count
	globalStepMap := make(map[string]map[string][]TaskEntry)
	// query labels task
	for _, label := range labelsArray {
		ret.Labels = append(ret.Labels, label)
		// queue key
		queueName := fmt.Sprintf("%s%s", config.Items().Redis.Prefix, label)
		// get queue taskIds
		taskIds, err := cli.ZRange(ctx, queueName, 0, -1).Result()

		if len(taskIds) == 0 {
			log.Info("taskIds is empty", log.Any("queue", queueName))
		}

		if errors.Is(err, redis.Nil) {
			log.Error("dequeue ZRange error", log.Field{String: "taskIds is empty"})
			continue
		}

		stepsMap := make(map[string][]TaskEntry)
		// get task
		for _, taskId := range taskIds {

			//log.Info(fmt.Sprintf("enqueue field # taskId: %v", taskId))

			task, err := cli.Get(ctx, taskId).Result()

			if err != nil {
				log.Error("dequeue Get error", log.Field{String: err.Error()})
				continue
			}

			var t queue.StepTask
			err = json.Unmarshal([]byte(task), &t)
			if err != nil {
				log.Error("task unmarshal error", log.Field{String: err.Error()})
			}

			if _, ok := stepsMap[t.Ctx.Step]; !ok {
				stepsMap[t.Ctx.Step] = []TaskEntry{}
			}
			stepsMap[t.Ctx.Step] = append(stepsMap[t.Ctx.Step], TaskEntry{
				Time:   t.Time,
				Step:   t.Ctx.Step,
				TxUUID: t.Ctx.TxUUID,
			})
		}

		// 处理数据
		for step := range stepsMap {
			if step != "" {
				if _, ok := globalStepMap[step]; !ok {
					globalStepMap[step] = make(map[string][]TaskEntry)
				}
				if _, ok := globalStepMap[step][label]; !ok {
					globalStepMap[step][label] = []TaskEntry{}
				}
				globalStepMap[step][label] = append(globalStepMap[step][label], stepsMap[step]...)
			}
		}
	}

	globalStepArr := make([]string, 0)
	for k := range globalStepMap {
		globalStepArr = append(globalStepArr, k)
	}
	sort.Strings(globalStepArr)
	ret.Steps = globalStepArr

	// step->label->count
	ret.Series = make([]QueueTaskSeries, len(globalStepArr))
	for i := 0; i < len(globalStepArr); i++ {
		qts := QueueTaskSeries{
			Name: globalStepArr[i],
		}
		values := make([]string, 0)
		// 获取每个x坐标的值(必须按照labelsArray的顺序)
		if labels, ok := globalStepMap[qts.Name]; ok {
			for _, label := range labelsArray {
				if tasks, ex := labels[label]; ex {
					values = append(values, fmt.Sprintf("%d", len(tasks)))
				} else {
					values = append(values, "-")
				}
			}
		}
		qts.Value = values
		ret.Series[i] = qts
	}

	ret.Time = util.NowDateTime()
	return &ret, nil
}
