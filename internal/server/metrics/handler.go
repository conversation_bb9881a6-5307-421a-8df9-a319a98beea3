package metrics

import (
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
	"pipeline/pkg/common/response"
)

func Task(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAnalyticsService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Task()
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}
