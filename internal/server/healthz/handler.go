package healthz

import (
	"net/http"

	"pipeline/config"

	"github.com/gin-gonic/gin"
)

// Healthz model
type Healthz struct {
	Name    string `json:"name"`
	Env     string `json:"env"`
	Version string `json:"version"`
}

func handleHealthz(c *gin.Context) {
	c.JSON(http.StatusOK, &Healthz{
		Name:    config.Items().AppName,
		Env:     config.Items().ProjectEnv,
		Version: config.Items().APIVersion,
	})
}
