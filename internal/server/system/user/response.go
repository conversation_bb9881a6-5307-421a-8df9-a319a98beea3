package user

type InfoResponse struct {
	UUID     string `json:"uuid"`     //  uuid
	Username string `json:"username"` //  账号
	Name     string `json:"name"`     //  名字
	Email    string `json:"email"`    //  邮箱
	Status   int64  `json:"status"`   //  角色
	Avatar   string `json:"avatar"`   //  头像
	Remark   string `json:"remark"`   //  备注
}

type GetInfoResponse struct {
	UUID     string `json:"uuid"`     //  uuid
	Username string `json:"username"` //  账号
	Password string `json:"password"` //  密码
	Name     string `json:"name"`     //  名字
	Email    string `json:"email"`    //  邮箱
	Status   int64  `json:"status"`   //  角色
	Avatar   string `json:"avatar"`   //  头像
	Remark   string `json:"remark"`   //  备注
	Roles    []any  `json:"roles"`    //角色列表
}
