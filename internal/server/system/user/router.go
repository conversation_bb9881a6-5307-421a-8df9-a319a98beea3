package user

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/system/user")
	{
		//cAPI.Use(auth.Handler())
		cAPI.Use(middleware.AuthUToken())
		//cAPI.GET("/:id", Info)
		//cAPI.GET("/get", Get)
		//cAPI.POST("/page", Page)
		//cAPI.POST("/list", List)
		//cAPI.POST("/add", Add)
		//cAPI.PUT("/update", Update)
		//cAPI.DELETE("/delete", Delete)
		//提供给人工卡点选择用户接口
		cAPI.POST("/select", Select)
		cAPI.POST("/query", Query)
	}
}
