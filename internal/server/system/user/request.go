package user

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
}

type PageRequest struct {
	request.PageRequest
	UserName string   `json:"userName" form:"userName"` //  账号
	Name     string   `json:"name" form:"name"`         //  名字
	Emails   []string `json:"emails" form:"emails"`     //  邮箱
}

type AddRequest struct {
	Username string `json:"username" form:"username"` //  账号
	Password string `json:"password" form:"password"` //  密码
	Name     string `json:"name" form:"name"`         //  名字
	Email    string `json:"email" form:"email"`       //  邮箱
	Status   int64  `json:"status" form:"status"`     //  状态
	Avatar   string `json:"avatar" form:"avatar"`     //  头像
	Remark   string `json:"remark" form:"remark"`     //  备注
}

type UpdateRequest struct {
	ID       int64  `json:"id" form:"id"`
	Password string `json:"password" form:"password"` //  密码
	Name     string `json:"name" form:"name"`         //  名字
	Email    string `json:"email" form:"email"`       //  邮箱
	Status   int64  `json:"status" form:"status"`     //  状态
	Avatar   string `json:"avatar" form:"avatar"`     //  头像
	Remark   string `json:"remark" form:"remark"`     //  备注
}
