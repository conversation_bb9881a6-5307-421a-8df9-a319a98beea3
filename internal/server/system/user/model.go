package user

import (
	"pipeline/pkg/common/model"
	"time"
)

// SysUser  用户表
type SysUser struct {
	model.BaseModel
	Username string `gorm:"column:username" db:"username" json:"username" form:"username"` //  账号
	Password string `gorm:"column:password" db:"password" json:"password" form:"password"` //  密码
	Name     string `gorm:"column:name" db:"name" json:"name" form:"name"`                 //  名字
	Email    string `gorm:"column:email" db:"email" json:"email" form:"email"`             //  邮箱
	Status   int64  `gorm:"column:status" db:"status" json:"status" form:"status"`         //  角色
	Avatar   string `gorm:"column:avatar" db:"avatar" json:"avatar" form:"avatar"`         //  头像
	Remark   string `gorm:"column:remark" db:"remark" json:"remark" form:"remark"`         //  备注
}

func (SysUser) TableName() string {
	return "sys_user"
}

type Users struct {
	ID        int64     `gorm:"column:id" db:"id" json:"id" form:"id"`
	CreatedAt int64     `gorm:"column:created_at" db:"created_at" json:"created_at" form:"created_at"`
	CreatedBy int64     `gorm:"column:created_by" db:"created_by" json:"created_by" form:"created_by"`
	UpdatedAt int64     `gorm:"column:updated_at" db:"updated_at" json:"updated_at" form:"updated_at"`
	UpdatedBy int64     `gorm:"column:updated_by" db:"updated_by" json:"updated_by" form:"updated_by"`
	DeletedAt time.Time `gorm:"column:deleted_at" db:"deleted_at" json:"deleted_at" form:"deleted_at"`
	Name      string    `gorm:"column:name" db:"name" json:"name" form:"name"`     //  ''姓名''
	Type      string    `gorm:"column:type" db:"type" json:"type" form:"type"`     //  ''授权类型''
	Value     string    `gorm:"column:value" db:"value" json:"value" form:"value"` //  ''授权值''
}

func (Users) TableName() string {
	return "users"
}
