package user

import (
	"context"
	"pipeline/config"
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/models"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
)

type userService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewUserService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *userService {
	svc := &userService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *userService) Info(id int64) (*InfoResponse, error) {
	repo := NewUserRepo(s.db)
	m, err := repo.Info(id)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *userService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewUserRepo(s.db)
	var m SysUser
	err = copier.Copy(&m, &r)
	m.Create(ops)
	if err != nil {
		return 0, err
	}
	return repo.Add(&m)
}

func (s *userService) Delete(ids []string, ops string) error {
	repo := NewUserRepo(s.db)
	return repo.Delete(ids)
}

func (s *userService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewUserRepo(s.db)
	m, err := repo.Info(r.ID)
	if err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	return repo.Update(m)
}

func (s *userService) List(r ListRequest) (results []*SysUser, err error) {
	repo := NewUserRepo(s.db)
	return repo.List(r)
}

func (s *userService) Get(username string) (*GetInfoResponse, error) {
	repo := NewUserRepo(s.db)
	m, err := repo.GetByUserName(username)
	if err != nil {
		return nil, err
	}
	var info GetInfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *userService) Select(r PageRequest) ([]*response.SelectResponse, error) {
	accounts, err := s.Page(PageRequest{
		Emails: r.Emails,
		Name:   r.Name,
		PageRequest: request.PageRequest{
			Page:     r.Page,
			PageSize: r.PageSize,
		},
	})
	if err != nil {
		return nil, err
	}
	ret := make([]*response.SelectResponse, 0)
	for _, user := range accounts.List {
		ret = append(ret, &response.SelectResponse{
			Label: user.Email,
			Value: user.Email,
		})
	}
	return ret, nil
}

func (s *userService) Query(r PageRequest) ([]models.User, error) {
	accounts, err := s.Page(PageRequest{
		Emails: r.Emails,
		Name:   r.Name,
		PageRequest: request.PageRequest{
			Page:     r.Page,
			PageSize: r.PageSize,
		},
	})
	if err != nil {
		return nil, err
	}
	return accounts.List, nil
}

func (s *userService) Page(r PageRequest) (*response.PageModelV2[models.User], error) {
	url := config.Items().EfficacyApi.Endpoint + common.UserPath
	res, err := util.HttpPost[response.PageModelV2[models.User]](url, r, map[string]string{
		common.TokenKey: config.Items().EfficacyApi.Token,
	})
	if err != nil {
		return nil, err
	}
	// 适配效能平台用户表的奇怪字段
	for i := 0; i < len(res.List); i++ {
		if res.List[i].UserName == "" {
			res.List[i].UserName = res.List[i].Name
		}
		if res.List[i].Email == "" {
			res.List[i].Email = res.List[i].Value
		}
	}
	return res, nil
}
