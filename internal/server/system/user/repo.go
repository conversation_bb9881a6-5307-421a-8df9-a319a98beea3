package user

import (
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"
)

type userRepo struct {
	db *gorm.DB
}

func NewUserRepo(db *gorm.DB) *userRepo {
	return &userRepo{db: db}
}

func (repo *userRepo) Info(id int64) (*SysUser, error) {
	var result SysUser
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *userRepo) Add(m *SysUser) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *userRepo) Delete(ids []string) error {
	return repo.db.Where("id in (?)", ids).Delete(&SysUser{}).Error
}

func (repo *userRepo) Update(m *SysUser) (rowsAffected int64, err error) {
	db := repo.db.Model(SysUser{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *userRepo) Page(r PageRequest) (pm response.PageModel, err error) {
	query := repo.db.Model(SysUser{})
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	if r.UserName != "" {
		query = query.Where("username like ?", util.Like(r.UserName))
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*InfoResponse
	if err := query.Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *userRepo) List(r ListRequest) (list []*SysUser, err error) {
	if err := repo.db.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *userRepo) GetByUserName(username string) (*SysUser, error) {
	var result SysUser
	if err := repo.db.Where("username = ?", username).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}
