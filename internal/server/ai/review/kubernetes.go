package review

import (
	"context"
	"fmt"
	"pipeline/pkg/llm"
	"pipeline/pkg/models"
	"pipeline/pkg/util"
	"strings"

	"git.makeblock.com/makeblock-go/log"
	"github.com/sashabaranov/go-openai"
)

// Create detailed instructions for the LLM
var (
	systemPrompt = `You are a Kubernetes manifest reviewer that determines if changes are safe to apply.
		Review Rules:
		1. REJECTED if the new manifest has ANY syntax errors or is not valid YAML
		2. REJECTED if the Kubernetes resource structure is invalid (missing required fields, incorrect indentation, etc.)
		3. REJECTED if apiVersion or kind fields are invalid or incompatible with each other
		4. REJECTED if metadata names contain invalid characters or exceed length limits
		5. REJECTED if there are duplicate keys in any maps/dictionaries
		6. REJECTED changes that increase resource limits or requests
		7. REJECTED changes that add privileged access or weaken security
		8. PASSED changes that only modify environment variables, annotations, labels, or comments
		9. PASSED changes to non-critical configuration that don't affect security or stability
		
		Always verify that the YAML syntax is correct before evaluating the content!
		
		Respond with a JSON object containing:
		- "State": either "passed" or "rejected"
		- "Reason": clear explanation for your decision with specific details about the changes or syntax errors
		
		IMPORTANT: Return ONLY the raw JSON object without any Markdown formatting. Do not use code blocks or backticks.
		
		Example response:
		{"State": "passed", "Reason": "YAML syntax is valid and changes only affect environment variables and annotations."}
		{"State": "rejected", "Reason": "The new manifest contains syntax errors: unexpected mapping key at line 15."}
		{"State": "rejected", "Reason": "The new manifest is missing required field 'spec.containers[0].image' for Deployment resource."}
`

	createPrompt = "This is a new Kubernetes manifest. Please verify if it's valid and safe to apply.\n\nNew Manifest:\n```yaml\n%s\n```"

	updatePrompt = "Compare these Kubernetes manifests and determine if the changes are safe according to the review rules.\n\nOld Manifest:\n```yaml\n%s\n```\n\nNew Manifest:\n```yaml\n%s\n```"
)

type ResponseReview struct {
	State  models.ApproveState
	Reason string
}

// KubernetesManifestReviewer is responsible for reviewing Kubernetes manifests.
type KubernetesManifestReviewer struct {
	client *llm.Client
}

// NewKubernetesManifestReviewer creates a new KubernetesManifestReviewer with the given LLM client.
func NewKubernetesManifestReviewer(client *llm.Client) *KubernetesManifestReviewer {
	return &KubernetesManifestReviewer{client: client}
}

// formatLLMResponse attempts to extract a raw JSON string from an LLM response
// that might be wrapped in Markdown code blocks.
func formatLLMResponse(content string) string {
	// Trim leading/trailing whitespace first
	trimmedContent := strings.TrimSpace(content)
	// Check for common Markdown code block patterns (json, or just backticks)
	if strings.HasPrefix(trimmedContent, "```json\n") && strings.HasSuffix(trimmedContent, "\n```") {
		return strings.TrimSpace(trimmedContent[len("```json\n") : len(trimmedContent)-len("\n```")])
	}
	if strings.HasPrefix(trimmedContent, "```") && strings.HasSuffix(trimmedContent, "```") {
		return strings.TrimSpace(trimmedContent[len("```") : len(trimmedContent)-len("```")])
	}
	// If no Markdown block is detected, return the trimmed content as is.
	return trimmedContent
}

// Review reviews the kubernetes manifest to determine if changes are acceptable.
// It checks if changes are limited to environment variables or other safe changes,
// and ensures no modifications to secrets or resource limits.
// Returns a pass state and reason for the decision.
func (r *KubernetesManifestReviewer) Review(ctx context.Context, oldValue, newValue string) (*ResponseReview, error) {
	if newValue == "" {
		return nil, fmt.Errorf("new manifest must not be empty")
	}
	isNewResource := oldValue == newValue

	// make sure the oldValue is not empty
	var userPrompt string
	if isNewResource {
		userPrompt = fmt.Sprintf(createPrompt, newValue)
	} else {
		userPrompt = fmt.Sprintf(updatePrompt, oldValue, newValue)
	}

	// prepare the request
	content, err := r.client.Chat(ctx, []openai.ChatCompletionMessage{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	})
	if err != nil {
		return nil, fmt.Errorf("LLM review failed: %w", err)
	}

	// parse the response
	response := util.JSONStringToStruct[ResponseReview](formatLLMResponse(content))

	log.Info("review kubernetes manifest completed", log.Any("state",
		response.State), log.Any("reason", response.Reason))

	return &response, nil
}
