package diagnosis

import (
	"context"
	"errors"
	"fmt"
	"pipeline/config"
	"pipeline/pkg/notify"
	"pipeline/pkg/notify/types"
	"strconv"
	"strings"
	"time"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"github.com/gin-gonic/gin"

	"pipeline/internal/server/executor"
	"pipeline/pkg/common/response"
	"pipeline/pkg/llm"
	"pipeline/pkg/models"
	"pipeline/pkg/pipeline"
)

// Feedback 发送反馈信息到飞书
func Feedback(c *gin.Context) {
	type FeedbackRequest struct {
		pipeline.StepIndex
		PipelineLoggerUuid string `json:"pipelineLoggerUuid"`
		ActionIdx          int    `json:"actionIdx"`
		Log                string `json:"log"`
		LLMContent         string `json:"llmContent"`
		Feedback           string `json:"feedback"`
		Solved             *bool  `json:"solved"`
	}

	var req FeedbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	req.LLMContent = strconv.Quote(req.LLMContent)
	req.LLMContent = req.LLMContent[1 : len(req.LLMContent)-1]

	req.Feedback = strconv.Quote(req.Feedback)
	req.Feedback = req.Feedback[1 : len(req.Feedback)-1]

	data := types.NotifyData{
		Type:         types.FeishuGroup,
		Receivers:    []string{config.Items().LLMConfig.External.NotifyGroupId},
		TemplateType: types.BuildLogDiagnosisFeedback,
		DataSource: map[string]any{
			"stageIdx":           strconv.Itoa(req.StageIdx),
			"stepRowIdx":         strconv.Itoa(req.StepRowIdx),
			"stepIdx":            strconv.Itoa(req.StepIdx),
			"pipelineLoggerUuid": req.PipelineLoggerUuid,
			"actionIdx":          strconv.Itoa(req.ActionIdx),
			"llmContent":         req.LLMContent,
			"feedback":           req.Feedback,
			"solved":             strconv.FormatBool(*req.Solved),
			"url":                fmt.Sprintf("%s/pipeline/#/cicd/pipeline?execute=%s", config.Items().FrontEndEndpoint, req.PipelineLoggerUuid),
		},
	}
	err := notify.SendNotification(context.Background(), data)
	if err != nil {
		log.Error("send notification error", log.Any("error", err))
		response.Err(c, response.UnknownError(err.Error()))
		return
	}

	response.OK(c, nil)
}

// LogDiagnosisRequest AI日志排查请求request
type LogDiagnosisRequest struct {
	ActionIdx  int    `json:"actionIdx" form:"actionIdx"`
	TxUUID     string `json:"txUUID" form:"txUUID"`
	StageIdx   int    `json:"stageIdx" form:"stageIdx"`
	StepRowIdx int    `json:"stepRowIdx" form:"stepRowIdx"`
	StepIdx    int    `json:"stepIdx" form:"stepIdx"`
	Limit      int64  `json:"limit" form:"limit"`   // 倒数读取的行数限制
	StreamMode bool   `json:"stream" form:"stream"` // 是否使用流式返回
}

// LogDiagnosisResult AI日志排查response
type LogDiagnosisResult struct {
	RawLogs     string `json:"rawLogs"`     // 原始日志内容
	Summary     string `json:"summary"`     // AI分析结果摘要
	ProcessTime int64  `json:"processTime"` // 处理时间(毫秒)
}

// StreamCallback 流式回调函数
type StreamCallback func(eventType string, data string) error

// 获取倒数指定行数的日志内容

func FetchLastLog(c context.Context, pipeAction models.ActionState, txUUID string, limit int64) (string, error) {
	if pipeAction.LogLength <= 0 || len(pipeAction.LogIndexes) == 0 {
		return "", errors.New("log not found")
	}

	length, offset := int64(0), int64(0)
	if limit >= pipeAction.LogLength {
		// 如果请求的行数超过总行数，返回全部日志
		length = pipeAction.LogLength
		offset = pipeAction.LogIndexes[0]
	} else {
		// 否则返回最后Limit行
		length = limit
		offset = pipeAction.LogIndexes[pipeAction.LogLength-limit]
	}

	sb, _, err := executor.ReadLogs(c, pipeAction.LogInStorage, txUUID, pipeAction.LogFilename, offset, length)
	if err != nil {
		return "", err
	}

	return sb.String(), nil
}

// DiagnoseLog AI日志排查，支持流式和非流式模式
func DiagnoseLog(ctx context.Context, req LogDiagnosisRequest, callback StreamCallback) (*LogDiagnosisResult, error) {
	if req.Limit <= 0 || req.Limit > 50 {
		req.Limit = 50
	}

	// 查询action状态
	logFileName := fmt.Sprintf("%s-%d-%d-%d-%d.log",
		req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, req.ActionIdx)
	actionState := models.ActionState{}
	if err := mysql.GetDB().Where("log_filename = ?", logFileName).First(&actionState).Error; err != nil {
		return nil, fmt.Errorf("failed to query action status: %w", err)
	}

	logContent, err := FetchLastLog(ctx, actionState, req.TxUUID, req.Limit)
	if err != nil {
		return nil, fmt.Errorf("failed to obtain logs: %w", err)
	}
	prompt := llm.PrepareAnalysisPrompt(llm.LogSystemPrompt, fmt.Sprintf(llm.LogUserPrompt, logContent))

	client := llm.NewClient(llm.DefaultConfig())

	// 流式模式
	if req.StreamMode && callback != nil {
		// 发送原始日志作为第一个事件
		if err = callback("log", logContent); err != nil {
			return nil, fmt.Errorf("failed to send log event: %w", err)
		}

		var summary strings.Builder
		messageCount := 0

		//
		startTime := time.Now()

		// 流式回调函数
		llmCallback := func(content string, tokenCount int) error {
			// 添加到汇总
			summary.WriteString(content)
			messageCount++

			// 发送增量更新
			if err = callback("token", content); err != nil {
				return err
			}

			// 每隔一段时间发送进度信息
			if messageCount%10 == 0 {
				progressData := fmt.Sprintf(`{"tokens":%d,"elapsed":%.2f}`,
					tokenCount, time.Since(startTime).Seconds())
				if err = callback("progress", progressData); err != nil {
					return err
				}
			}

			return nil
		}

		// 执行流式请求
		err = client.ChatStream(ctx, prompt, llmCallback)
		if err != nil {
			// 发送错误事件
			errMsg := fmt.Sprintf("分析失败: %v", err)
			log.Error("analyze logs with AI failed", log.Any("error", err))
			_ = callback("error", errMsg)
			return nil, fmt.Errorf("流式分析失败: %w", err)
		}

		// 发送完成事件
		processTime := time.Since(startTime).Milliseconds()
		completionData := fmt.Sprintf(`{"summary":%q,"processTime":%d}`,
			summary.String(), processTime)
		_ = callback("complete", completionData)

		// 发送关闭事件
		_ = callback("close", "")

		return &LogDiagnosisResult{
			RawLogs:     logContent,
			Summary:     summary.String(),
			ProcessTime: processTime,
		}, nil
	}

	// 非流式模式
	startTime := time.Now()
	result, err := client.Chat(ctx, prompt)
	processTime := time.Since(startTime).Milliseconds()

	if err != nil {
		log.Error("analyze logs with AI failed", log.Any("error", err))
		return &LogDiagnosisResult{
			RawLogs:     logContent,
			Summary:     "AI分析失败: " + err.Error(),
			ProcessTime: processTime,
		}, nil
	}

	return &LogDiagnosisResult{
		RawLogs:     logContent,
		Summary:     result,
		ProcessTime: processTime,
	}, nil
}

// LogsDiagnosis 获取日志并通过大模型分析，支持SSE实时流式返回
func LogsDiagnosis(c *gin.Context) {
	var req LogDiagnosisRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	log.Info("LogsDiagnosis request:", log.Any("req", req))

	// 流式模式
	if req.StreamMode {
		c.Header("Content-Type", "text/event-stream")
		c.Header("Cache-Control", "no-cache")
		c.Header("Connection", "keep-alive")
		// 禁用Nginx的缓冲
		c.Header("X-Accel-Buffering", "no")

		// 启用流刷新
		c.Writer.Flush()

		// 定义流回调
		streamCallback := func(eventType string, data string) error {
			sendSSEEvent(c, eventType, data)
			return nil
		}

		_, err := DiagnoseLog(c.Request.Context(), req, streamCallback)
		if err != nil {
			// 流式模式下错误已经通过callback发送
			log.Error("logs diagnosis failed", log.Any("error", err))
		}
		return
	}

	// 非流式模式
	result, err := DiagnoseLog(c.Request.Context(), req, nil)
	if err != nil {
		log.Error("logs diagnosis failed", log.Any("error", err))
		response.Err(c, response.UnknownError(err.Error()))
		return
	}

	response.OK(c, result)
}

func sendSSEEvent(c *gin.Context, event string, data string) {
	// 如果数据为空，使用空对象防止客户端解析错误
	if data == "" && event != "close" {
		data = "{}"
	}

	if event != "" {
		fmt.Fprintf(c.Writer, "event: %s\n", event)
	}

	// 发送数据，如果数据中有换行，需要每行前加 "data: "
	if strings.Contains(data, "\n") {
		lines := strings.Split(data, "\n")
		for _, line := range lines {
			fmt.Fprintf(c.Writer, "data: %s\n", line)
		}
	} else {
		fmt.Fprintf(c.Writer, "data: %s\n", data)
	}

	fmt.Fprint(c.Writer, "\n")
	c.Writer.Flush()
}
