package artifact

import (
	"context"
	"io"

	"pipeline/config"
	"pipeline/pkg/oss"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type assetsService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewAssetsService(ts *trace.Service,
	dbEngine *gorm.DB, redisClient *redis.Client) *assetsService {
	svc := &assetsService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *assetsService) Info(path string) (io.ReadCloser, error) {
	bucket := config.Items().OSS.LoggerBucketName
	reader, err := oss.GetService().Get(context.Background(), bucket, path)
	if err != nil {
		return nil, err
	}
	return reader, nil
}
