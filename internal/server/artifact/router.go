package artifact

import (
	"pipeline/pkg/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {

	// open download api
	router.GET("/packages/download/:app/:name/:version",
		middleware.RateLimit(), OpenApiDownload)

	// normal packages api
	cAPI := router.Group("/api/v1/packages")
	{
		cAPI.Use(middleware.AuthIsAdmin(), middleware.AuthUToken())
		cAPI.POST("/info", Info)
		cAPI.POST("/page", Page)
		cAPI.POST("/list", List)
		cAPI.POST("/add", Add)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
		// 版本相关
		cAPI.GET("/download/:uuid", Download)         // 下载
		cAPI.POST("/version", Version)                // 版本信息
		cAPI.POST("/versions", Versions)              // 版本列表
		cAPI.POST("/version/page", VersionPage)       // 版本分页
		cAPI.DELETE("/version/delete", VersionDelete) // 删除版本
	}

	// 针对assets的api, 如测试报告等
	assetsRouter := router.Group("/assets")
	{
		//cAPI.Use(auth.Handler())
		assetsRouter.GET("/*path", Assets)
	}
}
