package artifact

import (
	"context"
	"fmt"
	"io"
	"pipeline/internal/server/app"

	"github.com/avast/retry-go"

	"pipeline/config"
	"pipeline/internal/runner/step/factory"
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/oss"
	"pipeline/pkg/queue"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	mbredis "git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

type artifactService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func DefaultArtifactService() *artifactService {
	svc := &artifactService{
		Service: trace.IfNil(nil),
	}
	dbEngine := mysql.GetDB()
	redisClient := mbredis.GetClient()
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func NewArtifactService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *artifactService {
	svc := &artifactService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *artifactService) Info(uuid string) (*InfoResponse, error) {
	repo := NewArtifactRepo(s.db)
	m, err := repo.GetByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *artifactService) Add(r AddRequest, ops string) (err error) {
	repo := NewArtifactRepo(s.db)
	var m models.Artifact
	err = copier.Copy(&m, &r)
	if err != nil {
		return err
	}
	m.Create(ops)
	// 保存
	if _, err = repo.Add(&m); err != nil {
		return err
	}
	return nil
}

func (s *artifactService) Delete(ids []string, ops string) error {
	repo := NewArtifactRepo(s.db)
	return repo.SoftDelete(ids, ops)
}

func (s *artifactService) VersionDelete(uuidList []string, isAdmin bool, ops string) error {
	// get artifact list
	repo := NewArtifactRepo(s.db)
	list, err := repo.GetByUUIDList(uuidList)
	if err != nil {
		return err
	}
	// auth, ops must be the maintainer of the app or admin
	if !isAdmin {
		if err = repo.IsAppMaintainerByArtifactVersions(uuidList, ops); err != nil {
			return err
		}
	}
	// async delete oss artifact
	go func() {
		for _, v := range list {
			if err = retry.Do(func() error {
				return oss.GetService().Delete(context.Background(),
					config.Items().OSS.ArtifactsBucketName, v.Path)
			}, retry.Attempts(3)); err != nil {
				log.Error("delete oss artifact failed",
					log.Any("artifact", v), log.Any("error", err))
			}
		}
	}()
	// reduce the artifact version count
	countMap := make(map[string]uint64)
	for _, v := range list {
		countMap[v.ArtifactUuid] += 1
	}
	for k, v := range countMap {
		if err = repo.ReduceVersionCount(k, v); err != nil {
			log.Error("reduce artifact version count failed",
				log.Any("artifact", k), log.Any("error", err))
		}
	}
	return repo.SoftDeleteVersion(uuidList, ops)
}

func (s *artifactService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewArtifactRepo(s.db)
	m, err := repo.GetVersionByUUID(r.UUID)
	if err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	rowsAffected, err = repo.UpdateVersion(m)
	if err != nil {
		return 0, err
	}
	// 更新制品
	if r.MaxVersions > 0 {
		artifact, artErr := repo.GetByUUID(m.ArtifactUuid)
		if artErr != nil {
			return 0, artErr
		}
		artifact.MaxVersions = r.MaxVersions
		artifact.Update(ops)
		return repo.Update(artifact)
	}
	// 更新制品版本
	return rowsAffected, err
}

func (s *artifactService) Page(r PageRequest, uuid string, isAdmin bool) (response.PageModel, error) {
	repo := NewArtifactRepo(s.db)
	pm, err := repo.Page(r, uuid, isAdmin)
	return pm, err
}

func (s *artifactService) List(r ListRequest, uuid string, admin bool) (results []*InfoResponse, err error) {
	repo := NewArtifactRepo(s.db)
	return repo.List(r, uuid, admin)
}

func (s *artifactService) Versions(req VersionsRequest) ([]*VersionsResponse, error) {
	repo := NewArtifactRepo(s.db)
	versions, err := repo.Versions(req)
	if err != nil {
		return nil, err
	}
	if req.Value == "label" {
		for i, version := range versions {
			versions[i].Value = version.Label
		}
	}
	return versions, nil
}

func (s *artifactService) VersionPage(req VersionsPageRequest, isAdmin bool) (response.PageModel, error) {
	repo := NewArtifactRepo(s.db)
	return repo.VersionPage(req)
}

// ArtifactsLatestVersion get the latest version of the artifact
func (s *artifactService) ArtifactsLatestVersion(task *queue.StepTask) {
	Map := make(map[string]string)
	repo := NewArtifactRepo(s.db)
	// 解析出所有需要下载的制品
	config := factory.GetStepConfig(task.Ctx.StepDefine)
	if config == nil {
		log.Info("config is nil, skip get artifact latest version")
		return
	}
	artifacts := config.GetDownloadArtifacts()
	for _, artifact := range artifacts {
		if artifact.Version == "latest" {
			latestVersion := repo.GetLatestVersion(task.Ctx.App.UUID, artifact.Name)
			if latestVersion == "" {
				log.Error("get latest version failed", log.Any("artifact", artifact))
				continue
			}
			Map[artifact.Name] = latestVersion
		}
	}
	task.Ctx.ArtifactLatestVersion = Map
}

func (s *artifactService) Version(uuid string) (*ArtifactVersionResponse, error) {
	repo := NewArtifactRepo(s.db)
	m, err := repo.GetVersionByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info ArtifactVersionResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

// downloadArtifact 通用的制品下载逻辑
func (s *artifactService) downloadArtifact(artifactName string, versionInfo *models.ArtifactVersion, writer gin.ResponseWriter) error {
	// if version is deleted, return error
	if versionInfo.Deleted {
		return response.DBOperationError("artifact version is deleted")
	}

	// 从oss获取文件流
	bucket := config.Items().OSS.ArtifactsBucketName
	ctx := context.Background()
	reader, err := oss.GetService().Get(ctx, bucket, versionInfo.Path)
	if err != nil {
		return err
	}
	// 确保 reader 正常关闭
	defer func(reader io.ReadCloser) {
		if err = reader.Close(); err != nil {
			log.ErrorE("close reader failed", err)
		}
	}(reader)

	// 获取文件信息
	file, err := oss.GetService().Info(ctx, bucket, versionInfo.Path)
	if err != nil {
		return err
	}

	// 设置响应头
	writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s-%s%s",
		artifactName, versionInfo.Version, common.ZipCompressionExtension))
	writer.Header().Set("Content-Type", "application/octet-stream")
	writer.Header().Set("Content-Length", fmt.Sprintf("%d", file.Size))

	// 写入响应
	_, err = io.Copy(writer, reader)
	if err != nil {
		log.ErrorE("download artifact failed", err)
		return err
	}

	// 更新下载次数
	repo := NewArtifactRepo(s.db)
	return repo.UpdateDownloadCount(versionInfo.UUID)
}

func (s *artifactService) Download(uuid string, writer gin.ResponseWriter) error {
	// 从数据库中获取制品信息
	info, err := NewArtifactRepo(s.db).GetVersionByUUID(uuid)
	if err != nil {
		return err
	}

	// 反查制品名称
	artifact, err := s.Info(info.ArtifactUuid)
	if err != nil {
		return err
	}

	return s.downloadArtifact(artifact.Name, info, writer)
}

func (s *artifactService) OpenApiDownload(appIdentity, artifactName,
	artifactVersion string, writer gin.ResponseWriter) error {
	// get app info
	appInfo, err := app.NewAppRepo(s.db).GetByIdentity(appIdentity)
	if err != nil {
		return err
	}

	// 从数据库中获取制品信息
	repo := NewArtifactRepo(s.db)
	// get artifact info
	artifact, err := repo.GetArtifactByName(appInfo.UUID, artifactName)
	if err != nil {
		return err
	}

	// get version info
	info, err := repo.GetVersionByArtifactUUIDAndVersionName(artifact.UUID, artifactVersion)
	if err != nil {
		return err
	}

	return s.downloadArtifact(artifact.Name, info, writer)
}
