package artifact

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
	Name string `json:"name"` //  应用名称
}

type PageRequest struct {
	request.PageRequest
	Name    string `json:"name"`    //  应用名称
	AppUUID string `json:"appUuid"` //  标识
}

type AddRequest struct {
	Name           string `json:"name"`           //  应用名称
	Identity       string `json:"identity"`       //  标识
	NamespaceId    int    `json:"namespaceId"`    //  所属项目
	ProjectUuid    string `json:"projectUuid"`    //  所属项目
	Source         string `json:"source"`         //  代码源
	UseAppTemplate bool   `json:"useAppTemplate"` //  是否使用模板
	Template       string `json:"template"`       //  模板

	Category    string `json:"category"`    //  应用分类
	Level       string `json:"level"`       //  应用等级
	Description string `json:"description"` //  描述
	//渲染参数
	Params map[string]any `json:"param"` //
	//是否下载
	Download bool `json:"download"` //
	//
	CreatePipeline bool `json:"createPipeline"` //
	//关联用户
	Principals []string //负责人uuid
	Members    []string //参与人uuid
}

type UpdateRequest struct {
	// 针对制品版本的更新
	UUID        string `json:"uuid" form:"uuid" binding:"required"` //  版本uuid
	Labels      string `json:"labels"`                              //  所属项目
	Description string `json:"description"`                         //  描述
	// 针对制品的更新
	MaxVersions uint64 `json:"maxVersions"` //  最大保留版本数
}

type ImportRequest struct {
	Name        string `json:"name"`        //  应用名称
	Identity    string `json:"identity"`    //  标识
	NamespaceId int    `json:"namespaceId"` //  所属项目
	ProjectUuid string `json:"projectUuid"` //  所属项目
	Source      string `json:"source"`      //  代码源
	RepoId      int    `json:"repoId"`
	Description string `json:"description"` //  描述
	Category    string `json:"category"`    //  应用分类
	Level       string `json:"level"`       //  应用等级
	//关联用户
	Principals []string //负责人uuid
	Members    []string //参与人uuid
}

type VersionsRequest struct {
	UUID  string `form:"uuid" binding:"required"` // 制品uuid
	Name  string `form:"name"`
	Value string `form:"value"`
	Size  int    `form:"size"`
}

type VersionsPageRequest struct {
	request.PageRequest
	UUID string `form:"uuid" binding:"required"` // 制品uuid
	Name string `form:"name"`
}
