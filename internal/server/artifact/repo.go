package artifact

import (
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"gorm.io/gorm"
	"math"
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/util"
)

type artifactRepo struct {
	db *gorm.DB
}

func NewArtifactRepo(db *gorm.DB) *artifactRepo {
	return &artifactRepo{db: db}
}

func (repo *artifactRepo) Info(id int64) (*models.Artifact, error) {
	var result models.Artifact
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *artifactRepo) Add(m *models.Artifact) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *artifactRepo) SoftDelete(ids []string, osp string) error {
	//因为identity是唯一的，所以软删除时，需要将deleted字段置为true，将identity置为identity-uuid防止重复
	return repo.db.Model(&models.Artifact{}).
		Where("uuid in (?)", ids).
		Updates(map[string]any{
			"name":     gorm.Expr("concat(name,concat('-',uuid))"),
			"deleted":  true,
			"modifier": osp,
		}).Error
}

func (repo *artifactRepo) SoftDeleteVersion(ids []string, osp string) error {
	return repo.db.Model(&models.ArtifactVersion{}).
		Where("uuid in (?)", ids).
		Updates(map[string]any{
			"deleted":  true,
			"modifier": osp,
		}).Error
}

func (repo *artifactRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.Artifact{}).Error
}

func (repo *artifactRepo) Update(m *models.Artifact) (rowsAffected int64, err error) {
	db := repo.db.Model(models.Artifact{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *artifactRepo) UpdateVersion(m *models.ArtifactVersion) (rowsAffected int64, err error) {
	db := repo.db.Model(models.ArtifactVersion{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *artifactRepo) Page(r PageRequest, uuid string, isAdmin bool) (pm response.PageModel, err error) {
	var query = repo.db.Model(models.Artifact{})
	//名称模糊查询
	if r.Name != "" {
		query = query.Where("name like ?", util.Like(r.Name))
	}
	//删除标记
	query = query.Where("deleted = false and app_uuid = ?", r.AppUUID)
	//查询记录数
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*InfoResponse
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *artifactRepo) List(r ListRequest, uuid string, isAdmin bool) (list []*InfoResponse, err error) {
	var query = repo.db.Model(models.Artifact{})
	//名称模糊查询
	if r.Name != "" {
		query = query.Where("name like ?", util.Like(r.Name))
	}
	//删除标记
	query = query.Where("deleted = false")

	if err := query.Order("gmt_create desc,name asc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *artifactRepo) GetByUUID(uuid string) (*models.Artifact, error) {
	var result models.Artifact
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *artifactRepo) GetByUUIDList(uuid []string) ([]*models.ArtifactVersion, error) {
	var result []*models.ArtifactVersion
	if err := repo.db.Where("uuid in (?)", uuid).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (repo *artifactRepo) GetVersionByUUID(uuid string) (*models.ArtifactVersion, error) {
	var result models.ArtifactVersion
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

// GetLatestVersion get the latest version of the artifact
func (repo *artifactRepo) GetLatestVersion(uuid string, name string) string {
	var result string
	err := repo.db.Transaction(func(tx *gorm.DB) error {
		var artifactUUID string
		if err := tx.Model(models.Artifact{}).Select("uuid").
			Where("app_uuid = ? and name = ?", uuid, name).
			Scan(&artifactUUID).Error; err != nil {
			return err
		}
		return tx.Model(models.ArtifactVersion{}).Select("version").
			Where("artifact_uuid = ?", artifactUUID).
			Order("gmt_create desc").
			Limit(1).
			Scan(&result).Error
	})
	if err != nil {
		log.Error("get artifact latest version failed", log.Any("error", err))
		return ""
	}
	return result
}

func (repo *artifactRepo) Versions(req VersionsRequest) ([]*VersionsResponse, error) {
	var result []*VersionsResponse
	query := repo.db.Model(models.ArtifactVersion{}).
		Select("version label, uuid value").
		Where("artifact_uuid = ? and deleted = false", req.UUID)
	if req.Name != "" {
		query = query.Where("version like ? or labels like ?", util.Like(req.Name), util.Like(req.Name))
	}
	// max size 100
	size := int(math.Min(float64(req.Size), 100))
	// 查询记录数
	if err := query.Order("gmt_create desc").Limit(size).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (repo *artifactRepo) UpdateDownloadCount(uuid string) error {
	return repo.db.Model(models.ArtifactVersion{}).
		Where("uuid = ?", uuid).
		Update("downloads", gorm.Expr("downloads + 1")).Error
}

func (repo *artifactRepo) GetArtifactByName(app, name string) (*models.Artifact, error) {
	var result models.Artifact
	if err := repo.db.Where("app_uuid = ? and name = ?", app, name).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *artifactRepo) GetVersionByArtifactUUIDAndVersionName(artifactUUID,
	artifactVersion string) (*models.ArtifactVersion, error) {
	var result models.ArtifactVersion
	if err := repo.db.Where("artifact_uuid = ? and version = ?",
		artifactUUID, artifactVersion).Order("deleted, gmt_create desc").
		First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *artifactRepo) VersionPage(r VersionsPageRequest) (pm response.PageModel, err error) {
	var query = repo.db.Model(models.ArtifactVersion{})
	//名称模糊查询
	if r.Name != "" {
		like := util.Like(r.Name)
		query = query.Where("version like ? or labels like ?", like, like)
	}
	//删除标记
	query = query.Where("deleted = false and artifact_uuid = ?", r.UUID)
	//查询记录数
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*ArtifactVersionResponse
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *artifactRepo) ReduceVersionCount(uuid string, reduce uint64) error {
	var artifact models.Artifact
	if err := repo.db.Select("versions").
		Where("uuid = ?", uuid).First(&artifact).Error; err != nil {
		return err
	}
	if artifact.Versions >= reduce {
		return repo.db.Model(models.Artifact{}).
			Where("uuid = ?", uuid).
			Update("versions", artifact.Versions-reduce).Error
	} else {
		return fmt.Errorf("artifact version count is not enough")
	}
}

func (repo *artifactRepo) IsAppMaintainerByArtifactVersions(uuids []string, user string) error {
	// get artifact uuid list
	var artifactUUIDList []string
	if err := repo.db.Model(models.ArtifactVersion{}).
		Select("artifact_uuid").
		Where("uuid in (?)", uuids).
		Find(&artifactUUIDList).Error; err != nil {
		return err
	}
	if len(artifactUUIDList) == 0 {
		return nil
	}
	// get app uuid by artifact uuid
	var appUUIDList []string
	if err := repo.db.Model(models.Artifact{}).
		Select("app_uuid").
		Where("uuid in (?)", artifactUUIDList).
		Find(&appUUIDList).Error; err != nil {
		return err
	}
	if len(appUUIDList) == 0 {
		return nil
	}
	// check app uuid bing user, count must be equal
	var count int64
	if err := repo.db.Model(models.ProjectAppOfUser{}).
		Where("app_uuid in (?) and user_uuid = ? and type_of = ?",
			appUUIDList, user, common.Principal).
		Count(&count).Error; err != nil {
		return err
	}
	if int(count) != len(appUUIDList) {
		return fmt.Errorf("user %s is not the maintainer of the app", user)
	}
	return nil
}
