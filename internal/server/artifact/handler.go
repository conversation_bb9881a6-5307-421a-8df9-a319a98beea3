package artifact

import (
	"io"
	"net/http"
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/middleware"
	"strings"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

// Info 获取制品信息
// @Summary 获取制品信息
// @Description 根据UUID获取制品详细信息
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body request.InfoRequest true "请求体参数，包含制品UUID"
// @Success 200 {object} response.APIModel{data=artifact.InfoResponse} "成功返回制品信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/info [post]
func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Add 新增制品
// @Summary 新增制品
// @Description 新增一个制品
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body artifact.AddRequest true "请求体参数，包含制品信息"
// @Success 200 {object} response.APIModel "新增成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/add [post]
func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.Add(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}

	response.OK(c, nil)
}

// Update 更新制品
// @Summary 更新制品
// @Description 更新制品信息或版本信息
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body artifact.UpdateRequest true "请求体参数，包含更新信息"
// @Success 200 {object} response.APIModel "更新成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/update [put]
func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Update(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Page 分页查询制品
// @Summary 分页查询制品
// @Description 分页获取制品列表，支持多条件筛选
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body artifact.PageRequest true "请求体参数，包含分页及筛选条件"
// @Success 200 {object} response.APIModel "成功返回分页制品列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/page [post]
func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req, middleware.GetUserEmail(c), middleware.IsAdmin(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

// @Summary 获取制品列表
// @Description 获取所有制品列表（不分页）
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body artifact.ListRequest true "请求体参数，包含筛选条件"
// @Success 200 {object} response.APIModel "成功返回制品列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/list [post]
func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req, middleware.GetUserEmail(c), middleware.IsAdmin(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, response.PageModel{
		List: list,
	})
}

// @Summary 删除制品
// @Description 根据UUID批量删除制品
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body request.DeleteRequest true "请求体参数，包含待删除制品UUID列表"
// @Success 200 {object} response.APIModel "删除成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/delete [delete]
func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// Download 下载制品
// @Summary 下载制品
// @Description 根据UUID下载制品文件
// @Tags 制品管理
// @Accept json
// @Produce application/octet-stream
// @Param uuid path string true "制品UUID"
// @Success 200 {file} file "下载文件流"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/download/{uuid} [get]
func Download(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	// 从请求中获取uuid
	uuid := c.Param("uuid")
	// 下载制品
	err := svc.Download(uuid, c.Writer)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
}

// @Summary 获取制品版本列表
// @Description 获取指定制品的所有版本
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body artifact.VersionsRequest true "请求体参数，包含制品UUID等"
// @Success 200 {object} response.APIModel "成功返回版本列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/versions [post]
func Versions(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req VersionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Versions(req)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// VersionPage 分页获取制品版本
// @Summary 分页获取制品版本
// @Description 分页获取指定制品的版本信息
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body artifact.VersionsPageRequest true "请求体参数，包含分页及筛选条件"
// @Success 200 {object} response.APIModel "成功返回版本分页信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/version/page [post]
func VersionPage(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req VersionsPageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.VersionPage(req, middleware.IsAdmin(c))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Version 获取制品版本详情
// @Summary 获取制品版本详情
// @Description 获取指定UUID的制品版本详细信息
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body request.InfoRequest true "请求体参数，包含制品版本UUID"
// @Success 200 {object} response.APIModel "成功返回版本详情"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/version [post]
func Version(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Version(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// OpenApiDownload 通过应用标识、制品名、版本号下载制品
// @Summary OpenAPI下载制品
// @Description 通过应用标识、制品名、版本号下载制品
// @Tags 制品管理
// @Accept json
// @Produce application/octet-stream
// @Param app path string true "应用标识"
// @Param name path string true "制品名称"
// @Param version path string true "版本号"
// @Success 200 {file} file "下载文件流"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /packages/download/{app}/{name}/{version} [get]
func OpenApiDownload(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	// 从请求中获取uuid
	app := c.Param("app")
	name := c.Param("name")
	version := c.Param("version")
	// 下载制品
	err := svc.OpenApiDownload(app, name, version, c.Writer)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
}

// VersionDelete 删除制品版本
// @Summary 删除制品版本
// @Description 根据UUID批量删除制品版本
// @Tags 制品管理
// @Accept json
// @Produce json
// @Param data body request.DeleteRequest true "请求体参数，包含待删除版本UUID列表"
// @Success 200 {object} response.APIModel "删除成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/packages/version/delete [delete]
func VersionDelete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewArtifactService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.VersionDelete(req.UUIDs, middleware.IsAdmin(c), c.GetString(common.UserEmail))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// Assets 获取制品相关静态资源
// @Summary 获取静态资源
// @Description 获取制品相关静态资源（如测试报告等）
// @Tags 制品管理
// @Accept json
// @Produce text/html
// @Param path path string true "资源路径"
// @Success 200 {file} file "资源文件流"
// @Failure 404 {object} response.APIModel "资源不存在"
// @Router /assets/{path} [get]
func Assets(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	// 获取请求路径参数
	// "/assets/de74e6af96cd4ec5b2d62c62a185a2cf/artifact/0-0-0-report.html"
	path := c.Param("path")
	svc := NewAssetsService(ts, mysql.GetDB(), redis.GetClient())
	// 获取文件内容
	reader, err := svc.Info(path)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	defer reader.Close() // 确保 reader 正常关闭
	if strings.HasSuffix(path, ".html") {
		c.Header("Content-Type", "text/html; charset=utf-8")
		_, _ = io.Copy(c.Writer, reader)
		return
	}
	// 如何适配css、js、json等文件类型
	// 通过文件后缀名判断文件类型
	if strings.HasSuffix(path, ".css") {
		c.Header("Content-Type", "text/css; charset=utf-8")
		_, _ = io.Copy(c.Writer, reader)
		return
	}
	if strings.HasSuffix(path, ".js") {
		c.Header("Content-Type", "application/javascript; charset=utf-8")
		_, _ = io.Copy(c.Writer, reader)
		return
	}

	// default
	c.Status(http.StatusOK)
	_, _ = io.Copy(c.Writer, reader)
}
