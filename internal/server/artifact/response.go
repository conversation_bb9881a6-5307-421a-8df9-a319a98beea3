package artifact

import (
	"pipeline/pkg/common/model"
)

type InfoResponse struct {
	UUID          string      `json:"uuid"`          //  uuid
	Creator       string      `json:"creator"`       //  创建人
	Modifier      string      `json:"modifier"`      //  备注
	Remark        string      `json:"remark"`        //  修改人
	GmtCreate     *model.Time `json:"gmtCreate"`     //  创建时间
	GmtModified   *model.Time `json:"gmtModified"`   //  修改时间
	AppUuid       string      `json:"appUuid"`       //  所属应用
	Name          string      `json:"name"`          // 应用级别唯一标识                      //  应用名称
	Versions      uint64      `json:"versions"`      //  版本数
	LatestVersion string      `json:"latestVersion"` //  最新版本
	Description   string      `json:"description"`   //  描述
	MaxVersions   uint64      `json:"maxVersions"`   //  最大保留版本数
}

type VersionsResponse struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type ArtifactVersionResponse struct {
	UUID         string      `json:"uuid"`                                                   //  uuid
	Creator      string      `json:"creator"`                                                //  创建人
	Modifier     string      `json:"modifier"`                                               //  备注
	Remark       string      `json:"remark"`                                                 //  修改人
	GmtCreate    *model.Time `json:"gmtCreate"`                                              //  创建时间
	GmtModified  *model.Time `json:"gmtModified"`                                            //  修改时间
	SHA1         string      `gorm:"column:sha1" db:"sha1" json:"sha1"`                      //  sha1
	Path         string      `gorm:"column:path" db:"path" json:"path"`                      //  下载路径
	Version      string      `gorm:"column:version" db:"version" json:"version"`             //  版本
	Pusher       string      `gorm:"column:pusher" db:"pusher" json:"pusher"`                // 推送人
	Labels       string      `gorm:"column:labels" db:"labels" json:"labels"`                //  标签
	Description  string      `gorm:"column:description" db:"description" json:"description"` //  描述
	Downloads    int64       `gorm:"column:downloads" db:"downloads" json:"downloads"`       //  下载次数
	Size         string      `gorm:"column:size" db:"size" json:"size"`                      //  文件大小
	ArtifactUuid string      `json:"artifactUuid"`                                           //  所属制品
	TxUUID       string      `json:"txUUID"`                                                 //  流水线执行记录uuid
	PipelineUUID string      `json:"pipelineUUID"`                                           //  流水线uuid
}
