package group

import (
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"
)

type groupRepo struct {
	db *gorm.DB
}

func NewGroupRepo(db *gorm.DB) *groupRepo {
	return &groupRepo{db: db}
}

func (repo *groupRepo) Info(uuid string) (*PipelineTemplateGroup, error) {
	var result PipelineTemplateGroup
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *groupRepo) Add(m *PipelineTemplateGroup) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *groupRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&PipelineTemplateGroup{}).Error
}

func (repo *groupRepo) Update(m *PipelineTemplateGroup) (rowsAffected int64, err error) {
	db := repo.db.Model(PipelineTemplateGroup{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *groupRepo) Page(r PageRequest) (pm response.PageModel, err error) {
	query := repo.db.Model(PipelineTemplateGroup{})
	if r.Name != "" {
		query = query.Where("name like ?", util.Like(r.Name))
	}
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*PipelineTemplateGroup
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *groupRepo) List(r ListRequest) (list []*PipelineTemplateGroup, err error) {
	query := repo.db.Model(PipelineTemplateGroup{})
	if r.Enable != nil {
		query = query.Where("enable = ?", *r.Enable)
	}
	if err = query.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}
