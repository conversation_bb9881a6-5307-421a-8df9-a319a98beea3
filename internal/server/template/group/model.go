package group

import "pipeline/pkg/common/model"

// PipelineTemplateGroup  pipeline_step_group
type PipelineTemplateGroup struct {
	model.BaseModel
	Name     string `gorm:"column:name" db:"name" json:"name"`             //  分组名称
	Identity string `gorm:"column:identity" db:"identity" json:"identity"` //  分组标识
	Order    int32  `gorm:"column:order" db:"order" json:"order"`          //  排序
	Enable   *bool  `gorm:"column:enable" db:"enable" json:"enable"`       //  是否启用
}

func (PipelineTemplateGroup) TableName() string {
	return "pipeline_template_group"
}
