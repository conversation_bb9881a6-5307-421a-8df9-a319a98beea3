package group

import (
	"time"

	"pipeline/internal/server/template/template"
)

type InfoResponse struct {
	Id          int64     `json:"id"`          //  主键
	Uuid        string    `json:"uuid"`        //  UUID
	Name        string    `json:"name"`        //  分组名称
	Identity    string    `json:"identity"`    //  分组标识
	Order       int32     `json:"order"`       //  排序
	Enable      bool      `json:"enable"`      //  是否启用
	Creator     string    `json:"creator"`     //  创建人
	Modifier    string    `json:"modifier"`    //  修改人
	Remark      string    `json:"remark"`      //  修改人
	GmtCreate   time.Time `json:"gmtCreate"`   //  创建时间
	GmtModified time.Time `json:"gmtModified"` //  修改时间
	Deleted     string    `json:"deleted"`     //  逻辑删除
}

type TemplatesResponse struct {
	PipelineTemplateGroup
	List []template.PipelineTemplate `json:"list"`
}
