package template

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/pipeline/template")
	{
		cAPI.Use(middleware.AuthUToken())
		cAPI.POST("/info", Info)
		cAPI.POST("/page", Page)
		cAPI.POST("/list", List)
		cAPI.POST("/add", Add)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
	}
}
