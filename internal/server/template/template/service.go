package template

import (
	"context"

	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
)

type templateService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewTemplateService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *templateService {
	svc := &templateService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *templateService) Info(uuid string) (*InfoResponse, error) {
	repo := NewTemplateRepo(s.db)
	m, err := repo.Info(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

func (s *templateService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewTemplateRepo(s.db)
	var m PipelineTemplate
	err = copier.Copy(&m, &r)
	m.Create(ops)
	if err != nil {
		return 0, err
	}
	return repo.Add(&m)
}

func (s *templateService) Delete(ids []string, ops string) error {
	repo := NewTemplateRepo(s.db)
	return repo.Delete(ids)
}

func (s *templateService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewTemplateRepo(s.db)
	m, err := repo.Info(r.UUID)
	if err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	return repo.Update(m)
}

func (s *templateService) Page(r PageRequest) (response.PageModel, error) {
	repo := NewTemplateRepo(s.db)
	return repo.Page(r)
}

func (s *templateService) List(r ListRequest) (results []*PipelineTemplate, err error) {
	repo := NewTemplateRepo(s.db)
	return repo.List(r)
}
