package template

import "time"

type InfoResponse struct {
	ID          int64     `json:"id"`           //  主键
	UUID        string    `json:"uuid"`         //  UUID
	Name        string    `json:"name"`         //  流水线名称
	Group       string    `json:"group"`        //  流水线名称
	Content     string    `json:"content"`      //  配置正文
	Description string    `json:"description"`  //  描述
	Order       int32     `json:"order"`        //  排序
	Online      bool      `json:"online"`       //  是否上线
	Enable      bool      `json:"enable"`       //  是否启用
	Creator     string    `json:"creator"`      //  创建人
	Modifier    string    `json:"modifier"`     //  修改人
	GmtCreate   time.Time `json:"gmt_create"`   //  创建时间
	GmtModified time.Time `json:"gmt_modified"` //  修改时间
	Deleted     string    `json:"deleted"`      //  逻辑删除
	Icon        string    `json:"icon"`         //  图标
	Param       string    `json:"param"`        //  图标
}
