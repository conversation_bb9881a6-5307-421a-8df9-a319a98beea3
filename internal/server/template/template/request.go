package template

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
	Enable *bool `json:"enable"` //  是否启用
	Online *bool `json:"online"` //  是否启用
}

type PageRequest struct {
	request.PageRequest
	Name        string `json:"name"`        //  流水线名称
	Group       string `json:"group"`       //  配置正文
	Content     string `json:"content"`     //  配置正文
	Description string `json:"description"` //  描述
	Order       int32  `json:"order"`       //  排序
	Enable      *bool  `json:"enable"`      //  是否启用
	Online      *bool  `json:"online"`      //  是否启用
}

type AddRequest struct {
	Name        string `json:"name"`        //  流水线名称
	Group       string `json:"group"`       //  配置正文
	Content     string `json:"content"`     //  配置正文
	Description string `json:"description"` //  描述
	Order       int32  `json:"order"`       //  排序
	Online      bool   `json:"online"`      //  是否上线
	Enable      bool   `json:"enable"`      //  是否启用
	Icon        string `json:"icon"`        //  图标
	Param       string `json:"param"`       //  图标
}

type UpdateRequest struct {
	UUID        string  `json:"uuid" form:"uuid"`
	Group       string  `json:"group"`       //  配置正文
	Name        string  `json:"name"`        //  流水线名称
	Content     string  `json:"content"`     //  配置正文
	Description string  `json:"description"` //  描述
	Order       int32   `json:"order"`       //  排序
	Online      *bool   `json:"online"`      //  是否上线
	Enable      *bool   `json:"enable"`      //  是否启用
	Icon        string  `json:"icon"`        //  图标
	Param       *string `json:"param"`       //  参数
}
