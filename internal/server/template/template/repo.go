package template

import (
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"
)

type templateRepo struct {
	db *gorm.DB
}

func NewTemplateRepo(db *gorm.DB) *templateRepo {
	return &templateRepo{db: db}
}

func (repo *templateRepo) Info(uuid string) (*PipelineTemplate, error) {
	var result PipelineTemplate
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *templateRepo) Add(m *PipelineTemplate) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *templateRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&PipelineTemplate{}).Error
}

func (repo *templateRepo) Update(m *PipelineTemplate) (rowsAffected int64, err error) {
	db := repo.db.Model(PipelineTemplate{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *templateRepo) Page(r PageRequest) (pm response.PageModel, err error) {
	query := repo.db.Model(PipelineTemplate{})
	if r.Name != "" {
		query = query.Where("name like ?", util.Like(r.Name))
	}
	if r.Group != "" {
		query = query.Where("group = ?", r.Group)
	}
	if r.Enable != nil {
		query = query.Where("enable = ?", *r.Enable)
	}
	if r.Online != nil {
		query = query.Where("online = ?", *r.Online)
	}
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*PipelineTemplate
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *templateRepo) List(r ListRequest) (list []*PipelineTemplate, err error) {
	query := repo.db.Model(PipelineTemplate{})
	if r.Enable != nil {
		query = query.Where("enable = ?", *r.Enable)
	}
	if r.Online != nil {
		query = query.Where("online = ?", *r.Online)
	}
	if err = query.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}
