package template

import "pipeline/pkg/common/model"

// PipelineTemplate  pipeline_template
type PipelineTemplate struct {
	model.BaseModel
	Name        string  `gorm:"column:name" db:"name" json:"name"`                      //  流水线名称
	Group       string  `gorm:"column:group" db:"group" json:"group"`                   //  流水线名称
	Content     string  `gorm:"column:content" db:"content" json:"content"`             //  配置正文
	Description string  `gorm:"column:description" db:"description" json:"description"` //  描述
	Order       int32   `gorm:"column:order" db:"order" json:"order"`                   //  排序
	Online      *bool   `gorm:"column:online" db:"online" json:"online"`                //  是否上线
	Enable      *bool   `gorm:"column:enable" db:"enable" json:"enable"`                //  是否启用
	Icon        string  `gorm:"column:icon" db:"icon" json:"icon"`                      //  图标
	Param       *string `gorm:"column:param" db:"param" json:"param"`                   //  图标
}

func (PipelineTemplate) TableName() string {
	return "pipeline_template"
}
