package project

import (
	"pipeline/pkg/common/model"
	"time"
)

type InfoResponse struct {
	Id           int64      `json:"id"`                                              //  主键
	Uuid         string     `json:"uuid"`                                            //  唯一标示做关联
	Identity     string     `json:"identity"`                                        //  唯一标示做关联
	Name         string     `json:"name"`                                            //  项目名称
	Description  string     `json:"description"`                                     //  描述
	Creator      string     `json:"creator"`                                         //  创建人
	Modifier     string     `json:"modifier"`                                        //  修改人
	GmtCreate    model.Time `json:"gmtCreate"`                                       //  创建时间
	GmtModified  time.Time  `json:"gmtModified"`                                     //  修改时间
	Deleted      string     `json:"deleted"`                                         //  逻辑删除
	RelationShip string     `json:"relationship" gorm:"column:type_of" db:"type_of"` //项目逻辑关系

	//绑定关系
	Members    []RelationShipResponse `json:"members" gorm:"-"`
	Principals []RelationShipResponse `json:"principals" gorm:"-"`
}

type RelationShipResponse struct {
	Name   string `json:"label"`
	TypeOf string `json:"typeOf"`
	Uuid   string `json:"value"`
}
