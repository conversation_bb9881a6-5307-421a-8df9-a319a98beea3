package project

import (
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/middleware"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Add(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Update(req, middleware.GetUserEmail(c))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, info)
}

func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req, middleware.GetUserEmail(c), middleware.IsAdmin(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req, middleware.GetUserEmail(c), middleware.IsAdmin(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, response.PageModel{
		List: list,
	})
}

func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

func Request(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req RequestJoin
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewInfoService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.Request(req, c.GetString(common.UserEmail))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}
