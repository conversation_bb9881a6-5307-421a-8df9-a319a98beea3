package project

import (
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/util"
	"time"

	"gorm.io/gorm"
)

type infoRepo struct {
	db *gorm.DB
}

func NewInfoRepo(db *gorm.DB) *infoRepo {
	return &infoRepo{db: db}
}

func (repo *infoRepo) Info(id int64) (*models.ProjectInfo, error) {
	var result models.ProjectInfo
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *infoRepo) Add(m *models.ProjectInfo) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *infoRepo) SoftDelete(ids []string, osp string) error {
	//因为identity是唯一的，所以软删除时，需要将deleted字段置为true，将identity置为identity-uuid防止重复
	return repo.db.Model(&models.ProjectInfo{}).
		Where("uuid in (?)", ids).
		Updates(map[string]any{
			"identity": gorm.Expr("concat(identity,concat('-',uuid))"),
			"deleted":  true,
			"modifier": osp,
		}).Error
}

func (repo *infoRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.ProjectInfo{}).Error
}

func (repo *infoRepo) Update(m *models.ProjectInfo) (rowsAffected int64, err error) {
	db := repo.db.Model(models.ProjectInfo{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *infoRepo) Page(r PageRequest, uuid string, isAdmin bool) (pm response.PageModel, err error) {
	query := repo.db.Table("project_info pi").
		Select("distinct pi.*,piou.type_of").
		Joins("left join project_info_of_user piou on pi.uuid = piou.project_uuid and piou.user_uuid = ?", uuid)
	if r.TypeOf != common.All && r.TypeOf != "" {
		// 负责人和参与人
		if r.TypeOf == common.MemberAndPrincipal {
			query = query.Where("piou.type_of in (?)", []string{common.Member, common.Principal})
		} else {
			query = query.Where("piou.type_of = ?", r.TypeOf) // 特定关系
		}
	}
	// 项目名称
	if r.Name != "" {
		query = query.Where("pi.name like ? or pi.identity like ?", util.Like(r.Name), util.Like(r.Name))
	}
	// 删除标记
	query = query.Where("pi.deleted = false")

	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*InfoResponse
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	//
	if isAdmin {
		for i := range list {
			list[i].RelationShip = common.Principal
		}
	}
	return pm, nil
}

func (repo *infoRepo) List(r ListRequest, email string, isAdmin bool) (list []*models.ProjectInfo, err error) {
	query := repo.db.Model(models.ProjectInfoOfUser{})
	if isAdmin {
		query = repo.db.Table("project_info pi")
	} else {
		query = query.Select("pi.*").
			Joins("left join project_info pi on pi.uuid = project_uuid").
			Where("user_uuid = ? and pi.uuid  is not null", email)
	}
	if r.Name != "" {
		query = query.Where("pi.name like ? or pi.identity like ?", util.Like(r.Name), util.Like(r.Name))
	}
	//删除标记
	query = query.Where("pi.deleted = false").Order("gmt_create desc")

	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *infoRepo) AddRelationShip(list []models.ProjectInfoOfUser) error {
	db := repo.db.Create(list)
	if err := db.Error; err != nil {
		return err
	}
	return nil
}

func (repo *infoRepo) DeleteRelationShip(ids []int64) error {
	return repo.db.Where("id in (?)", ids).Delete(models.ProjectInfoOfUser{}).Error
}

func (repo *infoRepo) GetProjectRelationShipList(uuid string) (list []models.ProjectInfoOfUser, err error) {
	if err = repo.db.Model(models.ProjectInfoOfUser{}).Where("project_uuid = ?", uuid).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *infoRepo) GetProjectRelationShipWithName(uuid string) (list []models.ProjectInfoOfUser, err error) {
	if err = repo.db.Model(models.ProjectInfoOfUser{}).
		//Select("project_info_of_user.*,su.name name,su.value uuid").
		//Joins(fmt.Sprintf("left join `%s`.users su on su.value = project_info_of_user.user_uuid", config.Items().Mysql.DBName)).
		Where("project_uuid = ?", uuid).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *infoRepo) GetByUUID(uuid string) (*models.ProjectInfo, error) {
	var result models.ProjectInfo
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *infoRepo) AddUserToProjectIfNotExists(userIdList []string,
	projectUUID string, ops string) error {
	// 如果存在则不添加, 否则默认添加为参与者
	var existingUsers []string

	// 查询已存在的用户
	if err := repo.db.Model(&models.ProjectInfoOfUser{}).
		Where("project_uuid = ? AND user_uuid IN ?", projectUUID, userIdList).
		Pluck("user_uuid", &existingUsers).Error; err != nil {
		return err
	}

	// 找出不存在的用户
	var newUsers []string
	existingMap := make(map[string]bool)
	for _, user := range existingUsers {
		existingMap[user] = true
	}

	for _, user := range userIdList {
		if !existingMap[user] {
			newUsers = append(newUsers, user)
		}
	}

	// 如果没有新用户需要添加，直接返回
	if len(newUsers) == 0 {
		return nil
	}

	// 创建用户与项目的关系记录
	now := time.Now()
	var relations []models.ProjectInfoOfUser
	for _, userUUID := range newUsers {
		relations = append(relations, models.ProjectInfoOfUser{
			ProjectUuid: projectUUID,
			UserUuid:    userUUID,
			TypeOf:      common.Member, // 默认添加为参与者
			Creator:     ops,
			GmtCreate:   now,
		})
	}

	// 批量添加关系
	return repo.AddRelationShip(relations)
}
