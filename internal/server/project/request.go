package project

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	Name string
	request.PageRequest
}

type PageRequest struct {
	request.PageRequest
	ShowRelationship bool   `json:"showRelationship"` //  是否需要展示项目关系
	Name             string `json:"name"`             //  项目名称
	TypeOf           string `json:"typeOf"`           //  项目类型
	ProjectDesc      string `json:"projectDesc"`      //  描述
}

type AddRequest struct {
	Name        string   `json:"name"`        //  项目名称
	Identity    string   `json:"identity"`    //  项目标识
	Description string   `json:"description"` //  描述
	Principals  []string //负责人uuid
	Members     []string //参与人uuid
}

type UpdateRequest struct {
	request.UpdateRequest
	Name        string   `json:"name"`        //  项目名称
	Description string   `json:"description"` //  描述
	Principals  []string //负责人uuid
	Members     []string //参与人uuid
}
type RequestJoin struct {
	ProjectUUID string `json:"projectUUID" form:"projectUUID" bind:"required"`
	Role        string `json:"role" form:"role" bind:"required"`
	Reason      string `json:"reason" form:"reason"`
}
