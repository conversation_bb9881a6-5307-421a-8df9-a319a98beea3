package project

import (
	"context"
	"fmt"
	"pipeline/internal/server/system/user"
	"pipeline/pkg/common/model"
	"pipeline/pkg/common/request"
	"pipeline/pkg/models"
	"pipeline/pkg/notify"
	"pipeline/pkg/notify/types"
	"pipeline/pkg/util"

	"time"

	"pipeline/pkg/common"
	"pipeline/pkg/common/response"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

type infoService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewInfoService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *infoService {
	svc := &infoService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *infoService) Info(uuid string) (*InfoResponse, error) {
	repo := NewInfoRepo(s.db)
	m, err := repo.GetByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	//查询绑定的关系
	list, err := repo.GetProjectRelationShipWithName(m.UUID)
	if err != nil {
		return nil, err
	}
	info.Members = make([]RelationShipResponse, 0)
	info.Principals = make([]RelationShipResponse, 0)
	// rpc query account
	emails := make([]string, 0)
	for _, r := range list {
		emails = append(emails, r.UserUuid)
	}
	accounts, err := user.NewUserService(nil, s.db, s.redisClient).Page(user.PageRequest{
		Emails: emails,
		PageRequest: request.PageRequest{
			PageSize: model.MaxPageSize,
		},
	})
	if err != nil {
		return nil, err
	}
	// map account
	accountsMap := make(map[string]models.User)
	for _, acc := range accounts.List {
		accountsMap[acc.Email] = acc
	}
	//对关系进行分组
	for _, r := range list {
		user := RelationShipResponse{
			TypeOf: r.TypeOf,
		}
		acc, ok := accountsMap[r.UserUuid]
		if ok {
			user.Name = acc.Name
			user.Uuid = acc.Email
		} else {
			log.Printf("failed to get account by email: %v", r)
		}
		if r.TypeOf == common.Principal {
			info.Principals = append(info.Principals, user)
		}
		if r.TypeOf == common.Member {
			info.Members = append(info.Members, user)
		}
	}
	return &info, nil
}

func (s *infoService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewInfoRepo(s.db)
	var m models.ProjectInfo
	err = copier.Copy(&m, &r)
	m.Create(ops)
	if err != nil {
		return 0, err
	}
	//保存关联关系
	now := time.Now()
	list := make([]models.ProjectInfoOfUser, 0)
	for _, member := range r.Members {
		list = append(list, models.ProjectInfoOfUser{
			UserUuid:    member,
			ProjectUuid: m.UUID,
			TypeOf:      common.Member,
			Creator:     ops,
			GmtCreate:   now,
		})
	}
	for _, principal := range r.Principals {
		list = append(list, models.ProjectInfoOfUser{
			UserUuid:    principal,
			ProjectUuid: m.UUID,
			TypeOf:      common.Principal,
			Creator:     ops,
			GmtCreate:   now,
		})
	}
	if err = repo.AddRelationShip(list); err != nil {
		return 0, err
	}
	return repo.Add(&m)
}

func (s *infoService) Delete(ids []string, ops string) error {
	repo := NewInfoRepo(s.db)
	return repo.SoftDelete(ids, ops)
}

func (s *infoService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewInfoRepo(s.db)
	m, err := repo.GetByUUID(r.UUID)
	if err != nil {
		return 0, err
	}
	if err = model.CheckVersionConflict(r.GmtModified, m.GmtModified); err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	// 更新关联关系
	ships, err := repo.GetProjectRelationShipList(m.UUID)
	if err != nil {
		return 0, err
	}
	principalsMap := make(map[string]models.ProjectInfoOfUser)
	membersMap := make(map[string]models.ProjectInfoOfUser)
	//对关系进行分组
	for _, ship := range ships {
		if ship.TypeOf == common.Principal {
			principalsMap[ship.UserUuid] = ship
		}
		if ship.TypeOf == common.Member {
			membersMap[ship.UserUuid] = ship
		}
	}
	now := time.Now()
	addProjectInfoOfUser := make([]models.ProjectInfoOfUser, 0)
	//成员
	for _, member := range r.Members {
		//已经存在无需操作
		if _, ok := membersMap[member]; ok {
			delete(membersMap, member)
			continue
		}
		//需要新增
		addProjectInfoOfUser = append(addProjectInfoOfUser, models.ProjectInfoOfUser{
			UserUuid:    member,
			ProjectUuid: m.UUID,
			TypeOf:      common.Member,
			Creator:     ops,
			GmtCreate:   now,
		})
	}
	//map中剩下的都是需要删除的
	//管理员
	for _, principal := range r.Principals {
		//已经存在无需操作
		if _, ok := principalsMap[principal]; ok {
			delete(principalsMap, principal)
			continue
		}
		//需要新增
		addProjectInfoOfUser = append(addProjectInfoOfUser, models.ProjectInfoOfUser{
			UserUuid:    principal,
			ProjectUuid: m.UUID,
			TypeOf:      common.Principal,
			Creator:     ops,
			GmtCreate:   now,
		})
	}
	//新增
	err = repo.AddRelationShip(addProjectInfoOfUser)
	if err != nil {
		return 0, err
	}
	//删除
	deleteIds := make([]int64, 0)
	for _, shipResponse := range principalsMap {
		deleteIds = append(deleteIds, shipResponse.ID)
	}
	for _, shipResponse := range membersMap {
		deleteIds = append(deleteIds, shipResponse.ID)
	}
	if len(deleteIds) > 0 {
		err = repo.DeleteRelationShip(deleteIds)
	}
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	return repo.Update(m)
}

func (s *infoService) Page(r PageRequest, uuid string, isAdmin bool) (response.PageModel, error) {
	repo := NewInfoRepo(s.db)
	return repo.Page(r, uuid, isAdmin)
}

func (s *infoService) List(r ListRequest, email string, isAdmin bool) (results []*models.ProjectInfo, err error) {
	repo := NewInfoRepo(s.db)
	return repo.List(r, email, isAdmin)
}

func (s *infoService) Request(req RequestJoin, email string) error {
	// 发生通知信息给项目管理员
	var projectPrincipals []string
	if err := s.db.Model(&models.ProjectInfoOfUser{}).
		Select("distinct user_uuid").
		Where("project_uuid = ? and type_of = ?", req.ProjectUUID, common.Principal).
		Find(&projectPrincipals).Error; err != nil {
		return err
	}
	if len(projectPrincipals) == 0 {
		log.Error("project has no principal",
			log.Any("project_uuid", req.ProjectUUID))
		return nil
	}
	// 查询项目
	var projectInfo models.ProjectInfo
	if err := s.db.Where("uuid = ?", req.ProjectUUID).
		First(&projectInfo).Error; err != nil {
		return err
	}
	dataSource := make(map[string]any)
	dataSource["time"] = util.NowDateTime()
	dataSource["user"] = email
	dataSource["operator"] = ""
	dataSource["remark"] = ""
	dataSource["role"] = req.Role
	dataSource["disable"] = false
	dataSource["reason"] = req.Reason
	dataSource["resource"] = projectInfo.UUID
	dataSource["color"] = models.ColorMap[models.ApproveStatePending]
	dataSource["name"] = fmt.Sprintf("%s (项目)", projectInfo.Name)
	// 保存申请信息
	entry := models.ProjectAppRequest{
		Type: models.RequestTypeProject,
	}
	entry.Create(email)
	dataSource["uuid"] = entry.UUID
	entry.DataSource = util.ToJSONString(dataSource)
	if err := notify.SendNotification(context.Background(), types.NotifyData{
		Type:         types.FeishuPerson,
		Receivers:    projectPrincipals,
		TemplateType: types.ProjectAppRequest,
		DataSource:   dataSource,
	}); err != nil {
		return err
	}
	return s.db.Create(&entry).Error
}
