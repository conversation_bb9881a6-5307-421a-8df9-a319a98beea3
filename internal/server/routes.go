package server

import (
	"github.com/gin-gonic/gin"
	"pipeline/internal/server/ai/diagnosis"
	"pipeline/internal/server/app"
	"pipeline/internal/server/artifact"
	"pipeline/internal/server/credential"
	"pipeline/internal/server/docs"
	"pipeline/internal/server/executor"
	"pipeline/internal/server/healthz"
	"pipeline/internal/server/logger"
	"pipeline/internal/server/metrics"
	"pipeline/internal/server/pipeline"
	"pipeline/internal/server/project"
	"pipeline/internal/server/runner"
	"pipeline/internal/server/secret"
	"pipeline/internal/server/source"
	stepGroup "pipeline/internal/server/step/group"
	"pipeline/internal/server/step/step"
	"pipeline/internal/server/system/user"
	templateGroup "pipeline/internal/server/template/group"
	"pipeline/internal/server/template/template"
	"pipeline/internal/server/trigger"
)

func registerRouters(router *gin.Engine) {
	healthz.RegisterRouters(router)
	docs.RegisterRouters(router)
	user.RegisterRouter(router)
	project.RegisterRouter(router)
	app.RegisterRouter(router)
	source.RegisterRouter(router)
	pipeline.RegisterRouter(router)
	logger.RegisterRouter(router)
	runner.RegisterRouter(router)
	secret.RegisterRouter(router)
	step.RegisterRouter(router)
	stepGroup.RegisterRouter(router)
	trigger.RegisterRouter(router)
	credential.RegisterRouter(router)
	template.RegisterRouter(router)
	templateGroup.RegisterRouter(router)
	metrics.RegisterRouter(router)
	executor.RegisterRouter(router)
	artifact.RegisterRouter(router)
	diagnosis.RegisterRouter(router)
}
