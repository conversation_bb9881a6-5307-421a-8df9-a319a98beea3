package trigger

import (
	"github.com/gin-gonic/gin"
	"pipeline/internal/server/trigger/event"
	"pipeline/internal/server/trigger/webhook"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/pipeline/run")
	{
		// webhook触发
		cAPI.GET("/:uuid", webhook.GetTriggerWebhookHandler)
		cAPI.POST("/:uuid", webhook.PostTriggerWebhookHandler)
		// 平台相关的触发
		cAPI.POST("/gitea/:uuid", event.GiteaWebhookHandler)
		cAPI.POST("/github/:uuid", event.GithubWebhookHandler)
		cAPI.POST("/gitlab/:uuid", event.GitlabWebhookHandler)
		//...
	}
}
