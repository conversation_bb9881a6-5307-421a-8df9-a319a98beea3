package cron

import (
	"context"

	"github.com/robfig/cron/v3"
)

type CronEntry interface {
	// add a schedule to generate cron job
	Add(schedule string, cmd func()) (*cron.Entry, error)

	// remove a cron job from with the given id
	Remove(id cron.EntryID)

	// remove all cron jobs
	CleanAll()

	// get all cron jobs
	List() []cron.Entry

	// get a cron job by id
	GetEntryByID(id cron.EntryID) cron.Entry

	// Start the cron jobs
	Start()
}

// GetEntryCtrl gets the cron entry controller
func GetEntryCtrl(ctx context.Context, cronOpts ...cron.Option) CronEntry {
	// add more storage type here
	return NewLocalCache(ctx, cronOpts...)
}
