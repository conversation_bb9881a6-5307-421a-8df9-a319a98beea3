package cron

import (
	"context"
	"fmt"

	"git.makeblock.com/makeblock-go/log"
	"github.com/robfig/cron/v3"
)

type LocalCache struct {
	Ctx  context.Context
	Cron *cron.Cron
}

func NewLocalCache(ctx context.Context, cronOpts ...cron.Option) *LocalCache {
	return &LocalCache{
		Ctx:  ctx,
		Cron: cron.New(cronOpts...),
	}
}

func (cache *LocalCache) Start() {
	log.Info("--- start cron job ---")
	cache.Cron.Start()
}

// add a schedule to generate cron job
func (cache *LocalCache) Add(schedule string, cmd func()) (*cron.Entry, error) {
	entryID, err := cache.Cron.AddFunc(schedule, cmd)
	if err != nil {
		log.Error(fmt.Sprintf("add cron job failed, error: %v", err))
		return nil, err
	}
	entry := cache.Cron.Entry(entryID)
	return &entry, nil
}

// remove a cron job from with the given id
func (cache *LocalCache) Remove(id cron.EntryID) {
	cache.Cron.Remove(id)
}

// remove all cron jobs
func (cache *LocalCache) CleanAll() {
	log.Info("clean all cron jobs")
	entries := cache.Cron.Entries()
	for _, entry := range entries {
		cache.Cron.Remove(entry.ID)
	}
}

// get all cron jobs
func (cache *LocalCache) List() []cron.Entry {
	return cache.Cron.Entries()
}

// get a cron job by id
func (cache *LocalCache) GetEntryByID(id cron.EntryID) cron.Entry {
	return cache.Cron.Entry(id)
}
