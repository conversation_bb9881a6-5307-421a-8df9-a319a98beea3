package cron

import (
	"context"
	"sync"
	"time"

	"pipeline/pkg/models"

	"git.makeblock.com/makeblock-go/log"
	mbredis "git.makeblock.com/makeblock-go/redis"
	"github.com/go-redis/redis/v8"
	"github.com/robfig/cron/v3"
)

// PipelineEntry is the entry of pipeline cron job
type PipelineEntry struct {
	NextTime time.Time
	Schedule string
	EntryID  cron.EntryID
	Pipeline *models.PipelineInfo
}

// CronJobController is the controller of cron job
type CronJobController struct {
	*redis.Client
	EntryMutex sync.Mutex
	EntryIds   map[string]PipelineEntry
	EntryCtrl  CronEntry
}

var (
	cronJobCtrl *CronJobController
	once        sync.Once
)

// GetCronJobCtrl gets the cron job controller
func GetCronJobCtrl(ctx context.Context, cronOpts ...cron.Option) *CronJobController {
	once.Do(func() {
		cronJobCtrl = NewCronJobController(ctx, cronOpts...)
	})
	return cronJobCtrl
}

// NewCronJobController creates a new cron job controller
func NewCronJobController(ctx context.Context, cronOpts ...cron.Option) *CronJobController {
	return &CronJobController{
		Client:     mbredis.GetClient(),
		EntryMutex: sync.Mutex{},
		EntryIds:   make(map[string]PipelineEntry),
		EntryCtrl:  GetEntryCtrl(ctx, cronOpts...),
	}
}

// AddPipelineCronJob adds a pipeline cron job with schedule
func (c *CronJobController) AddPipelineCronJob(pipeUUID, pipeName, schedule string, cmd func()) error {
	log.Info("add pipeline cron job", log.Any("pipelineID", pipeUUID),
		log.Any("pipelineName", pipeName), log.Any("schedule", schedule))

	if _, ok := c.EntryIds[pipeUUID]; ok {
		log.Info("pipeline cron job already exists", log.Any("pipeline uuid", pipeUUID))
		return nil
	}

	if len(schedule) == 0 {
		log.Info("pipeline cron job schedule is empty", log.Any("pipeline uuid", pipeUUID))
		return nil
	}

	entry, err := c.EntryCtrl.Add(schedule, cmd)
	if err != nil {
		log.Error("add pipeline schedule failed", log.Any("error", err))
		return err
	}

	c.EntryMutex.Lock()
	defer c.EntryMutex.Unlock()

	c.EntryIds[pipeUUID] = PipelineEntry{
		NextTime: entry.Schedule.Next(time.Now()),
		Schedule: schedule,
		EntryID:  entry.ID,
	}

	return nil
}

// RemovePipelineCronJob removes a pipeline cron job with pipeline UUID
func (c *CronJobController) RemovePipelineCronJob(pipeUUID string) {
	c.EntryMutex.Lock()
	defer c.EntryMutex.Unlock()

	entry, ok := c.EntryIds[pipeUUID]
	if !ok {
		log.Info("pipeline cron job not exists", log.Any("pipeline UUID", pipeUUID))
		return
	}

	c.EntryCtrl.Remove(entry.EntryID)
	delete(c.EntryIds, pipeUUID)
}

// UpdatePipelineCronJob updates a pipeline cron job with pipelineID and schedule
func (c *CronJobController) UpdatePipelineCronJob(pipeUUID, schedule string, cmd func()) error {
	log.Info("update pipeline cron job", log.Any("pipelineID", pipeUUID))

	c.EntryMutex.Lock()
	defer c.EntryMutex.Unlock()

	// remove the old cron job
	pEntry, ok := c.EntryIds[pipeUUID]
	if ok {
		log.Info("remove old cron job", log.Any("pipelineID", pipeUUID))
		c.EntryCtrl.Remove(pEntry.EntryID)
	}

	entry, err := c.EntryCtrl.Add(schedule, cmd)
	if err != nil {
		log.Error("add pipeline schedule failed", log.Any("error", err))
		return err
	}

	c.EntryIds[pipeUUID] = PipelineEntry{
		NextTime: entry.Schedule.Next(time.Now()),
		Schedule: schedule,
		EntryID:  entry.ID,
	}

	return nil
}
