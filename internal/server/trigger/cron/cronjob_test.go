package cron

import (
	"context"
	"fmt"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("CronJobController", func() {
	var (
		ctx      context.Context
		ctrl     *CronJobController
		pipeUUID string
		schedule string
		pipeName string
		cmd      func()
	)

	BeforeEach(func() {
		ctx = context.TODO()
		ctrl = GetCronJobCtrl(ctx)
		pipeUUID = "93f101be493d11ecb7c254e1ad134d77"
		pipeName = "test"
		schedule = "* * * * *"
		cmd = func() {
			fmt.Println("test")
		}
	})

	Context("AddPipelineCronJob", func() {
		When("schedule is empty", func() {
			BeforeEach(func() {
				schedule = ""
			})

			It("should not add cron job", func() {
				err := ctrl.AddPipelineCronJob(pipeUUID, pipeName, schedule, cmd)
				Expect(err).Should(BeNil())

				Expect(len(ctrl.EntryIds)).Should(Equal(0))

				ctrl.EntryCtrl.CleanAll()
				ctrl.EntryIds = make(map[string]PipelineEntry)
			})
		})

		When("schedule is not empty", func() {
			It("should add cron job", func() {
				err := ctrl.AddPipelineCronJob(pipeUUID, pipeName, schedule, cmd)
				Expect(err).Should(BeNil())
				Expect(len(ctrl.EntryIds)).Should(Equal(1))

				entry, ok := ctrl.EntryIds[pipeUUID]
				Expect(ok).Should(BeTrue())
				Expect(entry.Schedule).Should(Equal(schedule))

				ctrl.EntryCtrl.CleanAll()
				ctrl.EntryIds = make(map[string]PipelineEntry)
			})
		})
	})

	Context("RemovePipelineCronJob", func() {
		When("pipeline cron job exists", func() {
			BeforeEach(func() {
				ctrl.AddPipelineCronJob(pipeUUID, pipeName, schedule, cmd)
			})

			It("should remove cron job", func() {
				ctrl.RemovePipelineCronJob(pipeUUID)
				Expect(len(ctrl.EntryIds)).Should(Equal(0))

				ctrl.EntryCtrl.CleanAll()
				ctrl.EntryIds = make(map[string]PipelineEntry)
			})
		})

		When("pipeline cron job not exists", func() {
			It("should not remove cron job", func() {
				ctrl.RemovePipelineCronJob(pipeUUID)
				Expect(len(ctrl.EntryIds)).Should(Equal(0))

				ctrl.EntryCtrl.CleanAll()
				ctrl.EntryIds = make(map[string]PipelineEntry)
			})
		})
	})

	Context("UpdatePipelineCronJob", func() {
		When("pipeline cron job exists", func() {
			BeforeEach(func() {
				ctrl.AddPipelineCronJob(pipeUUID, pipeName, schedule, cmd)
			})

			It("should update cron job", func() {
				newSchedule := "*/1 * * * *"
				err := ctrl.UpdatePipelineCronJob(pipeUUID, newSchedule, cmd)
				Expect(err).Should(BeNil())

				entry, ok := ctrl.EntryIds[pipeUUID]
				Expect(ok).Should(BeTrue())
				Expect(entry.Schedule).Should(Equal(newSchedule))
				Expect(len(ctrl.EntryIds)).Should(Equal(1))

				ctrl.EntryCtrl.CleanAll()
				ctrl.EntryIds = make(map[string]PipelineEntry)
			})
		})

		When("pipeline cron job not exists", func() {
			It("should add cron job", func() {
				newSchedule := "*/1 * * * *"
				err := ctrl.UpdatePipelineCronJob(pipeUUID, newSchedule, cmd)
				Expect(err).Should(BeNil())
				Expect(len(ctrl.EntryIds)).Should(Equal(1))

				ctrl.EntryCtrl.CleanAll()
				ctrl.EntryIds = make(map[string]PipelineEntry)
			})
		})
	})
})
