package cron

import (
	"context"
	"fmt"
	"testing"

	. "github.com/onsi/gomega"
)

func TestAdd(t *testing.T) {
	g := NewWithT(t)

	cache := NewLocalCache(context.Background())
	_, err := cache.Add("* * * * *", func() {
		fmt.Println("test")
	})
	g.Expect(err).Should(BeNil())

	entries := cache.List()
	g.Expect(len(entries)).Should(Equal(1))

	cache.CleanAll()
	entries = cache.List()
	g.Expect(len(entries)).Should(Equal(0))
}
