package event

import (
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"github.com/gin-gonic/gin"
	"pipeline/internal/server/pipeline"
	"pipeline/pkg/common/response"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	pd "pipeline/pkg/pipeline"
	"pipeline/pkg/util"
	"regexp"
	"strings"
	"time"
)

type TriggeredPipeline struct {
	UUID   string `json:"uuid"`   // 流水线uuid
	Name   string `json:"name"`   // 流水线名称
	TxUUID string `json:"txUUID"` // 执行记录uuid
}

type TriggeredResult struct {
	TriggeredPipelines []TriggeredPipeline `json:"triggeredPipelines,omitempty"`
	Errors             []error             `json:"errors,omitempty"`
}

func checkTriggers(mt pd.MatchType, message string) bool {
	for _, precise := range mt.Precise {
		if message == precise {
			return true
		}
	}
	for _, prefix := range mt.Prefix {
		if strings.HasPrefix(message, prefix) {
			return true
		}
	}
	for _, prefix := range mt.Include {
		if strings.Contains(message, prefix) {
			return true
		}
	}
	for _, regex := range mt.Regex {
		if ok, _ := regexp.MatchString(regex, message); ok {
			return true
		}
	}
	return false
}

// CommitInfo 统一的提交信息结构
type CommitInfo struct {
	ID        string
	Message   string
	URL       string
	Timestamp time.Time
	Author    struct {
		Name  string
		Email string
	}
}

// WebhookEvent 通用的webhook事件接口
type WebhookEvent interface {
	GetRef() string           // 获取引用（refs/heads/main 或 refs/tags/v1.0.0）
	GetCommitSHA() string     // 获取提交SHA
	GetCommits() []CommitInfo // 获取提交列表
	GetTriggerUser() string   // 获取触发用户
	IsPushEvent() bool        // 是否为推送事件
	IsTagEvent() bool         // 是否为标签事件
	IsValidTrigger() bool     // 是否为有效触发（如CheckoutSha不为空等）
}

// HandleWebhookEvent 通用的webhook处理函数
func HandleWebhookEvent(c *gin.Context, event WebhookEvent) {
	appUUID := c.Param("uuid")

	// 检查参数
	if appUUID == "" {
		response.Err(c, response.RequestParamError("param is required"))
		return
	}

	// 检查是否为有效触发
	if !event.IsValidTrigger() {
		response.OK(c, "not a valid trigger event")
		return
	}

	// 获取该app下的所有流水线
	deleted := false
	appPipelines, err := pipeline.NewInfoService(nil, mysql.GetDB(), redis.GetClient()).List(pipeline.ListRequest{
		AppUUID: appUUID,
		Deleted: &deleted,
	})
	if err != nil {
		errors.ResponseError(c, err)
		log.Error(err.Error())
		return
	}

	triggeredResult := TriggeredResult{
		TriggeredPipelines: make([]TriggeredPipeline, 0),
		Errors:             make([]error, 0),
	}

	for _, pipelineModel := range appPipelines {
		// 解析流水线
		parsePipeline, parseErr := pd.ParsePipeline(pipelineModel.Content)
		if parseErr != nil {
			parseErr = fmt.Errorf("failed to parse pipeline: %s, error: %v", pipelineModel.UUID, parseErr)
			triggeredResult.Errors = append(triggeredResult.Errors, parseErr)
			log.Error(parseErr.Error())
			continue
		}

		triggers := parsePipeline.Triggers
		// 如果没有触发条件，则不触发流水线
		if triggers.Push == nil {
			continue
		}

		// 检查事件触发
		log.Printf("triggers = %v\n", triggers)
		if opts, okk := checkWebhookTriggersMatch(triggers.Push, event); okk {
			// 基础配置
			txUUID := util.UUID()
			baseOpts := append([]pd.Option{
				pd.WithTriggerWay(pd.CodeSourceWebhook),
				pd.WithTxUUID(txUUID),
				pd.WithPipelineUUID(pipelineModel.UUID),
				pd.WithTriggerUser(event.GetTriggerUser()),
			}, opts...)

			log.Printf("exec pipeline = %v\n", pipelineModel.Name)

			// 触发流水线执行
			if err = eventbus.Publish(c, &events.RunPipelineEvent{
				Opts: pd.NewPipelineContextOption(baseOpts...),
			}); err != nil {
				err = fmt.Errorf("failed to trigger pipeline: %s, error: %v", pipelineModel.UUID, err)
				triggeredResult.Errors = append(triggeredResult.Errors, err)
				log.Error(err.Error())
			} else {
				triggeredResult.TriggeredPipelines = append(triggeredResult.TriggeredPipelines,
					TriggeredPipeline{
						UUID:   pipelineModel.UUID,
						Name:   pipelineModel.Name,
						TxUUID: txUUID,
					})
			}
		}
	}

	response.OK(c, triggeredResult)
}

// checkWebhookTriggersMatch 检查webhook事件是否匹配触发条件
func checkWebhookTriggersMatch(push *pd.Push, event WebhookEvent) ([]pd.Option, bool) {
	if event.IsPushEvent() {
		if opt, ok := checkWebhookBranchTriggers(push.Branch, event); ok {
			return opt, true
		}
		if opt, ok := checkWebhookCommitTriggers(push.Commit, event); ok {
			return opt, true
		}
	}

	if event.IsTagEvent() {
		if opt, ok := checkWebhookTagTriggers(push.Tag, event); ok {
			return opt, true
		}
		if opt, ok := checkWebhookCommitTriggers(push.Commit, event); ok {
			return opt, true
		}
	}

	return nil, false
}

// checkWebhookCommitTriggers 检查提交触发条件
func checkWebhookCommitTriggers(cs pd.Commit, event WebhookEvent) ([]pd.Option, bool) {
	commits := event.GetCommits()
	for _, commit := range commits {
		if checkTriggers(cs.MatchType, commit.Message) {
			return []pd.Option{
				pd.WithCheckout(pd.Checkout{
					Ref:           stringPtr(event.GetRef()),
					CheckoutSHA:   stringPtr(event.GetCommitSHA()),
					CommitMessage: stringPtr(commit.Message),
					CommitURL:     stringPtr(commit.URL),
				}),
			}, true
		}
	}
	return nil, false
}

// checkWebhookBranchTriggers 检查分支触发条件
func checkWebhookBranchTriggers(branch pd.Branch, event WebhookEvent) ([]pd.Option, bool) {
	ref := event.GetRef()
	if strings.HasPrefix(ref, "refs/heads/") {
		branchName := strings.TrimPrefix(ref, "refs/heads/")
		if checkTriggers(branch.MatchType, branchName) {
			// 获取最新的commit信息
			commits := event.GetCommits()
			var commitMessage, commitURL string
			if len(commits) > 0 {
				latestCommit := commits[len(commits)-1]
				commitMessage = latestCommit.Message
				commitURL = latestCommit.URL
			}

			return []pd.Option{
				pd.WithCheckout(pd.Checkout{
					Ref:           stringPtr(ref),
					CheckoutSHA:   stringPtr(event.GetCommitSHA()),
					Branch:        branchName,
					CommitMessage: stringPtr(commitMessage),
					CommitURL:     stringPtr(commitURL),
				}),
			}, true
		}
	}
	return nil, false
}

// checkWebhookTagTriggers 检查标签触发条件
func checkWebhookTagTriggers(tag pd.Tag, event WebhookEvent) ([]pd.Option, bool) {
	ref := event.GetRef()
	if strings.HasPrefix(ref, "refs/tags/") {
		tagName := strings.TrimPrefix(ref, "refs/tags/")
		if checkTriggers(tag.MatchType, tagName) {
			// 获取最新的commit信息
			commits := event.GetCommits()
			var commitMessage, commitURL string
			if len(commits) > 0 {
				latestCommit := commits[len(commits)-1]
				commitMessage = latestCommit.Message
				commitURL = latestCommit.URL
			}

			return []pd.Option{
				pd.WithCheckout(pd.Checkout{
					Tag:           stringPtr(tagName),
					Ref:           stringPtr(ref),
					CheckoutSHA:   stringPtr(event.GetCommitSHA()),
					CommitMessage: stringPtr(commitMessage),
					CommitURL:     stringPtr(commitURL),
				}),
			}, true
		}
	}
	return nil, false
}

// stringPtr 返回字符串指针的辅助函数
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
