package event

import (
	"pipeline/pkg/common/response"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

/*
{
  "ref": "refs/heads/main",
  "before": "db04dac6f117424295a9c0b8ec44651dee11d3aa",
  "after": "65bb47c632a42c51278356c2127123f7b00b3927",
  "compare_url": "https://gitea.com/mikeygithub/abc/compare/db04dac6f117424295a9c0b8ec44651dee11d3aa...65bb47c632a42c51278356c2127123f7b00b3927",
  "commits": [
    {
      "id": "65bb47c632a42c51278356c2127123f7b00b3927",
      "message": "Add README.md\n",
      "url": "https://gitea.com/mikeygithub/abc/commit/65bb47c632a42c51278356c2127123f7b00b3927",
      "author": {
        "name": "mikeygithub",
        "email": "<EMAIL>",
        "username": "mikeygithub"
      },
      "committer": {
        "name": "mikeygithub",
        "email": "<EMAIL>",
        "username": "mikeygithub"
      },
      "verification": null,
      "timestamp": "2025-06-02T04:33:09Z",
      "added": [
        "README.md"
      ],
      "removed": [],
      "modified": []
    }
  ],
  "total_commits": 1,
  "head_commit": {
    "id": "65bb47c632a42c51278356c2127123f7b00b3927",
    "message": "Add README.md\n",
    "url": "https://gitea.com/mikeygithub/abc/commit/65bb47c632a42c51278356c2127123f7b00b3927",
    "author": {
      "name": "mikeygithub",
      "email": "<EMAIL>",
      "username": "mikeygithub"
    },
    "committer": {
      "name": "mikeygithub",
      "email": "<EMAIL>",
      "username": "mikeygithub"
    },
    "verification": null,
    "timestamp": "2025-06-02T04:33:09Z",
    "added": [
      "README.md"
    ],
    "removed": [],
    "modified": []
  },
  "repository": {
    "id": 56280,
    "owner": {
      "id": 65961,
      "login": "mikeygithub",
      "login_name": "",
      "source_id": 0,
      "full_name": "",
      "email": "<EMAIL>",
      "avatar_url": "https://seccdn.libravatar.org/avatar/f7fc3e04e23200455edc7202ba171036?d=identicon",
      "html_url": "https://gitea.com/mikeygithub",
      "language": "",
      "is_admin": false,
      "last_login": "0001-01-01T00:00:00Z",
      "created": "2024-04-30T00:46:25Z",
      "restricted": false,
      "active": false,
      "prohibit_login": false,
      "location": "",
      "website": "",
      "description": "",
      "visibility": "public",
      "followers_count": 0,
      "following_count": 0,
      "starred_repos_count": 0,
      "username": "mikeygithub"
    },
    "name": "abc",
    "full_name": "mikeygithub/abc",
    "description": "",
    "empty": false,
    "private": true,
    "fork": false,
    "template": true,
    "parent": null,
    "mirror": false,
    "size": 34,
    "language": "",
    "languages_url": "https://gitea.com/api/v1/repos/mikeygithub/abc/languages",
    "html_url": "https://gitea.com/mikeygithub/abc",
    "url": "https://gitea.com/api/v1/repos/mikeygithub/abc",
    "link": "",
    "ssh_url": "*************:mikeygithub/abc.git",
    "clone_url": "https://gitea.com/mikeygithub/abc.git",
    "original_url": "",
    "website": "",
    "stars_count": 0,
    "forks_count": 0,
    "watchers_count": 1,
    "open_issues_count": 0,
    "open_pr_counter": 0,
    "release_counter": 0,
    "default_branch": "main",
    "archived": false,
    "created_at": "2024-04-30T00:48:19Z",
    "updated_at": "2024-09-11T01:24:20Z",
    "archived_at": "1970-01-01T00:00:00Z",
    "permissions": {
      "admin": true,
      "push": true,
      "pull": true
    },
    "has_issues": true,
    "internal_tracker": {
      "enable_time_tracker": true,
      "allow_only_contributors_to_track_time": true,
      "enable_issue_dependencies": true
    },
    "has_wiki": true,
    "has_pull_requests": true,
    "has_projects": true,
    "projects_mode": "all",
    "has_releases": true,
    "has_packages": false,
    "has_actions": true,
    "ignore_whitespace_conflicts": false,
    "allow_merge_commits": true,
    "allow_rebase": true,
    "allow_rebase_explicit": true,
    "allow_squash_merge": true,
    "allow_fast_forward_only_merge": true,
    "allow_rebase_update": true,
    "allow_manual_merge": false,
    "autodetect_manual_merge": false,
    "default_delete_branch_after_merge": false,
    "default_merge_style": "merge",
    "default_allow_maintainer_edit": false,
    "avatar_url": "",
    "internal": false,
    "mirror_interval": "",
    "object_format_name": "sha1",
    "mirror_updated": "0001-01-01T00:00:00Z",
    "repo_transfer": null,
    "topics": [],
    "licenses": null
  },
  "pusher": {
    "id": 65961,
    "login": "mikeygithub",
    "login_name": "",
    "source_id": 0,
    "full_name": "",
    "email": "<EMAIL>",
    "avatar_url": "https://seccdn.libravatar.org/avatar/f7fc3e04e23200455edc7202ba171036?d=identicon",
    "html_url": "https://gitea.com/mikeygithub",
    "language": "",
    "is_admin": false,
    "last_login": "0001-01-01T00:00:00Z",
    "created": "2024-04-30T00:46:25Z",
    "restricted": false,
    "active": false,
    "prohibit_login": false,
    "location": "",
    "website": "",
    "description": "",
    "visibility": "public",
    "followers_count": 0,
    "following_count": 0,
    "starred_repos_count": 0,
    "username": "mikeygithub"
  },
  "sender": {
    "id": 65961,
    "login": "mikeygithub",
    "login_name": "",
    "source_id": 0,
    "full_name": "",
    "email": "<EMAIL>",
    "avatar_url": "https://seccdn.libravatar.org/avatar/f7fc3e04e23200455edc7202ba171036?d=identicon",
    "html_url": "https://gitea.com/mikeygithub",
    "language": "",
    "is_admin": false,
    "last_login": "0001-01-01T00:00:00Z",
    "created": "2024-04-30T00:46:25Z",
    "restricted": false,
    "active": false,
    "prohibit_login": false,
    "location": "",
    "website": "",
    "description": "",
    "visibility": "public",
    "followers_count": 0,
    "following_count": 0,
    "starred_repos_count": 0,
    "username": "mikeygithub"
  }
}
*/

type GiteaWebhookRequest struct {
	Ref        string `json:"ref"`
	Before     string `json:"before"`
	After      string `json:"after"`
	CompareUrl string `json:"compare_url"`
	Commits    []struct {
		Id      string `json:"id"`
		Message string `json:"message"`
		Url     string `json:"url"`
		Author  struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"author"`
		Committer struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"committer"`
		Verification any       `json:"verification"`
		Timestamp    time.Time `json:"timestamp"`
		Added        []string  `json:"added"`
		Removed      []any     `json:"removed"`
		Modified     []any     `json:"modified"`
	} `json:"commits"`
	TotalCommits int `json:"total_commits"`
	HeadCommit   struct {
		Id      string `json:"id"`
		Message string `json:"message"`
		Url     string `json:"url"`
		Author  struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"author"`
		Committer struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"committer"`
		Verification any       `json:"verification"`
		Timestamp    time.Time `json:"timestamp"`
		Added        []string  `json:"added"`
		Removed      []any     `json:"removed"`
		Modified     []any     `json:"modified"`
	} `json:"head_commit"`
	Repository struct {
		Id    int `json:"id"`
		Owner struct {
			Id                int       `json:"id"`
			Login             string    `json:"login"`
			LoginName         string    `json:"login_name"`
			SourceId          int       `json:"source_id"`
			FullName          string    `json:"full_name"`
			Email             string    `json:"email"`
			AvatarUrl         string    `json:"avatar_url"`
			HtmlUrl           string    `json:"html_url"`
			Language          string    `json:"language"`
			IsAdmin           bool      `json:"is_admin"`
			LastLogin         time.Time `json:"last_login"`
			Created           time.Time `json:"created"`
			Restricted        bool      `json:"restricted"`
			Active            bool      `json:"active"`
			ProhibitLogin     bool      `json:"prohibit_login"`
			Location          string    `json:"location"`
			Website           string    `json:"website"`
			Description       string    `json:"description"`
			Visibility        string    `json:"visibility"`
			FollowersCount    int       `json:"followers_count"`
			FollowingCount    int       `json:"following_count"`
			StarredReposCount int       `json:"starred_repos_count"`
			Username          string    `json:"username"`
		} `json:"owner"`
		Name            string    `json:"name"`
		FullName        string    `json:"full_name"`
		Description     string    `json:"description"`
		Empty           bool      `json:"empty"`
		Private         bool      `json:"private"`
		Fork            bool      `json:"fork"`
		Template        bool      `json:"template"`
		Parent          any       `json:"parent"`
		Mirror          bool      `json:"mirror"`
		Size            int       `json:"size"`
		Language        string    `json:"language"`
		LanguagesUrl    string    `json:"languages_url"`
		HtmlUrl         string    `json:"html_url"`
		Url             string    `json:"url"`
		Link            string    `json:"link"`
		SshUrl          string    `json:"ssh_url"`
		CloneUrl        string    `json:"clone_url"`
		OriginalUrl     string    `json:"original_url"`
		Website         string    `json:"website"`
		StarsCount      int       `json:"stars_count"`
		ForksCount      int       `json:"forks_count"`
		WatchersCount   int       `json:"watchers_count"`
		OpenIssuesCount int       `json:"open_issues_count"`
		OpenPrCounter   int       `json:"open_pr_counter"`
		ReleaseCounter  int       `json:"release_counter"`
		DefaultBranch   string    `json:"default_branch"`
		Archived        bool      `json:"archived"`
		CreatedAt       time.Time `json:"created_at"`
		UpdatedAt       time.Time `json:"updated_at"`
		ArchivedAt      time.Time `json:"archived_at"`
		Permissions     struct {
			Admin bool `json:"admin"`
			Push  bool `json:"push"`
			Pull  bool `json:"pull"`
		} `json:"permissions"`
		HasIssues       bool `json:"has_issues"`
		InternalTracker struct {
			EnableTimeTracker                bool `json:"enable_time_tracker"`
			AllowOnlyContributorsToTrackTime bool `json:"allow_only_contributors_to_track_time"`
			EnableIssueDependencies          bool `json:"enable_issue_dependencies"`
		} `json:"internal_tracker"`
		HasWiki                       bool      `json:"has_wiki"`
		HasPullRequests               bool      `json:"has_pull_requests"`
		HasProjects                   bool      `json:"has_projects"`
		ProjectsMode                  string    `json:"projects_mode"`
		HasReleases                   bool      `json:"has_releases"`
		HasPackages                   bool      `json:"has_packages"`
		HasActions                    bool      `json:"has_actions"`
		IgnoreWhitespaceConflicts     bool      `json:"ignore_whitespace_conflicts"`
		AllowMergeCommits             bool      `json:"allow_merge_commits"`
		AllowRebase                   bool      `json:"allow_rebase"`
		AllowRebaseExplicit           bool      `json:"allow_rebase_explicit"`
		AllowSquashMerge              bool      `json:"allow_squash_merge"`
		AllowFastForwardOnlyMerge     bool      `json:"allow_fast_forward_only_merge"`
		AllowRebaseUpdate             bool      `json:"allow_rebase_update"`
		AllowManualMerge              bool      `json:"allow_manual_merge"`
		AutodetectManualMerge         bool      `json:"autodetect_manual_merge"`
		DefaultDeleteBranchAfterMerge bool      `json:"default_delete_branch_after_merge"`
		DefaultMergeStyle             string    `json:"default_merge_style"`
		DefaultAllowMaintainerEdit    bool      `json:"default_allow_maintainer_edit"`
		AvatarUrl                     string    `json:"avatar_url"`
		Internal                      bool      `json:"internal"`
		MirrorInterval                string    `json:"mirror_interval"`
		ObjectFormatName              string    `json:"object_format_name"`
		MirrorUpdated                 time.Time `json:"mirror_updated"`
		RepoTransfer                  any       `json:"repo_transfer"`
		Topics                        []any     `json:"topics"`
		Licenses                      any       `json:"licenses"`
	} `json:"repository"`
	Pusher struct {
		Id                int       `json:"id"`
		Login             string    `json:"login"`
		LoginName         string    `json:"login_name"`
		SourceId          int       `json:"source_id"`
		FullName          string    `json:"full_name"`
		Email             string    `json:"email"`
		AvatarUrl         string    `json:"avatar_url"`
		HtmlUrl           string    `json:"html_url"`
		Language          string    `json:"language"`
		IsAdmin           bool      `json:"is_admin"`
		LastLogin         time.Time `json:"last_login"`
		Created           time.Time `json:"created"`
		Restricted        bool      `json:"restricted"`
		Active            bool      `json:"active"`
		ProhibitLogin     bool      `json:"prohibit_login"`
		Location          string    `json:"location"`
		Website           string    `json:"website"`
		Description       string    `json:"description"`
		Visibility        string    `json:"visibility"`
		FollowersCount    int       `json:"followers_count"`
		FollowingCount    int       `json:"following_count"`
		StarredReposCount int       `json:"starred_repos_count"`
		Username          string    `json:"username"`
	} `json:"pusher"`
	Sender struct {
		Id                int       `json:"id"`
		Login             string    `json:"login"`
		LoginName         string    `json:"login_name"`
		SourceId          int       `json:"source_id"`
		FullName          string    `json:"full_name"`
		Email             string    `json:"email"`
		AvatarUrl         string    `json:"avatar_url"`
		HtmlUrl           string    `json:"html_url"`
		Language          string    `json:"language"`
		IsAdmin           bool      `json:"is_admin"`
		LastLogin         time.Time `json:"last_login"`
		Created           time.Time `json:"created"`
		Restricted        bool      `json:"restricted"`
		Active            bool      `json:"active"`
		ProhibitLogin     bool      `json:"prohibit_login"`
		Location          string    `json:"location"`
		Website           string    `json:"website"`
		Description       string    `json:"description"`
		Visibility        string    `json:"visibility"`
		FollowersCount    int       `json:"followers_count"`
		FollowingCount    int       `json:"following_count"`
		StarredReposCount int       `json:"starred_repos_count"`
		Username          string    `json:"username"`
	} `json:"sender"`
}

// GiteaWebhookEvent Gitea webhook事件适配器
type GiteaWebhookEvent struct {
	*GiteaWebhookRequest
}

// GetRef 获取引用
func (g *GiteaWebhookEvent) GetRef() string {
	return g.Ref
}

// GetCommitSHA 获取提交SHA
func (g *GiteaWebhookEvent) GetCommitSHA() string {
	return g.After
}

// GetCommits 获取提交列表
func (g *GiteaWebhookEvent) GetCommits() []CommitInfo {
	commits := make([]CommitInfo, len(g.Commits))
	for i, commit := range g.Commits {
		commits[i] = CommitInfo{
			ID:        commit.Id,
			Message:   commit.Message,
			URL:       commit.Url,
			Timestamp: commit.Timestamp,
			Author: struct {
				Name  string
				Email string
			}{
				Name:  commit.Author.Name,
				Email: commit.Author.Email,
			},
		}
	}
	return commits
}

// GetTriggerUser 获取触发用户
func (g *GiteaWebhookEvent) GetTriggerUser() string {
	return g.Pusher.Username
}

// IsPushEvent 是否为推送事件
func (g *GiteaWebhookEvent) IsPushEvent() bool {
	// Gitea推送事件通过ref判断，分支推送以refs/heads/开头
	return strings.HasPrefix(g.Ref, "refs/heads/")
}

// IsTagEvent 是否为标签事件
func (g *GiteaWebhookEvent) IsTagEvent() bool {
	// Gitea标签事件通过ref判断，标签推送以refs/tags/开头
	return strings.HasPrefix(g.Ref, "refs/tags/")
}

// IsValidTrigger 是否为有效触发
func (g *GiteaWebhookEvent) IsValidTrigger() bool {
	// Gitea的after字段为全0表示删除分支/标签，不应该触发
	return g.After != "" && g.After != "0000000000000000000000000000000000000000"
}

func GiteaWebhookHandler(c *gin.Context) {
	var req GiteaWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	// 使用通用的webhook处理函数
	event := &GiteaWebhookEvent{&req}
	HandleWebhookEvent(c, event)
}
