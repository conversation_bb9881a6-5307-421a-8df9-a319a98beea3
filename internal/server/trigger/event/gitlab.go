package event

import (
	"time"

	"github.com/gin-gonic/gin"
	"pipeline/pkg/common/response"
)

type ObjectKind string

const (
	TagPushEvent ObjectKind = "tag_push"
	PushEvent    ObjectKind = "push"
)

func (o ObjectKind) String() string {
	return string(o)
}

type PushEventResponse struct {
	ObjectKind   string `json:"object_kind"` // tag_push、push see:https://docs.gitlab.com/ee/user/project/integrations/webhook_events.html#push-events
	EventName    string `json:"event_name"`
	Before       string `json:"before"`
	After        string `json:"after"`
	Ref          string `json:"ref"` // refs/heads/main
	RefProtected bool   `json:"ref_protected"`
	CheckoutSha  string `json:"checkout_sha"`
	Message      string `json:"message"`
	UserId       int    `json:"user_id"`
	UserName     string `json:"user_name"`
	UserUsername string `json:"user_username"`
	UserEmail    any    `json:"user_email"`
	UserAvatar   string `json:"user_avatar"`
	ProjectId    int    `json:"project_id"`
	Project      struct {
		Id                int    `json:"id"`
		Name              string `json:"name"`
		Description       any    `json:"description"`
		WebUrl            string `json:"web_url"`
		AvatarUrl         any    `json:"avatar_url"`
		GitSshUrl         string `json:"git_ssh_url"`
		GitHttpUrl        string `json:"git_http_url"`
		Namespace         string `json:"namespace"`
		VisibilityLevel   int    `json:"visibility_level"`
		PathWithNamespace string `json:"path_with_namespace"`
		DefaultBranch     string `json:"default_branch"`
		CiConfigPath      any    `json:"ci_config_path"`
		Homepage          string `json:"homepage"`
		Url               string `json:"url"`
		SshUrl            string `json:"ssh_url"`
		HttpUrl           string `json:"http_url"`
	} `json:"project"`
	Commits []struct {
		Id        string    `json:"id"`
		Message   string    `json:"message"`
		Title     string    `json:"title"`
		Timestamp time.Time `json:"timestamp"`
		Url       string    `json:"url"`
		Author    struct {
			Name  string `json:"name"`
			Email string `json:"email"`
		} `json:"author"`
		Added    []any    `json:"added"`
		Modified []string `json:"modified"`
		Removed  []any    `json:"removed"`
	} `json:"commits"`
	TotalCommitsCount int `json:"total_commits_count"`
	PushOptions       struct {
	} `json:"push_options"`
	Repository struct {
		Name            string `json:"name"`
		Url             string `json:"url"`
		Description     any    `json:"description"`
		Homepage        string `json:"homepage"`
		GitHttpUrl      string `json:"git_http_url"`
		GitSshUrl       string `json:"git_ssh_url"`
		VisibilityLevel int    `json:"visibility_level"`
	} `json:"repository"`
}

// GitlabWebhookEvent GitLab webhook事件适配器
type GitlabWebhookEvent struct {
	*PushEventResponse
}

// GetRef 获取引用
func (g *GitlabWebhookEvent) GetRef() string {
	return g.Ref
}

// GetCommitSHA 获取提交SHA
func (g *GitlabWebhookEvent) GetCommitSHA() string {
	return g.CheckoutSha
}

// GetCommits 获取提交列表
func (g *GitlabWebhookEvent) GetCommits() []CommitInfo {
	commits := make([]CommitInfo, len(g.Commits))
	for i, commit := range g.Commits {
		commits[i] = CommitInfo{
			ID:        commit.Id,
			Message:   commit.Message,
			URL:       commit.Url,
			Timestamp: commit.Timestamp,
			Author: struct {
				Name  string
				Email string
			}{
				Name:  commit.Author.Name,
				Email: commit.Author.Email,
			},
		}
	}
	return commits
}

// GetTriggerUser 获取触发用户
func (g *GitlabWebhookEvent) GetTriggerUser() string {
	return g.UserName
}

// IsPushEvent 是否为推送事件
func (g *GitlabWebhookEvent) IsPushEvent() bool {
	return g.ObjectKind == string(PushEvent)
}

// IsTagEvent 是否为标签事件
func (g *GitlabWebhookEvent) IsTagEvent() bool {
	return g.ObjectKind == string(TagPushEvent)
}

// IsValidTrigger 是否为有效触发
func (g *GitlabWebhookEvent) IsValidTrigger() bool {
	// 如果 checkout_sha 为空，则不触发流水线(因为这个webhook是删除分支的时候触发的)
	return g.CheckoutSha != ""
}

func GitlabWebhookHandler(c *gin.Context) {
	var req PushEventResponse
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	// 使用通用的webhook处理函数
	event := &GitlabWebhookEvent{&req}
	HandleWebhookEvent(c, event)
}

// 检查是否触发流水线的执行
//func checkTriggersMatch(push *pd.Push, req PushEventResponse) ([]pd.Option, bool) {
//	switch req.ObjectKind {
//	case string(PushEvent):
//		if opt, ok := checkBranchTriggers(push.Branch, req); ok {
//			return opt, true
//		}
//		if opt, ok := checkCommitTriggers(push.Commit, req); ok {
//			return opt, true
//		}
//	case string(TagPushEvent):
//		if opt, ok := checkTagTriggers(push.Tag, req); ok {
//			return opt, true
//		}
//		if opt, ok := checkCommitTriggers(push.Commit, req); ok {
//			return opt, true
//		}
//	}
//	return nil, false
//}
//
//func checkCommitTriggers(cs pd.Commit, req PushEventResponse) ([]pd.Option, bool) {
//	// commits is a list
//	for _, commit := range req.Commits {
//		cm := commit
//		if checkTriggers(cs.MatchType, cm.Message) {
//			return []pd.Option{
//				pd.WithCheckout(pd.Checkout{
//					Ref:           &req.Ref,
//					CheckoutSHA:   &req.CheckoutSha,
//					CommitMessage: &cm.Message,
//					CommitURL:     &cm.Url,
//				}),
//			}, true
//		}
//	}
//	return nil, false
//}
//
//func checkBranchTriggers(branch pd.Branch, req PushEventResponse) ([]pd.Option, bool) {
//	//"ref":"refs/heads/main",
//	if strings.HasPrefix(req.Ref, "refs/heads/") {
//		Branch := strings.Replace(req.Ref, "refs/heads/", "", 1)
//		if checkTriggers(branch.MatchType, Branch) {
//			// 获取commit message
//			var commitMessage string
//			var commitURL string
//			if len(req.Commits) > 0 {
//				// default use the latest commit message
//				commitMessage = req.Commits[len(req.Commits)-1].Message
//				commitURL = req.Commits[len(req.Commits)-1].Url
//			}
//			return []pd.Option{
//				pd.WithCheckout(pd.Checkout{
//					Ref:           &req.Ref,
//					CheckoutSHA:   &req.CheckoutSha,
//					Branch:        Branch,
//					CommitMessage: &commitMessage,
//					CommitURL:     &commitURL,
//				}),
//			}, true
//		}
//	}
//	return nil, false
//}
//
//func checkTagTriggers(tag pd.Tag, req PushEventResponse) ([]pd.Option, bool) {
//	//"ref":"refs/tags/dev-1.0.0",
//	if strings.HasPrefix(req.Ref, "refs/tags/") {
//		Tag := strings.Replace(req.Ref, "refs/tags/", "", 1)
//		if checkTriggers(tag.MatchType, Tag) {
//			// 获取commit message
//			var commitMessage string
//			var commitURL string
//			if len(req.Commits) > 0 {
//				commitMessage = req.Commits[len(req.Commits)-1].Message
//				commitURL = req.Commits[len(req.Commits)-1].Url
//			}
//			return []pd.Option{
//				pd.WithCheckout(pd.Checkout{
//					Tag:           &Tag,
//					Ref:           &req.Ref,
//					CheckoutSHA:   &req.CheckoutSha,
//					Message:       &req.Message,
//					CommitMessage: &commitMessage,
//					CommitURL:     &commitURL,
//				}),
//			}, true
//		}
//	}
//	return nil, false
//}
