package event

import (
	pd "pipeline/pkg/pipeline"
	"strings"
	"testing"
	"time"
)

func TestCheckTriggers(t *testing.T) {
	tests := []struct {
		name    string
		mt      pd.MatchType
		message string
		want    bool
	}{
		// 精确匹配测试
		{
			name:    "精确匹配成功",
			mt:      pd.MatchType{Precise: []string{"你好"}},
			message: "你好",
			want:    true,
		},
		{
			name:    "精确匹配失败",
			mt:      pd.MatchType{Precise: []string{"你好"}},
			message: "你好啊",
			want:    false,
		},
		{
			name:    "多个精确匹配有一个成功",
			mt:      pd.MatchType{Precise: []string{"你好", "世界"}},
			message: "世界",
			want:    true,
		},

		// 前缀匹配测试
		{
			name:    "前缀匹配成功",
			mt:      pd.MatchType{Prefix: []string{"你好"}},
			message: "你好世界",
			want:    true,
		},
		{
			name:    "前缀匹配失败",
			mt:      pd.MatchType{Prefix: []string{"你好"}},
			message: "大家你好",
			want:    false,
		},

		// 包含匹配测试
		{
			name:    "包含匹配成功",
			mt:      pd.MatchType{Include: []string{"你好"}},
			message: "大家你好世界",
			want:    true,
		},
		{
			name:    "包含匹配失败",
			mt:      pd.MatchType{Include: []string{"你好"}},
			message: "大家好",
			want:    false,
		},

		// 正则表达式匹配测试
		{
			name:    "正则匹配数字成功",
			mt:      pd.MatchType{Regex: []string{"^\\d+$"}},
			message: "12345",
			want:    true,
		},
		{
			name:    "正则匹配数字失败",
			mt:      pd.MatchType{Regex: []string{"^\\d+$"}},
			message: "a12345",
			want:    false,
		},
		{
			name:    "正则匹配邮箱格式成功",
			mt:      pd.MatchType{Regex: []string{"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"}},
			message: "<EMAIL>",
			want:    true,
		},
		{
			name:    "正则匹配邮箱格式失败",
			mt:      pd.MatchType{Regex: []string{"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"}},
			message: "test@example",
			want:    false,
		},
		{
			name:    "正则匹配中文字符成功",
			mt:      pd.MatchType{Regex: []string{"[\u4e00-\u9fa5]+"}},
			message: "你好世界",
			want:    true,
		},
		{
			name:    "正则匹配特定格式成功",
			mt:      pd.MatchType{Regex: []string{"^(GET|POST|PUT|DELETE) /api/v\\d+/.*$"}},
			message: "GET /api/v1/users",
			want:    true,
		},

		// 多类型组合测试
		{
			name: "多种匹配类型组合-精确匹配成功",
			mt: pd.MatchType{
				Precise: []string{"测试消息"},
				Prefix:  []string{"开始"},
				Include: []string{"关键词"},
				Regex:   []string{"^\\d+$"},
			},
			message: "测试消息",
			want:    true,
		},
		{
			name: "多种匹配类型组合-前缀匹配成功",
			mt: pd.MatchType{
				Precise: []string{"测试消息"},
				Prefix:  []string{"开始"},
				Include: []string{"关键词"},
				Regex:   []string{"^\\d+$"},
			},
			message: "开始测试流程",
			want:    true,
		},

		// 空规则测试
		{
			name:    "全部空规则",
			mt:      pd.MatchType{},
			message: "任何消息",
			want:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkTriggers(tt.mt, tt.message); got != tt.want {
				t.Errorf("checkTriggers = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGitlabWebhookEvent(t *testing.T) {
	// 模拟GitLab推送事件
	req := &PushEventResponse{
		ObjectKind:  "push",
		Ref:         "refs/heads/main",
		CheckoutSha: "abc123",
		UserName:    "testuser",
		Commits: []struct {
			Id        string    `json:"id"`
			Message   string    `json:"message"`
			Title     string    `json:"title"`
			Timestamp time.Time `json:"timestamp"`
			Url       string    `json:"url"`
			Author    struct {
				Name  string `json:"name"`
				Email string `json:"email"`
			} `json:"author"`
			Added    []any    `json:"added"`
			Modified []string `json:"modified"`
			Removed  []any    `json:"removed"`
		}{
			{
				Id:      "abc123",
				Message: "test commit",
				Url:     "https://gitlab.com/commit/abc123",
				Author: struct {
					Name  string `json:"name"`
					Email string `json:"email"`
				}{
					Name:  "Test User",
					Email: "<EMAIL>",
				},
			},
		},
	}

	event := &GitlabWebhookEvent{req}

	// 验证接口实现
	if event.GetRef() != "refs/heads/main" {
		t.Errorf("Expected ref 'refs/heads/main', got '%s'", event.GetRef())
	}

	if event.GetCommitSHA() != "abc123" {
		t.Errorf("Expected commit SHA 'abc123', got '%s'", event.GetCommitSHA())
	}

	if !event.IsPushEvent() {
		t.Error("Expected push event to be true")
	}

	if event.IsTagEvent() {
		t.Error("Expected tag event to be false")
	}

	if !event.IsValidTrigger() {
		t.Error("Expected valid trigger to be true")
	}

	if event.GetTriggerUser() != "testuser" {
		t.Errorf("Expected trigger user 'testuser', got '%s'", event.GetTriggerUser())
	}

	commits := event.GetCommits()
	if len(commits) != 1 {
		t.Errorf("Expected 1 commit, got %d", len(commits))
	}

	if commits[0].Message != "test commit" {
		t.Errorf("Expected commit message 'test commit', got '%s'", commits[0].Message)
	}
}

func TestGiteaWebhookEvent(t *testing.T) {
	// 模拟Gitea推送事件
	req := &GiteaWebhookRequest{
		Ref:   "refs/heads/main",
		After: "def456",
		Pusher: struct {
			Id                int       `json:"id"`
			Login             string    `json:"login"`
			LoginName         string    `json:"login_name"`
			SourceId          int       `json:"source_id"`
			FullName          string    `json:"full_name"`
			Email             string    `json:"email"`
			AvatarUrl         string    `json:"avatar_url"`
			HtmlUrl           string    `json:"html_url"`
			Language          string    `json:"language"`
			IsAdmin           bool      `json:"is_admin"`
			LastLogin         time.Time `json:"last_login"`
			Created           time.Time `json:"created"`
			Restricted        bool      `json:"restricted"`
			Active            bool      `json:"active"`
			ProhibitLogin     bool      `json:"prohibit_login"`
			Location          string    `json:"location"`
			Website           string    `json:"website"`
			Description       string    `json:"description"`
			Visibility        string    `json:"visibility"`
			FollowersCount    int       `json:"followers_count"`
			FollowingCount    int       `json:"following_count"`
			StarredReposCount int       `json:"starred_repos_count"`
			Username          string    `json:"username"`
		}{
			Username: "testuser",
		},
	}

	event := &GiteaWebhookEvent{req}

	// 验证接口实现
	if event.GetRef() != "refs/heads/main" {
		t.Errorf("Expected ref 'refs/heads/main', got '%s'", event.GetRef())
	}

	if event.GetCommitSHA() != "def456" {
		t.Errorf("Expected commit SHA 'def456', got '%s'", event.GetCommitSHA())
	}

	if !event.IsPushEvent() {
		t.Error("Expected push event to be true")
	}

	if event.IsTagEvent() {
		t.Error("Expected tag event to be false")
	}

	if !event.IsValidTrigger() {
		t.Error("Expected valid trigger to be true")
	}

	if event.GetTriggerUser() != "testuser" {
		t.Errorf("Expected trigger user 'testuser', got '%s'", event.GetTriggerUser())
	}
}

func TestGithubWebhookEvent(t *testing.T) {
	// 模拟GitHub推送事件
	req := &GithubWebhookRequest{
		Ref:   "refs/heads/main",
		After: "ghi789",
		Pusher: struct {
			Name  string `json:"name"`
			Email string `json:"email"`
		}{
			Name: "testuser",
		},
		Deleted: false,
	}

	event := &GithubWebhookEvent{req}

	// 验证接口实现
	if event.GetRef() != "refs/heads/main" {
		t.Errorf("Expected ref 'refs/heads/main', got '%s'", event.GetRef())
	}

	if event.GetCommitSHA() != "ghi789" {
		t.Errorf("Expected commit SHA 'ghi789', got '%s'", event.GetCommitSHA())
	}

	if !event.IsPushEvent() {
		t.Error("Expected push event to be true")
	}

	if event.IsTagEvent() {
		t.Error("Expected tag event to be false")
	}

	if !event.IsValidTrigger() {
		t.Error("Expected valid trigger to be true")
	}

	if event.GetTriggerUser() != "testuser" {
		t.Errorf("Expected trigger user 'testuser', got '%s'", event.GetTriggerUser())
	}
}

func TestTagEvents(t *testing.T) {
	// 测试标签事件
	tests := []struct {
		name string
		ref  string
		want bool
	}{
		{"GitLab tag", "refs/tags/v1.0.0", true},
		{"GitHub tag", "refs/tags/v2.0.0", true},
		{"Gitea tag", "refs/tags/release-1.0", true},
		{"Branch ref", "refs/heads/main", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试GitLab
			gitlabReq := &PushEventResponse{
				ObjectKind: "tag_push",
				Ref:        tt.ref,
			}
			gitlabEvent := &GitlabWebhookEvent{gitlabReq}

			// 测试Gitea
			giteaReq := &GiteaWebhookRequest{
				Ref: tt.ref,
			}
			giteaEvent := &GiteaWebhookEvent{giteaReq}

			// 测试GitHub
			githubReq := &GithubWebhookRequest{
				Ref: tt.ref,
			}
			githubEvent := &GithubWebhookEvent{githubReq}

			if tt.want {
				if tt.ref == "refs/tags/v1.0.0" && !gitlabEvent.IsTagEvent() {
					t.Error("GitLab: Expected tag event to be true")
				}
				if !giteaEvent.IsTagEvent() && strings.HasPrefix(tt.ref, "refs/tags/") {
					t.Error("Gitea: Expected tag event to be true")
				}
				if !githubEvent.IsTagEvent() && strings.HasPrefix(tt.ref, "refs/tags/") {
					t.Error("GitHub: Expected tag event to be true")
				}
			} else {
				if gitlabEvent.IsTagEvent() && gitlabReq.ObjectKind != "tag_push" {
					t.Error("GitLab: Expected tag event to be false")
				}
				if giteaEvent.IsTagEvent() {
					t.Error("Gitea: Expected tag event to be false")
				}
				if githubEvent.IsTagEvent() {
					t.Error("GitHub: Expected tag event to be false")
				}
			}
		})
	}
}
