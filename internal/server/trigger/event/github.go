package event

import (
	"pipeline/pkg/common/response"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

/*
{
  "ref": "refs/heads/master",
  "before": "947754c29a7b0eca192d158817db443879ecc193",
  "after": "742321f6eeabe7db9613046c66c057de23a69ec0",
  "repository": {
    "id": 693740751,
    "node_id": "R_kgDOKVmkzw",
    "name": "code",
    "full_name": "mikeygithub/code",
    "private": true,
    "owner": {
      "name": "mikeygithub",
      "email": "<EMAIL>",
      "login": "mikeygithub",
      "id": 36464578,
      "node_id": "********************",
      "avatar_url": "https://avatars.githubusercontent.com/u/36464578?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/mikeygithub",
      "html_url": "https://github.com/mikeygithub",
      "followers_url": "https://api.github.com/users/mikeygithub/followers",
      "following_url": "https://api.github.com/users/mikeygithub/following{/other_user}",
      "gists_url": "https://api.github.com/users/mikeygithub/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/mikeygithub/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/mikeygithub/subscriptions",
      "organizations_url": "https://api.github.com/users/mikeygithub/orgs",
      "repos_url": "https://api.github.com/users/mikeygithub/repos",
      "events_url": "https://api.github.com/users/mikeygithub/events{/privacy}",
      "received_events_url": "https://api.github.com/users/mikeygithub/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false
    },
    "html_url": "https://github.com/mikeygithub/code",
    "description": null,
    "fork": false,
    "url": "https://api.github.com/repos/mikeygithub/code",
    "forks_url": "https://api.github.com/repos/mikeygithub/code/forks",
    "keys_url": "https://api.github.com/repos/mikeygithub/code/keys{/key_id}",
    "collaborators_url": "https://api.github.com/repos/mikeygithub/code/collaborators{/collaborator}",
    "teams_url": "https://api.github.com/repos/mikeygithub/code/teams",
    "hooks_url": "https://api.github.com/repos/mikeygithub/code/hooks",
    "issue_events_url": "https://api.github.com/repos/mikeygithub/code/issues/events{/number}",
    "events_url": "https://api.github.com/repos/mikeygithub/code/events",
    "assignees_url": "https://api.github.com/repos/mikeygithub/code/assignees{/user}",
    "branches_url": "https://api.github.com/repos/mikeygithub/code/branches{/branch}",
    "tags_url": "https://api.github.com/repos/mikeygithub/code/tags",
    "blobs_url": "https://api.github.com/repos/mikeygithub/code/git/blobs{/sha}",
    "git_tags_url": "https://api.github.com/repos/mikeygithub/code/git/tags{/sha}",
    "git_refs_url": "https://api.github.com/repos/mikeygithub/code/git/refs{/sha}",
    "trees_url": "https://api.github.com/repos/mikeygithub/code/git/trees{/sha}",
    "statuses_url": "https://api.github.com/repos/mikeygithub/code/statuses/{sha}",
    "languages_url": "https://api.github.com/repos/mikeygithub/code/languages",
    "stargazers_url": "https://api.github.com/repos/mikeygithub/code/stargazers",
    "contributors_url": "https://api.github.com/repos/mikeygithub/code/contributors",
    "subscribers_url": "https://api.github.com/repos/mikeygithub/code/subscribers",
    "subscription_url": "https://api.github.com/repos/mikeygithub/code/subscription",
    "commits_url": "https://api.github.com/repos/mikeygithub/code/commits{/sha}",
    "git_commits_url": "https://api.github.com/repos/mikeygithub/code/git/commits{/sha}",
    "comments_url": "https://api.github.com/repos/mikeygithub/code/comments{/number}",
    "issue_comment_url": "https://api.github.com/repos/mikeygithub/code/issues/comments{/number}",
    "contents_url": "https://api.github.com/repos/mikeygithub/code/contents/{+path}",
    "compare_url": "https://api.github.com/repos/mikeygithub/code/compare/{base}...{head}",
    "merges_url": "https://api.github.com/repos/mikeygithub/code/merges",
    "archive_url": "https://api.github.com/repos/mikeygithub/code/{archive_format}{/ref}",
    "downloads_url": "https://api.github.com/repos/mikeygithub/code/downloads",
    "issues_url": "https://api.github.com/repos/mikeygithub/code/issues{/number}",
    "pulls_url": "https://api.github.com/repos/mikeygithub/code/pulls{/number}",
    "milestones_url": "https://api.github.com/repos/mikeygithub/code/milestones{/number}",
    "notifications_url": "https://api.github.com/repos/mikeygithub/code/notifications{?since,all,participating}",
    "labels_url": "https://api.github.com/repos/mikeygithub/code/labels{/name}",
    "releases_url": "https://api.github.com/repos/mikeygithub/code/releases{/id}",
    "deployments_url": "https://api.github.com/repos/mikeygithub/code/deployments",
    "created_at": 1695139534,
    "updated_at": "2024-10-10T08:53:58Z",
    "pushed_at": 1748839351,
    "git_url": "git://github.com/mikeygithub/code.git",
    "ssh_url": "**************:mikeygithub/code.git",
    "clone_url": "https://github.com/mikeygithub/code.git",
    "svn_url": "https://github.com/mikeygithub/code",
    "homepage": null,
    "size": 53,
    "stargazers_count": 0,
    "watchers_count": 0,
    "language": "Rust",
    "has_issues": true,
    "has_projects": true,
    "has_downloads": true,
    "has_wiki": false,
    "has_pages": false,
    "has_discussions": false,
    "forks_count": 0,
    "mirror_url": null,
    "archived": false,
    "disabled": false,
    "open_issues_count": 0,
    "license": null,
    "allow_forking": true,
    "is_template": false,
    "web_commit_signoff_required": false,
    "topics": [

    ],
    "visibility": "private",
    "forks": 0,
    "open_issues": 0,
    "watchers": 0,
    "default_branch": "master",
    "stargazers": 0,
    "master_branch": "master"
  },
  "pusher": {
    "name": "mikeygithub",
    "email": "<EMAIL>"
  },
  "sender": {
    "login": "mikeygithub",
    "id": 36464578,
    "node_id": "********************",
    "avatar_url": "https://avatars.githubusercontent.com/u/36464578?v=4",
    "gravatar_id": "",
    "url": "https://api.github.com/users/mikeygithub",
    "html_url": "https://github.com/mikeygithub",
    "followers_url": "https://api.github.com/users/mikeygithub/followers",
    "following_url": "https://api.github.com/users/mikeygithub/following{/other_user}",
    "gists_url": "https://api.github.com/users/mikeygithub/gists{/gist_id}",
    "starred_url": "https://api.github.com/users/mikeygithub/starred{/owner}{/repo}",
    "subscriptions_url": "https://api.github.com/users/mikeygithub/subscriptions",
    "organizations_url": "https://api.github.com/users/mikeygithub/orgs",
    "repos_url": "https://api.github.com/users/mikeygithub/repos",
    "events_url": "https://api.github.com/users/mikeygithub/events{/privacy}",
    "received_events_url": "https://api.github.com/users/mikeygithub/received_events",
    "type": "User",
    "user_view_type": "public",
    "site_admin": false
  },
  "created": false,
  "deleted": false,
  "forced": false,
  "base_ref": null,
  "compare": "https://github.com/mikeygithub/code/compare/947754c29a7b...742321f6eeab",
  "commits": [
    {
      "id": "742321f6eeabe7db9613046c66c057de23a69ec0",
      "tree_id": "1ef6c8b98abdff535518c2cdae08ff8f5b4fcecf",
      "distinct": true,
      "message": "Update README.md",
      "timestamp": "2025-06-02T12:42:31+08:00",
      "url": "https://github.com/mikeygithub/code/commit/742321f6eeabe7db9613046c66c057de23a69ec0",
      "author": {
        "name": "麦奇",
        "email": "<EMAIL>",
        "username": "mikeygithub"
      },
      "committer": {
        "name": "GitHub",
        "email": "<EMAIL>",
        "username": "web-flow"
      },
      "added": [

      ],
      "removed": [

      ],
      "modified": [
        "README.md"
      ]
    }
  ],
  "head_commit": {
    "id": "742321f6eeabe7db9613046c66c057de23a69ec0",
    "tree_id": "1ef6c8b98abdff535518c2cdae08ff8f5b4fcecf",
    "distinct": true,
    "message": "Update README.md",
    "timestamp": "2025-06-02T12:42:31+08:00",
    "url": "https://github.com/mikeygithub/code/commit/742321f6eeabe7db9613046c66c057de23a69ec0",
    "author": {
      "name": "麦奇",
      "email": "<EMAIL>",
      "username": "mikeygithub"
    },
    "committer": {
      "name": "GitHub",
      "email": "<EMAIL>",
      "username": "web-flow"
    },
    "added": [

    ],
    "removed": [

    ],
    "modified": [
      "README.md"
    ]
  }
}
*/

type GithubWebhookRequest struct {
	Ref        string `json:"ref"`
	Before     string `json:"before"`
	After      string `json:"after"`
	Repository struct {
		Id       int    `json:"id"`
		NodeId   string `json:"node_id"`
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		Private  bool   `json:"private"`
		Owner    struct {
			Name              string `json:"name"`
			Email             string `json:"email"`
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"owner"`
		HtmlUrl                  string    `json:"html_url"`
		Description              any       `json:"description"`
		Fork                     bool      `json:"fork"`
		Url                      string    `json:"url"`
		ForksUrl                 string    `json:"forks_url"`
		KeysUrl                  string    `json:"keys_url"`
		CollaboratorsUrl         string    `json:"collaborators_url"`
		TeamsUrl                 string    `json:"teams_url"`
		HooksUrl                 string    `json:"hooks_url"`
		IssueEventsUrl           string    `json:"issue_events_url"`
		EventsUrl                string    `json:"events_url"`
		AssigneesUrl             string    `json:"assignees_url"`
		BranchesUrl              string    `json:"branches_url"`
		TagsUrl                  string    `json:"tags_url"`
		BlobsUrl                 string    `json:"blobs_url"`
		GitTagsUrl               string    `json:"git_tags_url"`
		GitRefsUrl               string    `json:"git_refs_url"`
		TreesUrl                 string    `json:"trees_url"`
		StatusesUrl              string    `json:"statuses_url"`
		LanguagesUrl             string    `json:"languages_url"`
		StargazersUrl            string    `json:"stargazers_url"`
		ContributorsUrl          string    `json:"contributors_url"`
		SubscribersUrl           string    `json:"subscribers_url"`
		SubscriptionUrl          string    `json:"subscription_url"`
		CommitsUrl               string    `json:"commits_url"`
		GitCommitsUrl            string    `json:"git_commits_url"`
		CommentsUrl              string    `json:"comments_url"`
		IssueCommentUrl          string    `json:"issue_comment_url"`
		ContentsUrl              string    `json:"contents_url"`
		CompareUrl               string    `json:"compare_url"`
		MergesUrl                string    `json:"merges_url"`
		ArchiveUrl               string    `json:"archive_url"`
		DownloadsUrl             string    `json:"downloads_url"`
		IssuesUrl                string    `json:"issues_url"`
		PullsUrl                 string    `json:"pulls_url"`
		MilestonesUrl            string    `json:"milestones_url"`
		NotificationsUrl         string    `json:"notifications_url"`
		LabelsUrl                string    `json:"labels_url"`
		ReleasesUrl              string    `json:"releases_url"`
		DeploymentsUrl           string    `json:"deployments_url"`
		CreatedAt                int       `json:"created_at"`
		UpdatedAt                time.Time `json:"updated_at"`
		PushedAt                 int       `json:"pushed_at"`
		GitUrl                   string    `json:"git_url"`
		SshUrl                   string    `json:"ssh_url"`
		CloneUrl                 string    `json:"clone_url"`
		SvnUrl                   string    `json:"svn_url"`
		Homepage                 any       `json:"homepage"`
		Size                     int       `json:"size"`
		StargazersCount          int       `json:"stargazers_count"`
		WatchersCount            int       `json:"watchers_count"`
		Language                 string    `json:"language"`
		HasIssues                bool      `json:"has_issues"`
		HasProjects              bool      `json:"has_projects"`
		HasDownloads             bool      `json:"has_downloads"`
		HasWiki                  bool      `json:"has_wiki"`
		HasPages                 bool      `json:"has_pages"`
		HasDiscussions           bool      `json:"has_discussions"`
		ForksCount               int       `json:"forks_count"`
		MirrorUrl                any       `json:"mirror_url"`
		Archived                 bool      `json:"archived"`
		Disabled                 bool      `json:"disabled"`
		OpenIssuesCount          int       `json:"open_issues_count"`
		License                  any       `json:"license"`
		AllowForking             bool      `json:"allow_forking"`
		IsTemplate               bool      `json:"is_template"`
		WebCommitSignoffRequired bool      `json:"web_commit_signoff_required"`
		Topics                   []any     `json:"topics"`
		Visibility               string    `json:"visibility"`
		Forks                    int       `json:"forks"`
		OpenIssues               int       `json:"open_issues"`
		Watchers                 int       `json:"watchers"`
		DefaultBranch            string    `json:"default_branch"`
		Stargazers               int       `json:"stargazers"`
		MasterBranch             string    `json:"master_branch"`
	} `json:"repository"`
	Pusher struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	} `json:"pusher"`
	Sender struct {
		Login             string `json:"login"`
		Id                int    `json:"id"`
		NodeId            string `json:"node_id"`
		AvatarUrl         string `json:"avatar_url"`
		GravatarId        string `json:"gravatar_id"`
		Url               string `json:"url"`
		HtmlUrl           string `json:"html_url"`
		FollowersUrl      string `json:"followers_url"`
		FollowingUrl      string `json:"following_url"`
		GistsUrl          string `json:"gists_url"`
		StarredUrl        string `json:"starred_url"`
		SubscriptionsUrl  string `json:"subscriptions_url"`
		OrganizationsUrl  string `json:"organizations_url"`
		ReposUrl          string `json:"repos_url"`
		EventsUrl         string `json:"events_url"`
		ReceivedEventsUrl string `json:"received_events_url"`
		Type              string `json:"type"`
		UserViewType      string `json:"user_view_type"`
		SiteAdmin         bool   `json:"site_admin"`
	} `json:"sender"`
	Created bool   `json:"created"`
	Deleted bool   `json:"deleted"`
	Forced  bool   `json:"forced"`
	BaseRef any    `json:"base_ref"`
	Compare string `json:"compare"`
	Commits []struct {
		Id        string    `json:"id"`
		TreeId    string    `json:"tree_id"`
		Distinct  bool      `json:"distinct"`
		Message   string    `json:"message"`
		Timestamp time.Time `json:"timestamp"`
		Url       string    `json:"url"`
		Author    struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"author"`
		Committer struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"committer"`
		Added    []any    `json:"added"`
		Removed  []any    `json:"removed"`
		Modified []string `json:"modified"`
	} `json:"commits"`
	HeadCommit struct {
		Id        string    `json:"id"`
		TreeId    string    `json:"tree_id"`
		Distinct  bool      `json:"distinct"`
		Message   string    `json:"message"`
		Timestamp time.Time `json:"timestamp"`
		Url       string    `json:"url"`
		Author    struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"author"`
		Committer struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			Username string `json:"username"`
		} `json:"committer"`
		Added    []any    `json:"added"`
		Removed  []any    `json:"removed"`
		Modified []string `json:"modified"`
	} `json:"head_commit"`
}

// GithubWebhookEvent GitHub webhook事件适配器
type GithubWebhookEvent struct {
	*GithubWebhookRequest
}

// GetRef 获取引用
func (g *GithubWebhookEvent) GetRef() string {
	return g.Ref
}

// GetCommitSHA 获取提交SHA
func (g *GithubWebhookEvent) GetCommitSHA() string {
	return g.After
}

// GetCommits 获取提交列表
func (g *GithubWebhookEvent) GetCommits() []CommitInfo {
	commits := make([]CommitInfo, len(g.Commits))
	for i, commit := range g.Commits {
		commits[i] = CommitInfo{
			ID:        commit.Id,
			Message:   commit.Message,
			URL:       commit.Url,
			Timestamp: commit.Timestamp,
			Author: struct {
				Name  string
				Email string
			}{
				Name:  commit.Author.Name,
				Email: commit.Author.Email,
			},
		}
	}
	return commits
}

// GetTriggerUser 获取触发用户
func (g *GithubWebhookEvent) GetTriggerUser() string {
	return g.Pusher.Name
}

// IsPushEvent 是否为推送事件
func (g *GithubWebhookEvent) IsPushEvent() bool {
	// GitHub推送事件通过ref判断，分支推送以refs/heads/开头
	return strings.HasPrefix(g.Ref, "refs/heads/")
}

// IsTagEvent 是否为标签事件
func (g *GithubWebhookEvent) IsTagEvent() bool {
	// GitHub标签事件通过ref判断，标签推送以refs/tags/开头
	return strings.HasPrefix(g.Ref, "refs/tags/")
}

// IsValidTrigger 是否为有效触发
func (g *GithubWebhookEvent) IsValidTrigger() bool {
	// GitHub的after字段为全0表示删除分支/标签，不应该触发
	// 同时检查deleted字段
	return g.After != "" && g.After != "0000000000000000000000000000000000000000" && !g.Deleted
}

func GithubWebhookHandler(c *gin.Context) {
	var req GithubWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	// 使用通用的webhook处理函数
	event := &GithubWebhookEvent{&req}
	HandleWebhookEvent(c, event)
}
