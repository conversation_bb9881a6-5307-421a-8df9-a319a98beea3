package webhook

import (
	"fmt"
	"pipeline/internal/server/trigger/event"

	"pipeline/internal/server/pipeline"
	"pipeline/pkg/common/response"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	pd "pipeline/pkg/pipeline"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"github.com/gin-gonic/gin"
)

// extractOptionsFromParams 从参数中提取特殊选项
func extractOptionsFromParams(options *[]pd.Option, params map[string]any) {
	// 检查是否指定了分支
	if branchVal, ok := params["branch"]; ok {
		if branch, isStr := branchVal.(string); isStr && branch != "" {
			*options = append(*options, pd.WithBranch(branch))
		}
	}
	// 检查是否指定了标签
	if tagVal, ok := params["tag"]; ok {
		if tag, isStr := tagVal.(string); isStr && tag != "" {
			*options = append(*options, pd.WithTag(tag))
		}
	}
}

func GetTriggerWebhookHandler(c *gin.Context) {
	pipelineUUID := c.Param("uuid")
	// 检查是否有匹配的条件进行触发流水线
	if pipelineUUID == "" {
		response.Err(c, response.RequestParamError("pipeline uuid is required"))
		return
	}
	// 检查该 app 下的所有流水线 是否有匹配的条件进行触发流水线
	pipelineModel, err := pipeline.NewInfoService(nil, mysql.GetDB(),
		redis.GetClient()).GetByUUID(pipelineUUID)
	if err != nil {
		errors.ResponseError(c, err)
		log.Error(err.Error())
		return
	}
	if pipelineModel.Deleted {
		errors.ResponseError(c, fmt.Errorf("pipeline is deleted"))
		return
	}
	// pipeline must enable webhook trigger
	parsePipeline, err := pd.ParsePipeline(pipelineModel.Content)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	if parsePipeline.Triggers.Webhook == nil || !*parsePipeline.Triggers.Webhook {
		errors.ResponseError(c, fmt.Errorf("pipeline %s trigger way is not webhook", pipelineUUID))
		return
	}
	// 解析请求参数成为map[string]any, 也可能没数据
	paramMap := make(map[string]any)
	for k, v := range c.Request.URL.Query() {
		if len(v) > 0 {
			paramMap[k] = v[0] // 或者尝试解析为 int/float 之类
		}
	}
	// 执行流水线
	txUUID := util.UUID()
	baseOptions := []pd.Option{
		pd.WithTxUUID(txUUID),
		pd.WithTriggerWay(pd.Webhook),
		pd.WithTriggerUser(pd.Webhook),
		pd.WithWebhookTriggerParam(paramMap),
		pd.WithPipelineUUID(pipelineModel.UUID),
	}
	// 从参数中检查是否指定了相关options
	extractOptionsFromParams(&baseOptions, paramMap)
	// 执行流水线
	err = eventbus.Publish(c, &events.RunPipelineEvent{
		Opts: pd.NewPipelineContextOption(baseOptions...),
	})
	response.OK(c, event.TriggeredResult{
		TriggeredPipelines: []event.TriggeredPipeline{
			{
				UUID:   pipelineModel.UUID,
				Name:   pipelineModel.Name,
				TxUUID: txUUID,
			},
		},
		Errors: []error{err},
	})
}

func PostTriggerWebhookHandler(c *gin.Context) {

	pipelineUUID := c.Param("uuid")
	// 检查是否有匹配的条件进行触发流水线
	if pipelineUUID == "" {
		response.Err(c, response.RequestParamError("pipeline uuid is required"))
		return
	}
	// 解析请求体成为map[string]any, 也可能没数据
	var param map[string]any
	if err := c.BindJSON(&param); err != nil {
		log.Printf("webhook trigger bind json error: %v", err)
	}
	// 检查该 app 下的所有流水线 是否有匹配的条件进行触发流水线
	pipelineModel, err := pipeline.NewInfoService(nil, mysql.GetDB(),
		redis.GetClient()).GetByUUID(pipelineUUID)
	if err != nil {
		errors.ResponseError(c, err)
		log.Error(err.Error())
		return
	}
	if pipelineModel.Deleted {
		errors.ResponseError(c, fmt.Errorf("pipeline is deleted"))
		return
	}
	// pipeline must enable webhook trigger
	parsePipeline, err := pd.ParsePipeline(pipelineModel.Content)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	if parsePipeline.Triggers.Webhook == nil || !*parsePipeline.Triggers.Webhook {
		errors.ResponseError(c, fmt.Errorf("pipeline %s trigger way is not webhook", pipelineUUID))
		return
	}
	// 执行流水线
	txUUID := util.UUID()
	baseOptions := []pd.Option{
		pd.WithTxUUID(txUUID),
		pd.WithTriggerWay(pd.Webhook),
		pd.WithTriggerUser(pd.Webhook),
		pd.WithWebhookTriggerParam(param),
		pd.WithPipelineUUID(pipelineModel.UUID),
	}
	// 从参数中检查是否指定了相关options
	extractOptionsFromParams(&baseOptions, param)
	// 初始化连接状态
	err = eventbus.Publish(c, &events.RunPipelineEvent{
		Opts: pd.NewPipelineContextOption(baseOptions...),
	})
	// 返回触发结果
	response.OK(c, event.TriggeredResult{
		TriggeredPipelines: []event.TriggeredPipeline{
			{
				UUID:   pipelineModel.UUID,
				Name:   pipelineModel.Name,
				TxUUID: txUUID,
			},
		},
		Errors: []error{err},
	})
}
