package credential

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/pipeline/credential")
	{
		cAPI.Use(middleware.AuthIsAdmin(), middleware.AuthUToken())
		cAPI.POST("/info", Info)
		cAPI.POST("/page", Page)
		cAPI.POST("/add", Add)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
		// for select level in the front end
		cAPI.GET("/levels", Levels)
		// for select types of management in the front end
		cAPI.GET("/types", Types)
		// for select list in the step dynamic form
		cAPI.POST("/select", Select)
		// update encrypted value, refresh the encrypted value
		cAPI.PUT("/encrypt", middleware.AuthApiToken(), Encrypt)
	}
}
