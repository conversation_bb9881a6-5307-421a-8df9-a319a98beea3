package credential

import (
	"context"
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"pipeline/internal/server/secret"
	"pipeline/internal/server/source"
	"pipeline/internal/server/system/user"
	"pipeline/pkg/common"
	"pipeline/pkg/common/model"
	"pipeline/pkg/common/request"
	"pipeline/pkg/models"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"
)

type credentialService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewCredentialService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *credentialService {
	svc := &credentialService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

// Info 获取凭证详情
func (s *credentialService) Info(uuid string) (*InfoResponse, error) {
	repo := NewCredentialRepo(s.db)
	m, authUsers, err := repo.Info(uuid)
	if err != nil {
		return nil, err
	}
	if !*m.Show {
		m.Value = util.HideValue
	} else {
		// decrypt value
		if m.Value, err = common.DefaultDecrypt(m.Value); err != nil {
			return nil, err
		}
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	//  获取用户信息
	credentialCategory, err := repo.GetPipelineCredentialCategoryByUUID(m.CategoryUUID)
	if err != nil {
		return nil, err
	}
	info.Scope = credentialCategory.Scope
	info.Example = credentialCategory.Example
	principals, member := make([]RelationShipResponse, 0), make([]RelationShipResponse, 0)
	info.Members, info.Principals = member, principals
	if len(authUsers) > 0 {
		// grpc 获取用户信息
		emails := make([]string, 0)
		for _, r := range authUsers {
			emails = append(emails, r.UserUUID)
		}
		accounts, err := user.NewUserService(nil, s.db, s.redisClient).Page(user.PageRequest{
			Emails: emails,
			PageRequest: request.PageRequest{
				PageSize: model.MaxPageSize,
			},
		})
		if err != nil {
			return nil, err
		}
		accountsMap := make(map[string]models.User)
		info.Members = make([]RelationShipResponse, 0)
		info.Principals = make([]RelationShipResponse, 0)
		for _, item := range accounts.List {
			accountsMap[item.Email] = item
		}
		// 封装 用户对象
		for _, item := range authUsers {
			if user, ok := accountsMap[item.UserUUID]; ok {
				relation := RelationShipResponse{
					Name: user.Name,
					Uuid: user.Email,
				}
				if item.Role == PRINCIPAL {
					relation.TypeOf = PRINCIPAL
					info.Principals = append(info.Principals, relation)
				} else {
					relation.TypeOf = MEMBER
					info.Members = append(info.Members, relation)
				}
			}
		}
	}
	return &info, nil
}

// Add 创建 凭证、凭证&用户权限关系
func (s *credentialService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewCredentialRepo(s.db)
	category, err := repo.GetPipelineCredentialCategoryByUUID(r.CategoryUUID)
	if err != nil {
		return 0, err
	}
	// 校验添加的凭证至少有一个管理者
	if len(r.Principals) == 0 {
		return 0, fmt.Errorf("at least one principal is required")
	}
	// 创建凭证
	var pc PipelineCredential
	err = copier.Copy(&pc, &r)
	pc.Create(ops)
	pc.TypeOf = category.Category
	if err != nil {
		return 0, err
	}
	//  创建 凭证 用户权限关系
	var authUsers = make([]*PipelineCredentialAuthUser, 0)
	for _, item := range r.Principals {
		var authUser PipelineCredentialAuthUser
		// 创建 base model
		authUser.Create(ops)
		authUser.UserUUID = item
		authUser.CredentialUUID = pc.UUID
		authUser.Role = PRINCIPAL
		authUsers = append(authUsers, &authUser)
	}
	// 普通成员(仅有使用权限)
	for _, item := range r.Members {
		var authUser PipelineCredentialAuthUser
		authUser.Create(ops)
		authUser.UserUUID = item
		authUser.CredentialUUID = pc.UUID
		authUser.Role = MEMBER
		authUsers = append(authUsers, &authUser)
	}
	// 加密凭证
	if err = s.EncryptCredential(&pc); err != nil {
		return 0, err
	}
	return repo.Add(&pc, authUsers)
}

// Delete 删除凭证
func (s *credentialService) Delete(ids []string, ops string) error {
	repo := NewCredentialRepo(s.db)
	return repo.Delete(ids)
}

// Update 更新凭证
func (s *credentialService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewCredentialRepo(s.db)
	// 获取凭证详情
	m, authUsers, err := repo.Info(r.UUID)
	if err != nil {
		return 0, err
	}
	// 内容的隐藏只允许从显示到隐藏,不允许从隐藏到显示
	if r.Show != nil && *r.Show && m.Show != nil && !*m.Show {
		r.Show = nil
	}
	// 如果未修改保存旧的 value
	oldValue := m.Value
	err = copier.Copy(&m, &r)
	// no need to update value if it's hidden
	if r.Value == util.HideValue {
		m.Value = oldValue
	}
	if len(r.Principals) == 0 {
		return 0, fmt.Errorf("at least one principal is required")
	}
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	// 获取 旧的 凭证用户权限关系
	principalsMap := make(map[string]*PipelineCredentialAuthUser)
	membersMap := make(map[string]*PipelineCredentialAuthUser)
	for _, item := range authUsers {
		if item.Role == PRINCIPAL {
			principalsMap[item.UserUUID] = item
		}
		if item.Role == MEMBER {
			membersMap[item.UserUUID] = item
		}
	}
	addAuthUsers := make([]*PipelineCredentialAuthUser, 0)
	// 普通成员
	for _, member := range r.Members {
		// 忽略已经存在的成员
		if _, ok := membersMap[member]; ok {
			delete(membersMap, member)
			continue
		}
		// 新增的成员
		newMember := &PipelineCredentialAuthUser{
			UserUUID:       member,
			Role:           MEMBER,
			CredentialUUID: m.UUID,
		}
		// 新建 base model
		newMember.Create(ops)
		addAuthUsers = append(addAuthUsers, newMember)
	}
	// 管理员
	for _, principal := range r.Principals {
		// 忽略已经存在的管理员
		if _, ok := principalsMap[principal]; ok {
			delete(principalsMap, principal)
			continue
		}
		// 新增的管理员
		newPrincipal := &PipelineCredentialAuthUser{
			UserUUID:       principal,
			Role:           PRINCIPAL,
			CredentialUUID: m.UUID,
		}
		// 新建 base model
		newPrincipal.Create(ops)
		addAuthUsers = append(addAuthUsers, newPrincipal)
	}
	// 新增的凭证用户权限关系
	deleteIds := make([]int64, 0)
	for _, item := range principalsMap {
		deleteIds = append(deleteIds, item.ID)
	}
	// 删除的凭证用户权限关系
	for _, item := range membersMap {
		deleteIds = append(deleteIds, item.ID)
	}
	// 加密凭证
	if err = s.EncryptCredential(m); err != nil {
		return 0, err
	}
	return repo.Update(m, addAuthUsers, deleteIds)
}

// Page 分页查询凭证列表
func (s *credentialService) Page(r PageRequest, ops string, isAdmin bool) (*response.PageModelV2[*PageItemResponse], error) {
	repo := NewCredentialRepo(s.db)
	page, err := repo.Page(r, ops, isAdmin)
	if err != nil {
		return nil, err
	}
	// 隐藏 value
	for i, v := range page.List {
		if v.Show == nil || !*v.Show {
			page.List[i].Value = util.HideValue
		} else {
			// decrypt value
			if page.List[i].Value, err = common.DefaultDecrypt(v.Value); err != nil {
				return nil, err
			}
		}
	}
	return &page, nil
}

// Types 查询凭证类型
func (s *credentialService) Types(isAdmin bool) ([]*TypeResponse, error) {
	repo := NewCredentialRepo(s.db)
	var res = make([]*TypeResponse, 0)
	levels := []int{NORMAL}
	if isAdmin {
		levels = append(levels, SYSTEM)
	}

	categories, err := repo.GetPipelineCredentialCategoryByScope(levels)
	if err != nil {
		return nil, err
	}

	for _, item := range categories {
		res = append(res, &TypeResponse{
			UUID:         item.UUID,
			TypeOf:       item.Category,
			Scope:        item.Scope,
			CategoryName: item.CategoryName,
			Example:      item.Example,
		})
	}

	return res, nil
}

// Select 查询凭证下拉框
func (s *credentialService) Select(r SelectRequest, ops string, isAdmin bool) (results []*SelectResponse, err error) {
	repo := NewCredentialRepo(s.db)
	// query list
	list, err := repo.List(ListRequest{
		CategoryUUID: r.CategoryUUID,
		TypeOf:       r.Type,
		PageRequest:  r.PageRequest,
	}, ops, isAdmin)
	if err != nil {
		return nil, err
	}
	// convert to response
	ret := make([]*SelectResponse, 0)
	for _, credential := range list {
		v := &SelectResponse{
			Label: credential.Name,
			Value: credential.UUID,
		}
		// hide value if it's hidden
		if r.Value == Value && credential.Show != nil && *credential.Show {
			// decrypt value
			if v.Value, err = common.DefaultDecrypt(credential.Value); err != nil {
				return nil, err
			}
		}
		ret = append(ret, v)
	}
	return ret, nil
}

// GetLevel 获取凭证级别
func (s *credentialService) GetLevel(isAdmin bool) (results []*CredentialLevelResponse, err error) {
	res := []*CredentialLevelResponse{
		{
			Label: "用户凭证",
			Scope: NORMAL,
		},
	}
	if isAdmin {
		res = append(res, &CredentialLevelResponse{
			Label: "系统凭证",
			Scope: SYSTEM,
		})
	}
	return res, nil
}

// EncryptCredential 对凭证进行加密
func (s *credentialService) EncryptCredential(certs ...*PipelineCredential) error {
	for i, item := range certs {
		encrypt, err := common.DefaultEncrypt(item.Value)
		if err != nil {
			return err
		}
		certs[i].Value = encrypt
	}
	return nil
}

// DecryptCredential 对凭证进行解密
func (s *credentialService) DecryptCredential(certs ...*PipelineCredential) error {
	for i, item := range certs {
		decrypt, err := common.DefaultDecrypt(item.Value)
		if err != nil {
			return err
		}
		certs[i].Value = decrypt
	}
	return nil
}

// Encrypt 更新加密凭证
func (s *credentialService) Encrypt(req EncryptRequest, name string) error {
	log.Info("encrypt request", log.Any("name", name))
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 更新凭证
		if err := model.Range[PipelineCredential](tx, 100, func(certs []*PipelineCredential) error {
			for _, item := range certs {
				var value = item.Value
				// 需要解密
				if !req.SkipDecrypt {
					var err error
					value, err = common.DefaultDecrypt(item.Value)
					if err != nil {
						return err
					}
				}
				encrypt, err := common.DefaultEncrypt(value)
				if err != nil {
					return err
				}
				item.Value = encrypt
			}
			// 更新凭证
			if err := tx.Save(certs).Error; err != nil {
				return err
			}
			return nil
		}); err != nil {
			return err
		}
		// 更新秘钥
		if err := model.Range[secret.PipelineSecret](tx, 100, func(certs []*secret.PipelineSecret) error {
			for _, item := range certs {
				var value = item.Value
				// 需要解密
				if !req.SkipDecrypt {
					var err error
					value, err = common.DefaultDecrypt(item.Value)
					if err != nil {
						return err
					}
				}
				encrypt, err := common.DefaultEncrypt(value)
				if err != nil {
					return err
				}
				item.Value = encrypt
			}
			// 更新凭证
			if err := tx.Save(certs).Error; err != nil {
				return err
			}
			return nil
		}); err != nil {
			return err
		}
		// 更新代码源
		return model.Range[source.ProjectAppCodeSource](tx, 100, func(certs []*source.ProjectAppCodeSource) error {
			for _, item := range certs {
				var value = item.AccessToken
				// 需要解密
				if !req.SkipDecrypt {
					var err error
					value, err = common.DefaultDecrypt(item.AccessToken)
					if err != nil {
						return err
					}
				}
				encrypt, err := common.DefaultEncrypt(value)
				if err != nil {
					return err
				}
				item.AccessToken = encrypt
			}
			// 更新凭证
			if err := tx.Save(certs).Error; err != nil {
				return err
			}
			return nil
		})
	})
}
