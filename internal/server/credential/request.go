package credential

import (
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
	// typeOf 后续不用
	TypeOf       string `json:"typeOf"`       //  类型
	CategoryUUID string `json:"categoryUUID"` //  类别ID
	//
	UUID []string
	//
	Decrypt        bool `json:"-"`
	ExcludeDeleted bool `json:"-"`
}

type InfoRequest struct {
	UUID    string
	Decrypt bool
}

type PageRequest struct {
	request.PageRequest
	Name  string `json:"name"`  //  环境变量名
	Value string `json:"value"` //  环境变量值
	// typeOf 后续不用
	TypeOf       string `json:"typeOf"`       //  类型
	CategoryUUID string `json:"categoryUUID"` //  类别
	Scope        int    `json:"scope"`        //  凭证级别(0:system|1:simple)
}

type AddRequest struct {
	Name         string   `json:"name"`   //  环境变量名
	Value        string   `json:"value"`  //  环境变量值
	TypeOf       string   `json:"typeOf"` //  类型
	Show         *bool    `json:"show"`
	Scope        int      `json:"scope"`        //  凭证级别(0:system|1:simple)
	CategoryUUID string   `json:"categoryUUID"` //  类别
	Principals   []string `json:"principals"`   //credential 创建者（管理者）uuid
	Members      []string `json:"members"`      //credential 共享者 uuid
	Remark       string   `json:"remark"`       //  描述
}

type UpdateRequest struct {
	UUID         string   `json:"uuid" form:"uuid" binding:"required"`
	Name         string   `json:"name"`   //  环境变量名
	Value        string   `json:"value"`  //  环境变量值
	TypeOf       string   `json:"typeOf"` //  类型
	Show         *bool    `json:"show"`
	CategoryUUID string   `json:"categoryUUID"` //  类别
	Principals   []string //credential 创建者（管理者）uuid
	Members      []string //credential 共享者 uuid
	Remark       string   `json:"remark"` //  描述
}

type SelectRequest struct {
	request.PageRequest
	CategoryUUID string    `json:"categoryUUID"` //  类别ID
	Type         string    `json:"type"`         //  类型
	Value        ValueType `json:"value"`        //  select的value类型：uuid或者直接是value（如果是value类型则必须是show状态的）
}

type ValueType string

const (
	UUID  ValueType = "uuid"
	Value ValueType = "value"
)

// 凭证级别
const (
	SYSTEM int = iota
	NORMAL
)

const (
	PRINCIPAL string = "principal"
	MEMBER    string = "member"
)

type EncryptRequest struct {
	Key         string `json:"key" binding:"required"`
	SkipDecrypt bool   `json:"skipDecrypt"`
}
