package credential

import (
	"gorm.io/gorm"
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"
)

type credentialRepo struct {
	db *gorm.DB
}

func NewCredentialRepo(db *gorm.DB) *credentialRepo {
	return &credentialRepo{db: db}
}

// Info 获取凭证详情
func (repo *credentialRepo) Info(uuid string) (*PipelineCredential, []*PipelineCredentialAuthUser, error) {
	var result PipelineCredential
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, nil, err
	}
	var authUsers = make([]*PipelineCredentialAuthUser, 0)
	if err := repo.db.Where("credential_uuid = ?", result.UUID).Find(&authUsers).Error; err != nil {
		return nil, nil, err
	}
	return &result, authUsers, nil
}

// GetByUUID 通过 uuid 获取凭证详情
func (repo *credentialRepo) GetByUUID(req InfoRequest) (*PipelineCredential, error) {
	var result PipelineCredential
	if err := repo.db.Where("uuid = ?", req.UUID).First(&result).Error; err != nil {
		return nil, err
	}
	// decrypt
	if req.Decrypt {
		value, err := common.DefaultDecrypt(result.Value)
		if err != nil {
			return nil, err
		}
		result.Value = value
	}
	return &result, nil
}

// Add 通过事务创建 凭证、凭证&用户权限关系
func (repo *credentialRepo) Add(pc *PipelineCredential, authUsers []*PipelineCredentialAuthUser) (id int64, err error) {
	tx := repo.db.Begin()
	if err = tx.Create(pc).Error; err != nil {
		tx.Rollback()
		return -1, err
	}
	if len(authUsers) > 0 {
		if err = tx.CreateInBatches(authUsers, len(authUsers)).Error; err != nil {
			tx.Rollback()
			return -1, err
		}
	}
	tx.Commit()
	return pc.ID, err
}

// Delete 删除凭证 & 凭证&用户权限关系
func (repo *credentialRepo) Delete(ids []string) error {
	tx := repo.db.Begin()
	if err := tx.Model(&PipelineCredential{}).Where("uuid in (?)", ids).Update("deleted", true).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Model(&PipelineCredentialAuthUser{}).Where("credential_uuid in (?)", ids).Update("deleted", true).Error; err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

// Update 通过事务更新 凭证、凭证&用户权限关系
func (repo *credentialRepo) Update(m *PipelineCredential, authUsers []*PipelineCredentialAuthUser, delIds []int64) (rowsAffected int64, err error) {
	tx := repo.db.Begin()
	// 更新凭证
	updateAffectedDB := tx.Model(PipelineCredential{}).Where("id = ?", m.ID).Updates(&m)
	if updateAffectedDB.Error != nil {
		tx.Rollback()
		return 0, updateAffectedDB.Error
	}
	// 增加 凭证&用户权限关系
	if len(authUsers) > 0 {
		if err = tx.CreateInBatches(authUsers, len(authUsers)).Error; err != nil {
			tx.Rollback()
			return 0, err
		}
	}
	// 删除 凭证&用户权限关系
	if len(delIds) > 0 {
		if err = tx.Where("id in (?)", delIds).Delete(&PipelineCredentialAuthUser{}).Error; err != nil {
			tx.Rollback()
			return 0, err
		}
	}
	tx.Commit()
	return updateAffectedDB.RowsAffected, nil
}

// Page 分页查询凭证
func (repo *credentialRepo) Page(r PageRequest, ops string, isAdmin bool) (pm response.PageModelV2[*PageItemResponse], err error) {
	// 查询凭证
	query := repo.db.Table("pipeline_credential pc")
	// 管理员查询所有凭证
	if isAdmin {
		query = query.Select("distinct pc.*,pcc.category, pcc.scope, pcc.example, 'principal' as role").
			Joins("join pipeline_credential_categories pcc on pc.category_uuid = pcc.uuid")
	} else {
		query = query.
			Select("distinct pc.*,pcc.category, pcc.scope, pcc.example, pcou.role").
			Joins("join pipeline_credential_categories pcc on pc.category_uuid = pcc.uuid").
			Joins("left join pipeline_credential_of_user pcou on pcou.user_uuid = ? and pc.uuid = pcou.credential_uuid", ops).
			Where("pcou.user_uuid = ?", ops)
	}

	if r.Scope >= 0 {
		query = query.Where("pcc.scope = ?", r.Scope)
	}

	if r.Name != "" {
		query = query.Where("pc.name like ?", util.Like(r.Name))
	}

	if r.CategoryUUID != "" {
		query = query.Where("pc.category_uuid = ?", r.CategoryUUID)
	}

	query = query.Where("pc.deleted = false")

	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*PageItemResponse
	if err = query.Order("pc.gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

// GetByUUIDList 查询凭证列表(用于builder获取凭证)
func (repo *credentialRepo) GetByUUIDList(r ListRequest) (list []*PipelineCredential, err error) {
	if len(r.UUID) == 0 {
		return nil, nil
	}
	query := repo.db
	if len(r.UUID) > 0 {
		query = query.Where("uuid in (?)", r.UUID)
	}
	if r.ExcludeDeleted {
		query = query.Where("deleted = false")
	}
	if err = query.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	// decrypt
	if r.Decrypt {
		for _, item := range list {
			value, decryptErr := common.DefaultDecrypt(item.Value)
			if decryptErr != nil {
				return nil, decryptErr
			}
			item.Value = value
		}
	}
	return list, nil
}

// List 查询凭证列表(用于下拉框选择)
func (repo *credentialRepo) List(r ListRequest, opt string, isAdmin bool) (list []*PipelineCredential, err error) {
	query := repo.db

	if isAdmin {
		query = query.Table("pipeline_credential pc").Select("pc.*")
	} else {
		query = query.Table("pipeline_credential pc").
			Select("DISTINCT pc.*").
			Joins("left join pipeline_credential_categories pcc on pc.category_uuid = pcc.uuid").
			Joins("left join pipeline_credential_of_user pcou on pc.uuid = pcou.credential_uuid")
	}

	// 因为系统凭证是需要提供给所有用户选择, 这里如果是普通用户, 需要加上 scope = 0 的条件
	if !isAdmin {
		query = query.Where("(user_uuid = ? or pcc.scope = 0 )", opt)
	}

	if r.TypeOf != "" {
		query = query.Where("type_of = ?", r.TypeOf)
	}

	if r.CategoryUUID != "" {
		query = query.Where("category_uuid = ?", r.CategoryUUID)
	}

	if len(r.UUID) > 0 {
		query = query.Where("uuid in (?)", r.UUID)
	}

	query = query.Where("pc.deleted = false")

	if err := query.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// GetPipelineCredentialCategoryByScope 通过 scope 获取凭证类型
func (repo *credentialRepo) GetPipelineCredentialCategoryByScope(scopes []int) ([]*PipelineCredentialCategory, error) {
	var list []*PipelineCredentialCategory
	if err := repo.db.Where("scope in (?)", scopes).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// GetPipelineCredentialCategoryByUUID 通过 uuid 获取凭证类型
func (repo *credentialRepo) GetPipelineCredentialCategoryByUUID(uuid string) (*PipelineCredentialCategory, error) {
	var res *PipelineCredentialCategory
	if err := repo.db.Where("uuid = ?", uuid).First(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}
