package credential

import "time"

type InfoResponse struct {
	Id           int64                  `json:"id"`    //  主键
	Uuid         string                 `json:"uuid"`  //  UUID
	Name         string                 `json:"name"`  //  环境变量名
	Value        string                 `json:"value"` //  环境变量值
	Show         *bool                  `json:"show"`
	TypeOf       string                 `json:"typeOf"`       //  类型
	Remark       string                 `json:"remark"`       //  备注
	Creator      string                 `json:"creator"`      //  创建人
	Modifier     string                 `json:"modifier"`     //  修改人
	GmtCreate    time.Time              `json:"gmtCreate"`    //  创建时间
	GmtModified  time.Time              `json:"gmtModified"`  //  修改时间
	Deleted      int                    `json:"deleted"`      //  逻辑删除
	Scope        int                    `json:"scope"`        //  凭证级别(0:system|1:simple)
	Principals   []RelationShipResponse `json:"principals"`   //credential 创建者（管理者）uuid
	Members      []RelationShipResponse `json:"members"`      //credential 共享者 uuid
	CategoryUUID string                 `json:"categoryUUID"` //  类别
	Example      string                 `json:"example"`      //  示例
}

type RelationShipResponse struct {
	Name   string `json:"label"`  // 唯一标识名称
	TypeOf string `json:"typeOf"` // 角色类型
	Uuid   string `json:"value"`  // 唯一标识
}

type ListResponse struct {
	Uuid  string `json:"uuid"` //  UUID
	Name  string `json:"name"` //  环境变量名
	Label string `json:"label"`
	Value string `json:"value"` //  环境变量值
}

type PageItemResponse struct {
	Id           int    `json:"id"`
	Uuid         string `json:"uuid"`
	Creator      string `json:"creator"`
	Modifier     string `json:"modifier"`
	Remark       string `json:"remark"`
	GmtCreate    string `json:"gmtCreate"`
	GmtModified  string `json:"gmtModified"`
	Deleted      int    `json:"deleted"`
	Name         string `json:"name"`
	Value        string `json:"value"`
	TypeOf       string `json:"typeOf"`
	Show         *bool  `json:"show"`
	Scope        int    `json:"scope"`
	CategoryUuid string `json:"categoryUUID"`
	Role         string `json:"role" gorm:"column:role" db:"role"` // principal or member
	Example      string `json:"example"  gorm:"column:example" db:"example"`
}

type SelectResponse struct {
	Label string `json:"label"`
	Value string `json:"value"` //  环境变量值
}

type TypeResponse struct {
	UUID         string `json:"uuid"`                     //  UUID
	TypeOf       string `json:"typeOf" copier:"Category"` // 角色类型
	Scope        int    `json:"scope"`                    //  凭证级别(0:system|1:simple)
	CategoryName string `json:"categoryName"`             //  类别名称
	Example      string `json:"example"  gorm:"column:example" db:"example"`
}

type CredentialLevelResponse struct {
	Scope int    `json:"value"` //  凭证级别(0:system|1:simple)
	Label string `json:"label"`
}
