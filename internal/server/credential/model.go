package credential

import "pipeline/pkg/common/model"

// PipelineCredential  pipeline_credential
type PipelineCredential struct {
	model.BaseModel
	Name         string `gorm:"column:name" db:"name" json:"name"`                                                 //  环境变量名
	Value        string `gorm:"column:value" db:"value" json:"value"`                                              //  环境变量值
	TypeOf       string `gorm:"column:type_of" db:"type_of" json:"typeOf"`                                         //  类型
	Show         *bool  `gorm:"column:show" db:"show" json:"show"`                                                 //  是否展示值
	CategoryUUID string `json:"categoryUUID" gorm:"column:category_uuid" db:"category_uuid" copier:"CategoryUUID"` //  类别
}

func (PipelineCredential) TableName() string {
	return "pipeline_credential"
}

type PipelineCredentialAuthUser struct {
	model.BaseModel
	CredentialUUID string `gorm:"column:credential_uuid" db:"credential_uuid" json:"credential_uuid"` //  证书ID
	UserUUID       string `gorm:"column:user_uuid" db:"user_uuid" json:"user_uuid"`                   //  用户ID
	Role           string `gorm:"column:role" db:"role" json:"role"`                                  //  角色
}

func (PipelineCredentialAuthUser) TableName() string {
	return "pipeline_credential_of_user"
}

type PipelineCredentialCategory struct {
	model.BaseModel
	CategoryName string `gorm:"column:category_name" db:"category_name" json:"categoryName"`
	Category     string `gorm:"column:category" db:"category" json:"category"` //  类别
	Scope        int    `json:"scope" gorm:"column:scope" db:"scope"`          //  凭证级别(0:system|1:simple)
	Example      string `json:"example" gorm:"column:example" db:"example"`    //  示例
}

func (PipelineCredentialCategory) TableName() string {
	return "pipeline_credential_categories"
}
