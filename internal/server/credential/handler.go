package credential

import (
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/middleware"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

// Info
// @Summary 获取凭证详情
// @Description 根据UUID获取凭证详细信息
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param data body request.InfoRequest true "请求体参数，包含凭证UUID"
// @Success 200 {object} response.APIModel{data=credential.InfoResponse} "成功返回凭证信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/info [post]
func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Add
// @Summary 添加凭证
// @Description 新增一个凭证
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param data body credential.AddRequest true "请求体参数，包含凭证信息"
// @Success 200 {object} response.APIModel "新增成功，返回凭证ID"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/add [post]
func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Add(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Update
// @Summary 更新凭证
// @Description 更新凭证信息
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param data body credential.UpdateRequest true "请求体参数，包含更新信息"
// @Success 200 {object} response.APIModel "更新成功，返回影响行数"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/update [put]
func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Update(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Page
// @Summary 分页查询凭证
// @Description 分页获取凭证列表，支持多条件筛选
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param data body credential.PageRequest true "请求体参数，包含分页及筛选条件"
// @Success 200 {object} response.APIModel{data=response.PageModel} "成功返回分页凭证列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/page [post]
func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req, c.GetString(common.UserEmail), middleware.IsAdmin(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

// Delete
// @Summary 删除凭证
// @Description 根据UUID批量删除凭证
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param data body request.DeleteRequest true "请求体参数，包含待删除凭证UUID列表"
// @Success 200 {object} response.APIModel "删除成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/delete [delete]
func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// Types
// @Summary 获取凭证类型对象数组
// @Description 获取所有凭证类型对象数组
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Success 200 {object} response.APIModel{data=[]credential.TypeResponse} "成功返回凭证类型数组"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/types [get]
func Types(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	isAdmin := middleware.IsAdmin(c)
	info, err := svc.Types(isAdmin)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Select
// @Summary 获取凭证类型（下拉框）
// @Description 获取凭证类型，提供给前端动态下拉框使用
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Param data body credential.SelectRequest true "请求体参数，包含筛选条件"
// @Success 200 {object} response.APIModel{data=[]credential.SelectResponse} "成功返回凭证下拉列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/select [post]
func Select(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	var req SelectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	if req.CategoryUUID == "" && req.Type == "" {
		response.Err(c, response.RequestParamError("type is required"))
		return
	}
	list, err := svc.Select(req, c.GetString(common.UserEmail), middleware.IsAdmin(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, list)
}

// Levels
// @Summary 获取凭证等级
// @Description 获取凭证等级列表
// @Tags 凭证管理
// @Accept json
// @Produce json
// @Success 200 {object} response.APIModel{data=[]credential.CredentialLevelResponse} "成功返回凭证等级"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/pipeline/credential/levels [get]
func Levels(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	isAdmin := middleware.IsAdmin(c)
	list, err := svc.GetLevel(isAdmin)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, list)
}

// Encrypt 加密凭证
func Encrypt(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewCredentialService(ts, mysql.GetDB(), redis.GetClient())
	var req EncryptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	if err := svc.Encrypt(req, middleware.GetUserName(c)); err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}
