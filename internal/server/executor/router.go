package executor

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {

	prefix := "/api/v1/pipeline"
	cAPI := router.Group(prefix)

	// runner 接口
	path, handler := NewRunnerServiceHandler()
	cAPI.POST(path+"*method", func(c *gin.Context) {
		http.StripPrefix(prefix, handler).ServeHTTP(c.Writer, c.Request)
	})

	// server 流水线相关接口
	{
		// 需要寻找更加高效的鉴权方式
		cAPI.POST("/state", State)                                                                        // pipeline状态
		cAPI.POST("/action", Action)                                                                      // action状态
		cAPI.POST("/logs", Logs)                                                                          // 查询日志
		cAPI.GET("/logs/origin", middleware.UTokenFromQuery(), middleware.AuthUToken(), OriginLogger)     // 原始日志
		cAPI.GET("/logs/download", middleware.UTokenFromQuery(), middleware.AuthUToken(), DownloadLogger) // 下载日志
		// operation
		cAPI.Use(middleware.AuthUToken())
		cAPI.POST("/manual", Run)          // 手动运行pipeline
		cAPI.POST("/skip", SkipStep)       // 跳过step
		cAPI.POST("/cancel", Cancel)       // 取消step
		cAPI.POST("/terminate", Terminate) // 终止pipeline
		cAPI.POST("/stage", Stage)         // 启动stage
		cAPI.POST("/dynamic", Dynamic)     // 动态参数注入确认
		cAPI.POST("/validate", Validate)   // 人工卡点
	}

	// 其他非核心接口
	{
		cAPI.POST("/validate/page", ValidatePage) // 人工卡点查询
	}
}
