package executor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"pipeline/internal/server/artifact"
	pipeRepo "pipeline/internal/server/pipeline"
	"strconv"
	"strings"
	"time"

	"pipeline/config"
	"pipeline/internal/server/logger"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	"pipeline/pkg/notify/send"
	"pipeline/pkg/notify/types"
	runnerv1 "pipeline/pkg/proto/runner/v1"
	"pipeline/pkg/queue"

	"github.com/avast/retry-go"
	"github.com/mitchellh/mapstructure"

	"gorm.io/gorm"

	"pipeline/pkg/eventbus"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	redisV8 "github.com/go-redis/redis/v8"
)

// 注册到事件总线
func init() {
	eventbus.Register(RunPipelineEventHandler)
	eventbus.Register(TriggerBlockingPipelineEventHandler)
	eventbus.Register(CleanBlockingPipelineEventHandler)
	eventbus.Register(PipelineRunnerUpdateHandler)
	eventbus.Register(PipelineRunnerHealthUpdateHandler)
	eventbus.Register(PipelineRunnerHealthDeleteHandler)
	eventbus.Register(StepRunOnRunnerEventHandler)
	eventbus.Register(ApproveStepNoticeEventHandler)
	eventbus.Register(PipelineExecuteCleanPreLogHandler)
	eventbus.Register(PipelineStatusUpdateNotifyEventHandler)
	eventbus.Register(NotifyRunnerCleanPipelineRunEventHandler)
	eventbus.Register(CheckPipelineRunFinishEventHandler)
	eventbus.Register(PipelineRunFinishEventHandler)
	eventbus.Register(ListenPipelineStepRunTimeoutEventHandler)
	eventbus.Register(CancelListenPipelineStepRunTimeoutEventHandler)
	eventbus.Register(PipelineStepRunTimeoutEventHandler)
	eventbus.Register(PipelineStepArtifactsOutputEventHandler)
	eventbus.Register(PipelineStepEnvOutputEventHandler)
	eventbus.Register(PipelineCancelStepEventHandler)
	eventbus.Register(PipelineTransferLogsEventHandler)
	eventbus.Register(PipelineStepRowStateUpdateEventHandler)
	eventbus.Register(StepStateUpdateEventHandler)
	eventbus.Register(UpdatePipelineStatusEventHandler)
	eventbus.Register(LogTransferReconciliationEventHandler)
	eventbus.Register(TerminatePipelineEventHandler)
	eventbus.Register(PurgeHistoryArtifactVersionEventHandler)
}

// RunPipelineEventHandler 执行流水线事件处理器
func RunPipelineEventHandler(ctx context.Context, event *events.RunPipelineEvent) error {
	jb, err := buildJob(event.Opts)
	if err != nil {
		log.ErrorE("failed to build pipeline job", err)
		return err
	}
	// 如果是阻塞方式执行，则将其放入任务队列中
	if jb.ctx.PipelineDefine.Blocking {
		log.Info("current pipeline is blocking, put it into queue", log.Any("txUuid", jb.ctx.TxUuid))
		pipelineUUID := jb.ctx.PipelineInfo.UUID
		pipelineTaskQueue := queue.NewPipelineTaskQueue(pipelineUUID)
		// 提交任务到队列
		log.Info("push pipeline task into queue", log.Any("txUuid", jb.ctx.TxUuid))
		if err = pipelineTaskQueue.Push(&queue.PipelineTask{TxUUID: jb.ctx.TxUuid}); err != nil {
			log.ErrorE("Failed to push pipeline task into queue", err)
			return err
		}
		// 尝试去触发执行
		log.Info("try to trigger blocking pipeline", log.Any("txUuid", jb.ctx.TxUuid))
		eventbus.PublishAsync(ctx, &events.TriggerBlockingPipelineEvent{
			PipelineUUID: pipelineUUID,
		})
	} else {
		// 提交任务进行执行
		pipelineDispatcher.SubmitJob(*jb)
	}
	return nil
}

// TriggerBlockingPipelineEventHandler 触发阻塞流水线事件处理(获取队头任务并执行,需要保证只有一个任务在执行)
func TriggerBlockingPipelineEventHandler(ctx context.Context, event *events.TriggerBlockingPipelineEvent) error {
	engine := mysql.GetDB()
	// 判断是否是流水线UUID, 如果为空则通过txUuid获取
	if event.PipelineUUID == "" {
		if err := engine.Model(models.PipelineLogger{}).Where("uuid = ?", event.TxUUID).
			Pluck("pipeline_uuid", &event.PipelineUUID).Error; err != nil {
			log.ErrorE("Failed to get pipeline uuid", err)
			return err
		}
	}
	log.Info(fmt.Sprintf("try to trigger blocking pipeline, pipelineUuid: %s", event.PipelineUUID))
	pipelineTaskQueue := queue.NewPipelineTaskQueue(event.PipelineUUID)
	for {
		task, ok := pipelineTaskQueue.Peek()
		if !ok {
			log.Info("no task in queue, skip trigger", log.Any("pipelineUuid", event.PipelineUUID))
			break
		}
		// 检查是否有正在执行的任务
		if err := engine.Transaction(func(tx *gorm.DB) error {
			var count int64
			if err := tx.Model(models.PipelineLogger{}).Where("pipeline_uuid = ? and status in (?)",
				event.PipelineUUID, []string{pipeline.Waiting, pipeline.Running}).Count(&count).Error; err != nil {
				return err
			}
			if count > 0 {
				return pipeline.PipelineNotFinishError
			}
			// 更新流水线状态为waiting, 如果前置状态不是blocking则说明已经被执行过了, pop继续peek下一个任务
			ret := tx.Model(models.PipelineLogger{}).Where("uuid = ? and status = ?",
				task.TxUUID, pipeline.Blocking).Update("status", pipeline.Waiting)
			if ret.Error != nil {
				return ret.Error
			}
			if ret.RowsAffected == 0 {
				return pipeline.PipelineAlreadyExecuteError // 更新失败说明已经被执行过了
			}
			return nil
		}); err != nil {
			if errors.Is(err, pipeline.PipelineNotFinishError) {
				log.Info("pipeline is running, skip trigger", log.Any("pipelineUuid", event.PipelineUUID))
				return nil
			} else if errors.Is(err, pipeline.PipelineAlreadyExecuteError) {
				log.Info("pipeline already execute, skip trigger, try next", log.Any("pipelineUuid", event.PipelineUUID))
				// pop task, because it has been executed
				pipelineTaskQueue.Pop()
				// try to trigger next
				continue
			} else {
				log.ErrorE("failed to check pipeline status", err)
				return err
			}
		}
		log.Info(fmt.Sprintf("execute blocking pipeline, txUuid: %s", task.TxUUID))
		// 从队列中获取任务并执行
		executeContext, err := RenewPipelineContext(task.TxUUID)
		if err != nil {
			return err
		}
		if err = NewEngine().ExecutePipeline(executeContext); err != nil {
			log.ErrorE("error executing pipeline : %v", err)
		}
		return nil
	}
	return nil
}

// CleanBlockingPipelineEventHandler 清理阻塞流水线事件处理器
func CleanBlockingPipelineEventHandler(ctx context.Context, event *events.CleanBlockingPipelineEvent) error {
	// 获取流水线UUID
	var pipelineUUID string
	engine := mysql.GetDB()
	if err := engine.Model(models.PipelineLogger{}).
		Where("uuid = ?", event.TxUUID).
		Pluck("pipeline_uuid", &pipelineUUID).Error; err != nil {
		log.ErrorE("Failed to get pipeline uuid", err)
		return err
	}
	log.Info(fmt.Sprintf("clean blocking pipeline, txUuid: %s", event.TxUUID))
	// 清理队列数据
	return queue.NewPipelineTaskQueue(pipelineUUID).Remove(event.TxUUID)
}

// PipelineStatusUpdateNotifyEventHandler 流水线状态变更通知事件处理器
func PipelineStatusUpdateNotifyEventHandler(_ context.Context, event *events.PipelineStatusUpdateNotifyEvent) error {
	// 重建流水线上下文
	if util.IsEmpty(event.ExecuteContext) {
		pipelineContext, err := RenewPipelineContext(event.TxUUID)
		if err != nil {
			return fmt.Errorf("failed to rebuild pipeline context: %w", err)
		} else {
			event.ExecuteContext = pipelineContext
		}
	}
	// 设置缺省值
	message := util.Coalesce(event.Message, "流水线状态变更通知")
	template := util.Coalesce(event.TemplateType, types.Pipeline)
	// 获取最新的环境变量
	event.ExecuteContext.StepCtx.Env = util.MergeMap(event.ExecuteContext.StepCtx.Env, pipelineEnvOutput(event.TxUUID))

	// 组装流水线失败卡片的变量，用于AI分析功能入参传递
	if event.Status == pipeline.Failed && event.StepIndex != nil {
		event.ExecuteContext.StepCtx.Env["stageIdx"] = strconv.Itoa(event.StepIndex.StageIdx)
		event.ExecuteContext.StepCtx.Env["stepRowIdx"] = strconv.Itoa(event.StepIndex.StepRowIdx)
		event.ExecuteContext.StepCtx.Env["stepIdx"] = strconv.Itoa(event.StepIndex.StepIdx)
		db := mysql.GetDB()
		stepState := &models.StepState{}
		if err := db.Model(&models.StepState{}).Where("tx_uuid = ? and state = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
			event.TxUUID, pipeline.Failed, event.StepIndex.StageIdx, event.StepIndex.StepRowIdx, event.StepIndex.StepIdx).First(stepState).Error; err != nil {
			return fmt.Errorf("failed to get step state: %w", err)
		}
		actionState := &models.ActionState{}
		if err := db.Model(&models.ActionState{}).Where("step_id = ? and state = ?", stepState.Id, pipeline.Failed).First(actionState).Error; err != nil {
			return fmt.Errorf("failed to get action state: %w", err)
		}
		event.ExecuteContext.StepCtx.Env["actionIdx"] = strconv.Itoa(actionState.ActionIdx)
		event.ExecuteContext.StepCtx.Env["txUUID"] = event.TxUUID
	}

	// 处理json字符串中的双引号
	notifyContent := &send.NotifyContent{
		TemplateType: template,
		Status:       event.Status,
		Message:      strings.ReplaceAll(message, `"`, `'`),
	}

	// 检查是否需要发送通知
	for _, notice := range event.ExecuteContext.PipelineDefine.Notices {
		for _, eventType := range notice.Events {
			if eventType == event.Status {
				if err := send.PipelineNotice(event.ExecuteContext,
					notice, notifyContent); err != nil {
					log.ErrorE("failed to send pipeline notice", err)
				}
			}
		}
	}
	return nil
}

// PipelineExecuteCleanPreLogHandler 流水线执行开始后检查并清理前置build数据和日志
func PipelineExecuteCleanPreLogHandler(_ context.Context, event *events.PipelineExecuteCleanPreLogEvent) error {

	pipelineContext := event.ExecuteContext
	// 清理日志 和 构建文件
	pid := pipelineContext.Logger.PipelineUuid

	log.Info("ClearBuildHistoryLogs", log.Any("pipeline_uuid", pid))

	clearThreshold := int(config.Items().Pipeline.MaxKeepItem) //nolint:gosec

	if pipelineContext.PipelineDefine.MaxKeepItem <= 0 || pipelineContext.PipelineDefine.MaxKeepItem > clearThreshold {
		pipelineContext.PipelineDefine.MaxKeepItem = clearThreshold
	}

	loggerSvc := logger.NewLoggerService(nil, mysql.GetDB(), redis.GetClient())

	// 查询 当前pipeline_id 对应 的pipeline_log 构建的日志条数
	logItemsCount, err := loggerSvc.GetByPipelineUUID(pid)

	log.Info("ClearBuildHistoryLogs", log.Any("logItemsCount", logItemsCount))

	if err != nil {
		log.Error("ClearBuildHistoryLogs GetByPipelineUUID", log.Any("error", err))
		return err
	}

	// 小于阈值不清理
	if logItemsCount <= int64(pipelineContext.PipelineDefine.MaxKeepItem) {
		return err
	}

	clearLogsNum := logItemsCount - int64(pipelineContext.PipelineDefine.MaxKeepItem)

	// 删除 minio 中的构建文件日志
	// 1. 查询当前的pipeline_id 对应的pipeline_log
	logItems, err := loggerSvc.GetLogUUIDByPipelineUUID(pid, clearLogsNum)
	if err != nil {
		log.Error("ClearBuildHistoryLogs GetLogUUIDByPipelineUUID", log.Any("error", err))
		return err
	}

	// 删除 minio 中的构建文件日志 和  删除 pipeline_log 中的快照
	err = loggerSvc.Delete(logItems, "clean", true)
	if err != nil {
		log.Error("ClearBuildHistoryLogs Delete", log.Any("error", err))
		return err
	}

	// 清理dbfs中可能残余的日志
	engine := mysql.GetDB()
	totalMetaIds := make([]int64, 0)
	for _, item := range logItems {
		var metaIds []int64
		engine.Table("dbfs_meta").Where("full_path like ?",
			util.Like(DBFSPrefix+item)).Pluck("id", &metaIds)
		totalMetaIds = append(totalMetaIds, metaIds...)
	}
	if len(totalMetaIds) > 0 {
		if err = engine.Transaction(func(tx *gorm.DB) error {
			if err = tx.Table("dbfs_meta").Where("id in (?)", totalMetaIds).Delete(nil).Error; err != nil {
				return err
			}
			return tx.Table("dbfs_data").Where("meta_id in (?)", totalMetaIds).Delete(nil).Error
		}); err != nil {
			log.Error("clear build history logs delete dbfs", log.Any("error", err))
		}
	}

	log.Info(fmt.Sprintf("clear build history logs pipeline_uuid: %s, clearLogsNum: %d finished", pid, clearLogsNum))

	return nil
}

// ApproveStepNoticeEventHandler 人工卡点通知事件处理器
func ApproveStepNoticeEventHandler(_ context.Context, event *events.ApproveStepNoticeEvent) error {
	stepDefine := event.StepDefine
	if stepDefine == nil {
		log.Println("approve step notice event data is invalid", log.Any("event", event))
		return nil
	}
	// 获取最新的环境变量
	event.ExecuteContext.StepCtx.Env = util.MergeMap(event.ExecuteContext.StepCtx.Env, pipelineEnvOutput(event.ExecuteContext.TxUuid))
	// 发送通知
	for _, notice := range event.StepDefine.Notices {
		_ = send.PipelineNotice(event.ExecuteContext, notice, &send.NotifyContent{
			TemplateType: types.Approve,
		})
	}
	return nil
}

// StepRunOnRunnerEventHandler 记录当前步骤在具体的Runner上执行事件处理器
func StepRunOnRunnerEventHandler(ctx context.Context, event *events.StepRunOnRunnerEvent) error {
	engine := mysql.GetDB()
	return engine.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
			event.TxUUID, event.Index.StageIdx, event.Index.StepRowIdx, event.Index.StepIdx).
			Update("runner", event.RunnerToken).Error; err != nil {
			return err
		}
		return nil
	})
}

// CheckPipelineRunFinishEventHandler 检查流水线执行完成 事件处理器
func CheckPipelineRunFinishEventHandler(ctx context.Context, event *events.CheckPipelineRunFinishEvent) error {
	log.Info("try to check pipeline run finish", log.Any("uuid", event.TxUUID))
	engine := mysql.GetDB()
	if err := engine.Transaction(func(tx *gorm.DB) error {
		// 需要保证所有的step都已经执行完毕
		query := tx.Model(models.StepState{}).Where("tx_uuid = ? and state in (?)",
			event.TxUUID, []string{pipeline.Waiting, pipeline.Pending, pipeline.Running})
		// 如果是step是失败状态：因为下一个阶段不可能再执行，所以只需要检查当前阶段的所有step即可
		if event.StepState == pipeline.Failed || event.StepState == pipeline.Cancel {
			query = query.Where("stage_idx = ?", event.StageIdx)
		}
		var count int64
		if err := query.Count(&count).Error; err != nil {
			return err
		}
		// 有未执行完的step, 放弃清理
		if count > 0 {
			return pipeline.PipelineNotFinishError
		}
		return nil
	}); err != nil {
		if errors.Is(err, pipeline.PipelineNotFinishError) {
			log.Info("pipeline run not finish", log.Any("uuid", event.TxUUID))
			return nil // 未执行完的step, 放弃清理
		} else {
			log.ErrorE("failed to check pipeline run finish", err)
		}
	}
	// 发送流水线执行完成事件
	return eventbus.Publish(ctx, &events.PipelineRunFinishEvent{TxUUID: event.TxUUID})
}

// PipelineRunFinishEventHandler 流水线执行完成事件处理器
func PipelineRunFinishEventHandler(ctx context.Context, event *events.PipelineRunFinishEvent) error {
	// 如果流水线是阻塞执行的, 需要清理队列数据和通知下一个条流水线执行
	err := eventbus.Publish(ctx, &events.CleanBlockingPipelineEvent{TxUUID: event.TxUUID})
	if err != nil {
		log.ErrorE("Failed to clean blocking pipeline", err)
	}
	// 尝试触发下一个阻塞中的流水线执行
	eventbus.PublishAsync(ctx, &events.TriggerBlockingPipelineEvent{TxUUID: event.TxUUID})
	// 将dbfs中还未传输日志到对象存储
	eventbus.PublishAsync(ctx, &events.PipelineTransferLogsEvent{TxUUID: event.TxUUID, Delay: 10 * time.Second})
	// 清理runner缓存
	return eventbus.Publish(ctx, &events.NotifyRunnerCleanPipelineRunEvent{TxUUID: event.TxUUID})
}

// NotifyRunnerCleanPipelineRunEventHandler 通知runner清理pipeline执行缓存
func NotifyRunnerCleanPipelineRunEventHandler(ctx context.Context, event *events.NotifyRunnerCleanPipelineRunEvent) error {

	engine := mysql.GetDB()

	var pipelineSnapshot string
	// check pipeline mode is debug, if true, skip clean runner pipeline cached
	if err := engine.Model(models.PipelineLogger{}).Where("uuid = ?", event.TxUUID).
		Pluck("pipeline_snapshot", &pipelineSnapshot).Error; err != nil {
		log.ErrorE("Failed to get pipeline snapshot", err)
		return err
	}
	pipelineDefine, err := pipeline.ParsePipeline(pipelineSnapshot)
	if err != nil {
		log.ErrorE("Failed to parse pipeline snapshot", err)
		return err
	}
	// try to clean runner pipeline cached
	var useRunners []string
	if err = engine.Model(models.StepState{}).Select("DISTINCT runner").Where("tx_uuid = ?", event.TxUUID).
		Find(&useRunners).Error; err != nil {
		return err
	}
	// get pipeline meta
	meta, _ := logger.NewLoggerRepo(engine).LoadPipelineLogMeta(event.TxUUID)
	// add redis set
	for _, runnerToken := range useRunners {
		// because some steps may not use runner, e.g. StepManualValidate/StepDynamicEnvInput
		if runnerToken == "" {
			log.Info("runner token is empty, skip clean runner pipeline cached",
				log.Any("uuid", event.TxUUID))
			continue
		}
		log.Info("clean runner pipeline cached", log.Any("runnerToken", runnerToken))
		purgeTask := &runnerv1.PurgeTask{
			TxUuid:      meta.LoggerInfo.UUID,
			PipelineId:  meta.PipelineInfo.ID,
			AppIdentity: meta.AppInfo.Identity,
			BuildNumber: meta.LoggerInfo.BuildNumber,
		}
		// 通过zset的时间戳来作为可清理的时间排序, 针对debug模式下，需要延迟清理
		var score = time.Now().Unix()
		if pipelineDefine.Debug {
			score = score + int64(config.Items().Runner.DebugPurgeDelay.Seconds())
		}
		z := &redisV8.Z{
			Score:  float64(score),
			Member: util.ToJSONString(purgeTask),
		}
		if err = redis.GetClient().ZAdd(ctx, runnerPurgeTaskKey(runnerToken), z).Err(); err != nil {
			log.ErrorE("Failed to add clean runner entry to redis zset", err)
		}
	}
	log.Info("notify runner clean pipeline run success", log.Any("uuid", event.TxUUID), log.Any("runners", useRunners))
	// 清理redis
	queue.PostProcessTask(event.TxUUID)

	return nil
}

// PipelineRunnerUpdateHandler 流水线执行器更新事件处理器
func PipelineRunnerUpdateHandler(ctx context.Context, event *events.PipelineRunnerUpdateEvent) error {
	// convert to cache entry
	entry := &models.PipelineRunnerCacheEntry{
		UUID:    event.UUID,
		Name:    event.Name,
		Disable: event.Disable,
		Labels:  strings.Split(event.Labels, ","),
		Version: event.Version,
		Token:   event.Token,
	}
	// marshal
	data, err := json.Marshal(entry)
	if err != nil {
		return err
	}
	// set to redis
	return redis.GetClient().Set(ctx, runnerKey(event.UUID), data, 0).Err()
}

// PipelineRunnerHealthUpdateHandler 流水线执行器健康更新事件处理器
func PipelineRunnerHealthUpdateHandler(ctx context.Context, event *events.PipelineRunnerUpdateEvent) error {
	// convert to cache entry
	entry := models.RunnerConvertToEntry(event.PipelineRunner)
	// check if skip update active time
	if event.SkipRefreshActiveTime {
		// get from redis
		data, err := redis.GetClient().HGet(ctx, models.RunnerHeartBeat, event.UUID).Result()
		// check for redis.NIL
		if errors.Is(err, redisV8.Nil) {
			entry.ActiveTime = time.Time{}
		} else if err != nil {
			return err
		} else {
			// unmarshal
			var runnerHealthEntry models.RunnerHealthEntry
			if err = json.Unmarshal([]byte(data), &runnerHealthEntry); err != nil {
				return err
			}
			entry.ActiveTime = runnerHealthEntry.ActiveTime
		}
	}
	// set to redis
	return redis.GetClient().HSet(ctx, models.RunnerHeartBeat, event.UUID, entry.Marshal()).Err()
}

// PipelineRunnerHealthDeleteHandler 流水线执行器健康删除事件处理器
func PipelineRunnerHealthDeleteHandler(ctx context.Context, event *events.PipelineRunnerDeleteEvent) error {
	// set to redis
	return redis.GetClient().HDel(ctx, models.RunnerHeartBeat, event.IDs...).Err()
}

// ListenPipelineStepRunTimeoutEventHandler 监听流水线步骤执行超时事件处理器
func ListenPipelineStepRunTimeoutEventHandler(ctx context.Context, event *events.ListenPipelineStepRunTimeoutEvent) error {
	timeout := event.Time.Add(config.Items().Pipeline.Timeout)
	var strategy pipeline.Strategy
	if event.StepDefine != nil {
		if m, ok := event.StepDefine["strategy"]; ok {
			// 需要先将map转为json字符串
			jsonBytes, err := json.Marshal(m)
			if err != nil {
				log.ErrorE("marshal strategy error", err)
			} else {
				if err = json.Unmarshal(jsonBytes, &strategy); err != nil {
					log.ErrorE("unmarshal strategy error", err)
				} else {
					duration := time.Duration(strategy.Timeout) * time.Minute //nolint:gosec
					timeout = event.Time.Add(duration)
				}
			}
		}
	}
	// 满足一些不需要配置超时的情况
	if event.DefaultSkipTimeout && strategy.Timeout == 0 {
		log.Info("skip listen pipeline step run timeout", log.Any("txUuid", event.TxUUID))
		return nil
	}
	log.Info("listen pipeline step run timeout", log.Any("txUuid", event.TxUUID),
		log.Any("index", event.Index), log.Any("timeout", timeout))

	return queue.NewPipelineStepTimeoutQueue().Push(&queue.TimeoutQueueEntry{
		TxUUID:  event.TxUUID,
		Index:   event.Index,
		Timeout: timeout,
	})
}

// CancelListenPipelineStepRunTimeoutEventHandler 取消监听流水线步骤执行超时事件处理器
func CancelListenPipelineStepRunTimeoutEventHandler(_ context.Context, event *events.CancelListenPipelineStepRunTimeoutEvent) error {
	tq := queue.NewPipelineStepTimeoutQueue()
	for _, index := range event.Indexes {
		log.Info("cancel listen pipeline step run timeout", log.Any("txUuid", event.TxUUID), log.Any("index", index))
		if err := tq.Remove(event.TxUUID, index); err != nil {
			log.ErrorE("failed to remove pipeline step timeout entry", err)
		}
	}
	return nil
}

// PipelineStepRunTimeoutEventHandler 流水线步骤执行超时事件处理器（服务端检查）
func PipelineStepRunTimeoutEventHandler(ctx context.Context, event *events.PipelineStepRunTimeoutEvent) error {
	// 设置step状态为失败
	engine := mysql.GetDB()
	if err := engine.Transaction(func(tx *gorm.DB) error {
		r := tx.Model(models.StepState{}).
			Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
				event.TxUUID, event.Index.StageIdx, event.Index.StepRowIdx, event.Index.StepIdx, pipeline.Running).
			UpdateColumns(map[string]any{
				"state":    pipeline.Failed,
				"end_time": time.Now().Unix(),
			})
		if r.Error != nil {
			return r.Error
		} else if r.RowsAffected == 0 {
			return fmt.Errorf("step not running, skip timeout , txUuid: %s, index: %v", event.TxUUID, event.Index)
		}
		// 发送事件通知
		eventbus.PublishAsync(ctx, &events.StepStateUpdateEvent{
			TxUUID:      event.TxUUID,
			StepIndexes: []pipeline.StepIndex{event.Index},
			State:       pipeline.Failed,
		})
		return nil
	}); err != nil {
		log.ErrorE("update step state error", err)
		return err
	}
	// 本质上就是当前step正常执行失败了，的处理逻辑一样
	if err := NewEngine().ExecuteNext(event.TxUUID, pipeline.Failed, "执行超时", event.Index); err != nil {
		log.ErrorE("execute next error", err)
		return nil
	}

	return nil
}

// PipelineStepArtifactsOutputEventHandler 流水线步骤输出制品事件处理器
func PipelineStepArtifactsOutputEventHandler(ctx context.Context, event *events.StepArtifactsOutputEvent) error {
	log.Printf("StepArtifactsOutputEvent: %#v", event)
	// 反序列化制品数组数据
	var artifactEntries []*models.ArtifactEntry
	if err := json.Unmarshal([]byte(event.Outputs), &artifactEntries); err != nil {
		log.ErrorE("unmarshal artifacts error", err)
		return err
	}
	// size check
	if len(artifactEntries) == 0 {
		log.Error("artifacts is empty")
		return nil
	}
	// 保存制品数据
	engine := mysql.GetDB()
	// 更新制品（最新版本、更新时间、版本数量等），如果制品不存在则创建，如果版本存在则更新
	repo := artifact.NewArtifactRepo(engine)
	artifactUUIDList := make([]string, 0) // 用于检查是否需要清理历史版本
	for i, entry := range artifactEntries {
		// 检查制品是否存在
		var artifactModel models.Artifact
		err := engine.Where("app_uuid = ? and name = ?",
			entry.Artifact.AppUuid, entry.Artifact.Name).First(&artifactModel).Error
		// 制品不存在，则保存制品
		if errors.Is(err, gorm.ErrRecordNotFound) {
			entry.Artifact.Versions = 1
			entry.Artifact.UUID = util.UUID()
			entry.Artifact.GmtCreate = entry.Version.GmtCreate
			entry.Artifact.Creator = entry.Version.Creator
			entry.Artifact.LatestVersion = entry.Version.Version
			// 设置版本的制品UUID
			artifactEntries[i].Version.ArtifactUuid = entry.Artifact.UUID
			if _, err = repo.Add(&entry.Artifact); err != nil {
				log.ErrorE("save artifact error", err)
			}
		} else {
			// 存在则更新制品版本
			artifactModel.LatestVersion = entry.Version.Version
			artifactModel.GmtModified = entry.Version.GmtCreate
			artifactModel.Versions++
			if err = engine.Save(&artifactModel).Error; err != nil {
				log.ErrorE("update artifact error", err)
			}
			artifactEntries[i].Artifact.UUID = artifactModel.UUID
			artifactEntries[i].Version.ArtifactUuid = artifactModel.UUID
		}
		// 记录制品UUID
		artifactUUIDList = append(artifactUUIDList, entry.Artifact.UUID)
	}
	// 制品版本：直接新增
	var artifactVersions []*models.ArtifactVersion
	for _, entry := range artifactEntries {
		artifactVersions = append(artifactVersions, &entry.Version)
	}
	if err := engine.Save(artifactVersions).Error; err != nil {
		log.ErrorE("save artifact versions error", err)
		return err
	}
	// 清理历史版本
	eventbus.PublishAsync(ctx, &events.PurgeHistoryArtifactVersionEvent{
		ArtifactUUIDList: artifactUUIDList,
	})
	return nil
}

// PipelineStepEnvOutputEventHandler 流水线步骤输出环境变量事件处理器
func PipelineStepEnvOutputEventHandler(ctx context.Context, event *events.StepEnvOutputEvent) error {

	log.Printf("step envs output event: %#v", event)

	// 反序列化制品数组数据
	var envMap map[string]string
	if err := json.Unmarshal([]byte(event.EnvMapString), &envMap); err != nil {
		log.ErrorE("unmarshal envs error", err)
		return err
	}

	// size check
	if len(envMap) == 0 {
		log.Info("envs is empty")
		return nil
	}

	// 存储环境变量供后续step运行使用
	var dbEnvMap = make(map[string]string)

	var envs string
	engine := mysql.GetDB()
	if err := engine.Model(models.PipelineLogger{}).Where("uuid = ?", event.TxUUID).Limit(1).Pluck("envs", &envs).Error; err != nil {
		log.ErrorE("get envs error", err)
	}
	if envs != "" {
		dbEnvMap = util.JSONStringToStruct[map[string]string](envs)
	}

	saveEnvMap := util.MergeMap(envMap, dbEnvMap)

	// 更新到数据库（需要考虑并发问题）
	if err := retry.Do(func() error {
		return engine.Transaction(func(tx *gorm.DB) error {
			return tx.Model(models.PipelineLogger{}).Where("uuid = ?", event.TxUUID).
				Update("envs", util.ToJSONString(saveEnvMap)).Error
		})
	}); err != nil {
		log.ErrorE("update envs error", err)
	}

	return nil
}

// PipelineCancelStepEventHandler 流水线取消步骤事件处理器
func PipelineCancelStepEventHandler(ctx context.Context, event *events.CancelStepEvent) error {
	// 取消监听任务超时
	eventbus.PublishAsync(ctx, &events.CancelListenPipelineStepRunTimeoutEvent{
		TxUUID:  event.TxUUID,
		Indexes: []pipeline.StepIndex{event.StepIndex},
	})
	engine := mysql.GetDB()
	// 更新成功尝试更新流水线状态
	if err := engine.Transaction(func(tx *gorm.DB) error {
		return eventbus.Publish(ctx, &events.PipelineLoggerStatusUpdateEvent{
			DB:               tx,
			TxUUID:           event.TxUUID,
			ExpectedStatuses: []string{pipeline.Running},
			TargetStatus:     pipeline.Cancel,
		})
	}); err != nil {
		log.Printf("update pipeline state error: %s", err.Error())
	} else {
		// publish event to notify user
		eventbus.PublishAsync(ctx, &events.PipelineStatusUpdateNotifyEvent{
			TxUUID:  event.TxUUID,
			Status:  pipeline.Cancel,
			Message: fmt.Sprintf("%s cancel step", event.User),
		})
	}
	// 检查流水线是否执行完成， 完成则发送流水线执行完成事件，触发后续的阻塞流水线执行
	eventbus.PublishAsync(ctx, &events.CheckPipelineRunFinishEvent{
		TxUUID:    event.TxUUID,
		StageIdx:  event.StepIndex.StageIdx,
		StepState: pipeline.Cancel,
	})
	return nil
}

// PipelineTransferLogsEventHandler 流水线日志转移事件处理器(做兜底处理，将日志从dbfs转移到对象存储)
func PipelineTransferLogsEventHandler(ctx context.Context, event *events.PipelineTransferLogsEvent) error {
	// 延迟一定时间后再进行执行(因为日志可能还在写入中)
	if event.Delay > 0 {
		time.Sleep(event.Delay)
	}
	// 获取数据库连接
	engine := mysql.GetDB()
	// 查询所有的step状态
	var stepIds []uint64
	if err := engine.Model(&models.StepState{}).Select("id").
		Where("tx_uuid = ?", event.TxUUID).Find(&stepIds).Error; err != nil {
		return err
	}
	if len(stepIds) == 0 {
		return nil
	}
	// 查询所有的action状态
	var actions []models.ActionState
	if err := engine.Model(&models.ActionState{}).
		Where("log_in_storage = false and step_id in (?)", stepIds).
		Find(&actions).Error; err != nil {
		return err
	}
	if len(actions) == 0 {
		return nil
	}
	// 转移全部还未转义的日志
	for _, action := range actions {
		// 7c6d0a3ee81745859662f2a9b241a911-0-1-0-5.log
		filename := action.LogFilename
		arr := strings.Split(filename, "-")
		if len(arr) == 0 {
			log.ErrorE("invalid log filename", fmt.Errorf("filename: %s", filename))
			continue
		}
		// txUUID
		txUUID := arr[0]
		var remove func()
		// 从数据库写入到存储
		remove, err := TransferLogs(ctx, txUUID, action.LogFilename)
		if err != nil {
			if event.Force && errors.Is(err, os.ErrNotExist) {
				log.Info(fmt.Sprintf("file does not exist, force remove it, txUuid: %s, filename: %s",
					txUUID, action.LogFilename))
			} else {
				//log.Error(err.Error())
				continue
			}
		}
		if remove != nil {
			remove()
		}
		// 更新字段为true
		if err = engine.Model(&models.ActionState{}).
			Where("log_in_storage = false and id = ?", action.Id).
			Updates(map[string]any{"log_in_storage": true}).Error; err != nil {
			return err
		}
	}
	return nil
}

// PipelineStepRowStateUpdateEventHandler 流水线步骤行状态更新事件处理器(用于更新当前行的后续step状态，比如当前step执行失败，后续step需要设置为取消状态)
func PipelineStepRowStateUpdateEventHandler(ctx context.Context, event *events.PipelineStepRowStateUpdateEvent) error {
	// 获取数据库连接
	engine := mysql.GetDB()
	// 更新成功尝试更新当前行的后续step状态为cancel状态
	query := engine.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ?",
		event.TxUUID, event.StageIdx, event.StepRowIdx)
	// 是否需要增加前置状态条件
	if event.PrevState != "" {
		query = query.Where("state = ?", event.PrevState)
	}
	// 发送通知
	var stepIndexes []pipeline.StepIndex
	if err := query.Select("stage_idx", "step_row_idx", "step_idx", "state").
		Find(&stepIndexes).Error; err == nil {
		eventbus.PublishAsync(ctx, &events.StepStateUpdateEvent{
			TxUUID:      event.TxUUID,
			StepIndexes: stepIndexes,
			State:       event.State,
		})
	}
	// 更新状态
	if err := query.Update("state", event.State).Error; err != nil {
		log.ErrorE("update step row state error: %s", err)
		return err
	}
	// 同时更新后续的阶段状态
	return engine.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx > ? ",
		event.TxUUID, event.StageIdx).Update("state", event.State).Error
}

// StepStateUpdateEventHandler 步骤状态更新事件处理器，支持多个step的状态更新
func StepStateUpdateEventHandler(ctx context.Context, event *events.StepStateUpdateEvent) error {
	log.Info("StepStateUpdateEventHandler", log.Any("indexs", event.StepIndexes),
		log.Any("state", event.State), log.Any("txUuid", event.TxUUID))

	if len(event.StepIndexes) == 0 {
		log.Info("StepStateUpdateEventHandler no step indexs")
		return nil
	}

	// 重建流水线上下文
	if util.IsEmpty(event.ExecuteContext) {
		pipelineContext, err := RenewPipelineContext(event.TxUUID)
		if err != nil {
			return fmt.Errorf("failed to rebuild pipeline context: %w", err)
		} else {
			event.ExecuteContext = pipelineContext
		}
	}

	type StepDefine struct {
		Notices []pipeline.Notice `json:"notices"`
	}

	var allNotices []pipeline.Notice
	stageMatrix := pipeline.ParseStepMatrix(event.ExecuteContext.PipelineDefine.Stages)
	for _, stepIdx := range event.StepIndexes {
		step := stageMatrix.Get(stepIdx)
		var stepDefine StepDefine
		if err := mapstructure.Decode(step, &stepDefine); err != nil {
			return fmt.Errorf("StepStateUpdateEventHandler mapstructure decode error: %w", err)
		}
		allNotices = append(allNotices, stepDefine.Notices...)
	}

	message := util.Coalesce(event.Message, "流水线步骤状态变更通知")
	notifyContent := &send.NotifyContent{
		Status:  event.State,
		Message: strings.ReplaceAll(message, `"`, `'`),
	}

	for _, notice := range allNotices {
		for _, noticeEvent := range notice.Events {
			if noticeEvent == event.State {
				_ = send.StepNotice(event.ExecuteContext, notice, notifyContent)
			}
		}
	}

	return nil
}

// UpdatePipelineStatusEventHandler 流水线状态更新事件处理器
func UpdatePipelineStatusEventHandler(ctx context.Context, event *events.PipelineLoggerStatusUpdateEvent) error {

	log.Info("event is: ", log.Any("txUUID", event.TxUUID),
		log.Any("expectedStatuses", event.ExpectedStatuses),
		log.Any("targetStatus", event.TargetStatus))

	loggerRepo := logger.NewLoggerRepo(event.DB)
	pipelineRepo := pipeRepo.NewInfoRepo(event.DB)

	// 业务逻辑：根据目标状态设置不同的字段
	updates := map[string]any{
		"status": event.TargetStatus,
	}

	// 如果目标状态是 running，则设置开始时间
	if event.TargetStatus == pipeline.Running {
		updates["start_time"] = time.Now()
	}

	// 如果目标状态是终结态，则设置结束时间
	if event.TargetStatus == pipeline.Failed || event.TargetStatus == pipeline.Success ||
		event.TargetStatus == pipeline.Terminated || event.TargetStatus == pipeline.Cancel {
		updates["end_time"] = time.Now()
	}

	// 更新流水线状态
	updated, err := loggerRepo.UpdateStatusWithColumns(event.TxUUID, event.ExpectedStatuses, updates)
	if err != nil {
		log.ErrorE("update pipeline logger state error", err)
		return err
	}
	if !updated {
		log.Info("pipeline status update failed, current status is not as expected")
		return fmt.Errorf("pipeline status is not in expected status")
	}

	// 获取流水线记录
	pipelineLogger, err := loggerRepo.GetByUUID(event.TxUUID)
	if err != nil {
		log.ErrorE("get pipeline logger error", err)
		return err
	}

	// 更新流水线最新状态
	if err = pipelineRepo.UpdateLatestStatus(pipelineLogger.PipelineUuid,
		pipelineLogger.BuildNumber, event.TargetStatus); err != nil {
		log.ErrorE("update pipeline latest status error", err)
		return err
	}

	return nil

}

// LogTransferReconciliationEventHandler 检查日志文件是否在dbfs中存在
func LogTransferReconciliationEventHandler(ctx context.Context, event *events.LogTransferReconciliationEvent) error {
	engine := mysql.GetDB()
	timestamp := event.Before.UnixMicro()
	// 查询dbfs_meta表中是否存在对应的日志文件
	var fileFullPathList []string
	if err := engine.Table("dbfs_meta").
		Where("create_timestamp < ?", timestamp).
		Pluck("full_path", &fileFullPathList).Error; err != nil {
		log.ErrorE("get dbfs meta error", err)
		return err
	}
	if len(fileFullPathList) == 0 {
		log.Info("no dbfs meta found, skip reconciliation")
		return nil
	}
	// 格式：1:action_log/b3afa9a750bd4a13b328c4c2022b9cf5-1-0-1-2.log
	// 需要解析出tx_uuid, stage_idx, step_idx, step_row_idx
	for _, fileFullPath := range fileFullPathList {
		// 获取文件名
		logFilename := filepath.Base(fileFullPath)
		filename := strings.TrimSuffix(logFilename, ".log")
		// 解析：b3afa9a750bd4a13b328c4c2022b9cf5-1-0-1-2
		arr := strings.Split(filename, "-")
		if len(arr) < 5 {
			log.ErrorE("invalid log filename", fmt.Errorf("filename: %s", filename))
			continue
		}
		txUUID := arr[0]
		// 存储到对象存储
		remove, err := TransferLogs(ctx, txUUID, logFilename)
		if err != nil {
			log.ErrorE("transfer logs error", err)
			continue
		}
		if remove != nil {
			remove()
		}
		// 反查action如果未log_in_storage为false则更新为true
		if err = engine.Model(&models.ActionState{}).Where(
			"log_filename = ? and log_in_storage = false", logFilename).
			Update("log_in_storage", true).Error; err != nil {
			log.ErrorE("update action log_in_storage error", err)
		}
	}
	return nil
}

// TerminatePipelineEventHandler 流水线终止事件处理器
func TerminatePipelineEventHandler(ctx context.Context, event *events.TerminatePipelineEvent) error {
	engine := mysql.GetDB()
	// 移除队列中等待执行的任务
	queueSteps := queue.RemoveTask(event.UUID)
	log.Info("remove queue steps", log.Any("steps", queueSteps))
	// 处理超时监听队列
	var runningStepIdx []pipeline.StepIndex
	engine.Model(models.StepState{}).
		Select("stage_idx", "step_row_idx", "step_idx").
		Where("tx_uuid = ? and state = ?", event.UUID, pipeline.Running).
		Find(&runningStepIdx)
	eventbus.PublishAsync(ctx, &events.CancelListenPipelineStepRunTimeoutEvent{
		TxUUID:  event.UUID,
		Indexes: runningStepIdx,
	})
	// open transaction, update step state and pipeline state
	if err := engine.Transaction(func(tx *gorm.DB) error {
		if err := eventbus.Publish(ctx, &events.PipelineLoggerStatusUpdateEvent{
			DB:               tx,
			TxUUID:           event.UUID,
			ExpectedStatuses: []string{pipeline.Blocking, pipeline.Waiting, pipeline.Running},
			TargetStatus:     pipeline.Terminated,
		}); err != nil {
			return err
		}
		// 针对人工卡点，更新状态
		query := tx.Model(models.StepState{}).Where("tx_uuid = ? and state in (?)",
			event.UUID, []string{pipeline.Blocking, pipeline.Waiting, pipeline.Pending, pipeline.Running})

		// 发送通知
		var stepIndexes []pipeline.StepIndex
		query.Select("stage_idx", "step_row_idx", "step_idx", "state", "end_time").Find(&stepIndexes)

		if ret := query.UpdateColumns(map[string]any{
			"state":    pipeline.Cancel,
			"end_time": time.Now().Unix(),
		}); ret.Error != nil {
			return ret.Error
		} else if ret.RowsAffected > 0 {
			eventbus.PublishAsync(context.Background(), &events.StepStateUpdateEvent{
				TxUUID:      event.UUID,
				StepIndexes: stepIndexes,
				State:       pipeline.Cancel,
			})
		}
		// 更新stage状态
		if err := tx.Model(models.StageState{}).
			Where("tx_uuid = ? and state = ?", event.UUID, pipeline.Ready).
			Update("state", pipeline.Cancel).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	// publish event to notify user
	if err := eventbus.Publish(ctx, &events.PipelineStatusUpdateNotifyEvent{
		Status:  pipeline.Terminated,
		Message: fmt.Sprintf("%s terminated pipeline", event.Ops),
		TxUUID:  event.UUID,
	}); err != nil {
		log.ErrorE("publish notify event error", err)
	}
	// 当前流水线执行完成（清理构建缓存、清理阻塞队列缓存等）
	eventbus.PublishAsync(ctx, &events.PipelineRunFinishEvent{
		TxUUID: event.UUID,
	})
	return nil
}

// PurgeHistoryArtifactVersionEventHandler 清理制品历史版本事件处理器
func PurgeHistoryArtifactVersionEventHandler(ctx context.Context, event *events.PurgeHistoryArtifactVersionEvent) error {
	engine := mysql.GetDB()
	artifactService := artifact.NewArtifactService(nil, engine, redis.GetClient())
	for _, artifactUUID := range event.ArtifactUUIDList {
		// 查询制品
		var artifact models.Artifact
		if err := engine.Where("uuid = ?", artifactUUID).
			First(&artifact).Error; err != nil {
			log.ErrorE("get artifact error", err)
			continue
		}
		// 如果配置了最大版本数量，则清理历史版本
		if artifact.MaxVersions <= 0 {
			continue
		}
		// 查询制品版本, 根据创建时间降序排列, 查出来的是可以删除的版本
		var artifactVersions []models.ArtifactVersion
		if err := engine.Where("artifact_uuid = ? and deleted = false",
			artifactUUID).Order("gmt_create desc").
			Limit(10000).Offset(int(artifact.MaxVersions)). // nolint
			Find(&artifactVersions).Error; err != nil {
			log.ErrorE("get artifact versions error", err)
			continue
		}
		// 如果没有需要清理的版本，则继续下一个制品
		if len(artifactVersions) == 0 {
			continue
		}
		// 获取需要删除的版本UUID列表
		var versionUUIDs []string
		for _, version := range artifactVersions {
			versionUUIDs = append(versionUUIDs, version.UUID)
		}
		if err := artifactService.VersionDelete(versionUUIDs,
			true, "auto purge version"); err != nil {
			log.ErrorE("delete artifact versions error", err)
		}
		// 更新制品的版本数量
		versions := artifact.Versions - uint64(len(artifactVersions))
		if err := engine.Model(&artifact).Update("versions",
			versions).Error; err != nil {
			log.ErrorE("update artifact versions error", err)
		}
	}
	return nil
}
