package executor

import (
	"context"
	"errors"
	"fmt"
	"pipeline/pkg/notify/types"
	"time"

	"pipeline/internal/runner/step/tool"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/queue"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"gorm.io/gorm"
)

// Engine is the core of pipeline execution
type Engine struct{}

// NewEngine 创建流水线引擎
func NewEngine() *Engine {
	return &Engine{}
}

// ExecutePipeline 执行流水线
func (r *Engine) ExecutePipeline(ctx *pipeline.ExecuteContext) error {
	// 更新流水线状态为运行中
	if err := eventbus.Publish(context.Background(), &events.PipelineLoggerStatusUpdateEvent{
		DB:               mysql.GetDB(),
		TxUUID:           ctx.TxUuid,
		TargetStatus:     pipeline.Running,
		ExpectedStatuses: []string{pipeline.Waiting},
	}); err != nil {
		return err
	}
	// 检查状态
	if len(ctx.PipelineStageMatrix.Stages) <= 0 {
		return errors.New("pipeline stage is empty")
	}
	// 流水线状态变更通知
	eventbus.PublishAsync(context.Background(), &events.PipelineStatusUpdateNotifyEvent{
		ExecuteContext: ctx,
		TxUUID:         ctx.TxUuid,
		Status:         pipeline.Running,
	})
	// 清理日志
	eventbus.PublishAsync(context.Background(), &events.PipelineExecuteCleanPreLogEvent{
		ExecuteContext: ctx,
	})
	return r.ExecuteStage(ctx, ctx.PipelineStageMatrix.Stages[0])
}

// ExecuteStage 执行阶段
func (r *Engine) ExecuteStage(ctx *pipeline.ExecuteContext, stage pipeline.StageMatrix) error {

	// 检查当前阶段是否已经被执行
	tryFunc := func(columns map[string]any, prevState string) error {
		if ret := mysql.GetDB().Model(models.StageState{}).Where("tx_uuid = ? and stage_idx = ? and state = ?",
			ctx.TxUuid, stage.StageIdx, prevState).
			UpdateColumns(columns); ret.Error != nil {
			log.Error("update stage state error", log.Any("err", ret.Error))
			return ret.Error
		} else if ret.RowsAffected <= 0 { // 没有更新到数据说明已经被其他节点执行
			log.Info("stage already executed", log.Any("state", stage))
			return errors.New("stage already executed")
		}
		return nil
	}

	startSteps := make([]map[string]any, 0)

	for _, row := range stage.StepRows {
		// get the first step of each row
		startSteps = append(startSteps, row[0])
	}

	// check stage trigger mode: manual or auto
	// if stage trigger mode is auto , put the stage all rows first node into waiting queue
	if stage.StageDefine.Trigger == pipeline.AutoTrigger || (stage.StageDefine.Trigger == pipeline.ManualTrigger && ctx.StageTrigger != "") { // trigger by user
		prevState := pipeline.Waiting
		columns := map[string]any{
			"state":    pipeline.Running,
			"end_time": time.Now().Unix(),
		}
		// 手动触发
		if ctx.StageTrigger != "" {
			prevState = pipeline.Ready
			columns["content"] = util.ToJSONString(map[string]any{
				"operator": ctx.StageTrigger,
			})
		}
		// update stage status to running
		if err := tryFunc(columns, prevState); err != nil {
			return nil
		}
		for i, step := range startSteps {
			err := r.ExecuteStep(ctx, step, pipeline.StepIndex{
				StageIdx:   stage.StageIdx,
				StepRowIdx: i,
			})
			if err != nil {
				log.ErrorE("execute step error", err)
			}
		}
		return nil
	}

	// if manual, waiting user trigger
	if stage.StageDefine.Trigger == pipeline.ManualTrigger {
		columns := map[string]any{
			"state":      pipeline.Ready,
			"start_time": time.Now().Unix(),
		}
		// update stage status to ready
		if err := tryFunc(columns, pipeline.Waiting); err != nil {
			return nil
		}
	}

	return nil
}

// ExecuteStep 执行步骤
func (r *Engine) ExecuteStep(ctx *pipeline.ExecuteContext, step map[string]any, index pipeline.StepIndex) error {

	// 尝试更新当前step状态为pending
	if ret := mysql.GetDB().Model(models.StepState{}).
		Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
			ctx.TxUuid, index.StageIdx, index.StepRowIdx, index.StepIdx, pipeline.Waiting).
		Update("state", pipeline.Pending); ret.Error != nil {
		log.Error("update step state error", log.Any("err", ret.Error))
		return ret.Error
	} else if ret.RowsAffected <= 0 { // 没有更新到数据说明已经被其他节点执行
		log.Info("step already executed", log.Any("index", index))
		return nil
	}

	stepCtx := ctx.StepCtx
	run, err := pipeline.ParseRunConfig(step["run"])
	if err != nil {
		return err
	}
	stepCtx.Step = step["step"].(string)
	stepCtx.StepDefine = step
	stepCtx.Index = index
	// 设置失败策略
	stepCtx.Strategy = ctx.PipelineStageMatrix.Stages[index.StageIdx].StageDefine.Strategy

	// 人工卡点
	if stepCtx.Step == pipeline.StepManualValidate {
		return r.ExecuteApproveStep(ctx, step, index)
	}

	// 动态参数输入step
	if stepCtx.Step == pipeline.StepDynamicEnvInput {
		return r.ExecuteDynamicEnvInput(ctx, step, index)
	}

	// normal step, add to task queue
	queue.PushTask(run.Runner, &queue.StepTask{Ctx: stepCtx})

	return nil
}

// ExecuteNext 执行下一个步骤任务
func (r *Engine) ExecuteNext(txUUID, state, message string, index pipeline.StepIndex) error {

	ctx := context.Background()
	engine := mysql.GetDB()

	// 清理超时队列
	eventbus.PublishAsync(ctx, &events.CancelListenPipelineStepRunTimeoutEvent{
		TxUUID: txUUID, Indexes: []pipeline.StepIndex{index},
	})

	// 检查流水线是否已经执行完成(用于通知runner清理，触发下一个阻塞的流水线)
	defer eventbus.PublishAsync(ctx, &events.CheckPipelineRunFinishEvent{
		TxUUID:    txUUID,
		StageIdx:  index.StageIdx,
		StepState: state,
	})

	// 更新当前行后续step状态为取消状态
	if state == pipeline.Failed || state == pipeline.Cancel {
		_ = eventbus.Publish(ctx, &events.PipelineStepRowStateUpdateEvent{
			TxUUID:    txUUID,
			StepIndex: index,
			State:     pipeline.Cancel,
			PrevState: pipeline.Waiting,
		})
	}

	// 流水线执行失败
	if state == pipeline.Failed {
		// 开启事务
		_ = engine.Transaction(func(tx *gorm.DB) error {
			if err := eventbus.Publish(ctx, &events.PipelineLoggerStatusUpdateEvent{
				DB:               tx,
				TxUUID:           txUUID,
				ExpectedStatuses: []string{pipeline.Running},
				TargetStatus:     pipeline.Failed,
			}); err != nil {
				return err
			}
			// 设置成功需要发送通知
			eventbus.PublishAsync(context.Background(), &events.PipelineStatusUpdateNotifyEvent{
				StepIndex:    &index,
				TxUUID:       txUUID,
				Message:      message,
				Status:       pipeline.Failed,
				TemplateType: types.FailedPipeline,
			})
			var failStrategy string
			if err := tx.Model(models.StageState{}).Where("tx_uuid = ? and stage_idx = ?", txUUID, index.StageIdx).
				Pluck("strategy", &failStrategy).Error; err != nil {
				return err
			}
			// 如果当前阶段是快速失败，当前所有待执行、正在执行的step更新为取消状态
			if failStrategy == pipeline.FastStrategy {
				query := tx.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and state in (?)",
					txUUID, index.StageIdx, []string{pipeline.Waiting, pipeline.Pending, pipeline.Running})
				// 发送通知
				var stepIndexes []pipeline.StepIndex
				if err := query.Select("stage_idx", "step_row_idx", "step_idx", "state", "end_time").
					Find(&stepIndexes).Error; err == nil {
					eventbus.PublishAsync(ctx, &events.StepStateUpdateEvent{
						TxUUID:      txUUID,
						StepIndexes: stepIndexes,
						State:       pipeline.Cancel,
					})
				}
				// 移除超时队列 stepIndexes
				eventbus.PublishAsync(ctx, &events.CancelListenPipelineStepRunTimeoutEvent{
					TxUUID:  txUUID,
					Indexes: stepIndexes,
				})
				// 更新step状态
				if err := query.UpdateColumns(map[string]any{
					"state":    pipeline.Cancel,
					"end_time": time.Now().Unix(),
				}).Error; err != nil {
					return err
				}
				// 移除队列中的任务
				queueSteps := queue.RemoveTask(txUUID)
				log.Info("remove queue steps", log.Any("steps", queueSteps))
			}
			return nil
		})
	}

	// 继续执行下一个任务（需要保证并发安全：只能让一个实例执行一次）
	if state == pipeline.Success || state == pipeline.Skipped {
		// 开启事务
		_ = engine.Transaction(func(tx *gorm.DB) error {
			// 查询阶段
			var stage models.StageState
			if err := tx.Model(models.StageState{}).Where("tx_uuid = ? and stage_idx = ?",
				txUUID, index.StageIdx).First(&stage).Error; err != nil {
				return err
			}
			var loggerInfo models.PipelineLogger
			if err := tx.Model(models.PipelineLogger{}).Where("uuid = ?",
				txUUID).First(&loggerInfo).Error; err != nil {
				return err
			}
			// 如果流水线已经结束(失败、取消、终止)则不执行下一阶段，当前阶段的step根据策略执行
			if loggerInfo.Status != pipeline.Running {
				// 如果是快速失败策略，直接返回
				if stage.Strategy == pipeline.FastStrategy {
					return nil
				}
				// 否则是自然失败策略，因为当前行是执行成功的所以继续执行当前行的下一个step
				return r.ExecuteNextStep(txUUID, index)
			}
			// 如果流水线还在执行中，继续执行下一个阶段或者下个step
			return r.ExecuteNextStageOrStep(txUUID, index)
		})
		// 当前流水线的所有step状态都执行完成，更新流水线状态为成功
		err := engine.Transaction(func(tx *gorm.DB) error {
			var count int64
			if err := tx.Model(models.StepState{}).Where("tx_uuid = ? and state in (?)", txUUID,
				[]string{pipeline.Pending, pipeline.Waiting, pipeline.Running, pipeline.Failed}).
				Count(&count).Error; err != nil {
				return err
			}
			if count > 0 {
				return fmt.Errorf("stage step is running, skip exec next stage")
			}

			return eventbus.Publish(ctx, &events.PipelineLoggerStatusUpdateEvent{
				DB:               tx,
				TxUUID:           txUUID,
				ExpectedStatuses: []string{pipeline.Running},
				TargetStatus:     pipeline.Success,
			})
		})
		// 设置成功需要发送通知
		if err != nil {
			log.Info("pipeline status is not running, skip exec next stage")
			return nil
		}
		eventbus.PublishAsync(context.Background(), &events.PipelineStatusUpdateNotifyEvent{
			Status: pipeline.Success,
			TxUUID: txUUID,
		})
	}

	return nil
}

// ExecuteNextStep 执行下一个步骤任务
func (r *Engine) ExecuteNextStep(txUUID string, index pipeline.StepIndex) error {
	// 如果没有下一个step，且pipeline的状态是running，判断是否所有step都执行完毕，如果是则设置pipeline状态为成功
	pipelineContext, err := RenewPipelineContext(txUUID)
	if err != nil {
		return err
	}
	// 下一个即将执行的step
	index.StepIdx++
	// 检查是否有下一个step
	if stepDefine := pipelineContext.PipelineStageMatrix.Get(index); stepDefine != nil {
		return r.ExecuteStep(pipelineContext, stepDefine, index)
	}
	return nil
}

// ExecuteNextStageOrStep 执行下一个阶段或者下一个步骤
func (r *Engine) ExecuteNextStageOrStep(txUUID string, index pipeline.StepIndex) error {
	// 重新构建pipeline上下文
	pipelineContext, err := RenewPipelineContext(txUUID)
	if err != nil {
		return err
	}
	// 下一个即将执行的step
	nextIndex := pipeline.StepIndex{
		StageIdx:   index.StageIdx,
		StepRowIdx: index.StepRowIdx,
		StepIdx:    index.StepIdx + 1,
	}
	// 获取下一个step为空尝试去执行下一个stage
	if stepDefine := pipelineContext.PipelineStageMatrix.Get(nextIndex); stepDefine == nil {
		// 没有后续step了, 尝试查找下一个stage
		nextStageIdx := index.StageIdx + 1
		if nextStageIdx < len(pipelineContext.PipelineStageMatrix.Stages) {
			stageDefine := pipelineContext.PipelineStageMatrix.Stages[nextStageIdx]
			// 需要判断当前阶段的所有step是否已经执行完成
			var count int64
			if err = mysql.GetDB().Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and state in (?)",
				txUUID, index.StageIdx, []string{pipeline.Pending, pipeline.Waiting, pipeline.Running}).Count(&count).Error; err != nil {
				return err
			}
			// 如果当前阶段还有step没有执行完成，则不执行下一个阶段
			if count > 0 {
				return nil
			}
			return r.ExecuteStage(pipelineContext, stageDefine)
		}
		return nil
	} else {
		// 找到下一个step, 直接执行
		return r.ExecuteStep(pipelineContext, stepDefine, nextIndex)
	}
}

// ExecuteApproveStep 执行人工卡点
func (r *Engine) ExecuteApproveStep(executeContext *pipeline.ExecuteContext, step map[string]any, index pipeline.StepIndex) error {
	// 解析当前step的配置
	stepDefine, _ := tool.ParseTaskDiyValidateTbDefine(step)
	// 构造审批数据
	approveUsers := make([]tool.ApproveUser, 0)
	for _, user := range stepDefine.Reviewer {
		approveUsers = append(approveUsers, tool.ApproveUser{
			Name:   user,
			Status: pipeline.Waiting,
			Time:   nil,
		})
	}
	// 序列化成json
	content := util.ToJSONString(tool.ApproveStepContent{
		ApproveUsers:         approveUsers,
		ManualValidateConfig: stepDefine,
	})
	// 尝试更新当前step状态为running
	if ret := mysql.GetDB().Model(models.StepState{}).
		Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
			executeContext.TxUuid, index.StageIdx, index.StepRowIdx, index.StepIdx, pipeline.Pending).
		UpdateColumns(map[string]any{
			"state":      pipeline.Running,
			"content":    content,
			"start_time": time.Now().Unix(),
		}); ret.Error != nil {
		log.Error("update step state error", log.Any("err", ret.Error))
		return ret.Error
	} else if ret.RowsAffected <= 0 { // 没有更新到数据说明已经被其他节点执行
		log.Info("step already executed", log.Any("index", index))
		return nil
	}
	ctx := context.Background()
	// 发送通知
	eventbus.PublishAsync(ctx, &events.StepStateUpdateEvent{
		ExecuteContext: executeContext,
		TxUUID:         executeContext.TxUuid,
		StepIndexes:    []pipeline.StepIndex{index},
		State:          pipeline.Running,
	})
	// 监听step执行超时
	eventbus.PublishAsync(ctx, &events.ListenPipelineStepRunTimeoutEvent{
		Index:              index,
		TxUUID:             executeContext.TxUuid,
		StepDefine:         step,
		Time:               time.Now(),
		DefaultSkipTimeout: true,
	})
	// 发送审批通知
	return eventbus.Publish(context.Background(), &events.ApproveStepNoticeEvent{
		StepDefine:     stepDefine,
		ExecuteContext: executeContext,
	})
}

// ExecuteDynamicEnvInput 执行动态环境变量输入
func (r *Engine) ExecuteDynamicEnvInput(executeContext *pipeline.ExecuteContext, step map[string]any, index pipeline.StepIndex) error {
	// 尝试更新当前step状态为running
	if ret := mysql.GetDB().Model(models.StepState{}).
		Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
			executeContext.TxUuid, index.StageIdx, index.StepRowIdx, index.StepIdx, pipeline.Pending).
		UpdateColumns(map[string]any{
			"state":      pipeline.Running,
			"start_time": time.Now().Unix(),
		}); ret.Error != nil {
		log.Error("update step state error", log.Any("err", ret.Error))
		return ret.Error
	} else if ret.RowsAffected <= 0 { // 没有更新到数据说明已经被其他节点执行
		log.Info("step already executed", log.Any("index", index))
	}
	ctx := context.Background()
	// 更新step状态
	eventbus.PublishAsync(ctx, &events.StepStateUpdateEvent{
		ExecuteContext: executeContext,
		TxUUID:         executeContext.TxUuid,
		StepIndexes:    []pipeline.StepIndex{index},
		State:          pipeline.Running,
	})
	// 监听step执行超时
	eventbus.PublishAsync(ctx, &events.ListenPipelineStepRunTimeoutEvent{
		Index:              index,
		TxUUID:             executeContext.TxUuid,
		StepDefine:         step,
		Time:               time.Now(),
		DefaultSkipTimeout: true,
	})
	return nil
}
