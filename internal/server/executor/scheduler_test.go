package executor

import (
	"context"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"gorm.io/gorm/logger"
	"pipeline/config"
	"pipeline/pkg/oss"
	"testing"
)

func TestListenPipelineLoggerActionTransfer(t *testing.T) {
	t.Skip()
	config.Load()
	// 缺省数据库
	cnf := mysql.NewConfig(
		config.Items().Mysql.User,
		config.Items().Mysql.Pwd,
		config.Items().Mysql.Host,
		config.Items().Mysql.Port,
		config.Items().Mysql.DBName,
		config.Items().Mysql.Charset,
		logger.Info)
	mysql.Register(cnf)
	defer mysql.Close()
	// 初始化oss服务
	oss.Initialize()
	ctx := context.Background()
	ListenPipelineLoggerActionTransfer(ctx)
}
