package executor

import (
	"context"
	"fmt"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"github.com/robfig/cron/v3"
	"pipeline/pkg/models"
	"time"

	"pipeline/config"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/queue"

	"git.makeblock.com/makeblock-go/log"
)

// ListenPipelineStepRunTimeout 监听流水线步骤执行超时
func ListenPipelineStepRunTimeout(ctx context.Context) {
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("%v", r)
			log.ErrorE("Listen pipeline step run timeout panic: %v", err)
		}
	}()
	// 监听流水线步骤执行超时,每分钟检查一次
	stepTimeoutCheckInterval := config.Items().Pipeline.StepTimeoutCheckInterval
	log.Info(fmt.Sprintf("Listen pipeline step run timeout started, check interval: %s", stepTimeoutCheckInterval))
	t := time.NewTicker(stepTimeoutCheckInterval)
	// 初始化超时队列
	timeoutQueue := queue.NewPipelineStepTimeoutQueue()
	for {
		select {
		case <-ctx.Done():
			t.Stop()
			log.Info("Listen pipeline step run timeout stopped")
			return
		case <-t.C:
			// 1. 从超时队列中获取超时任务
			tasks, ok := timeoutQueue.Pop()
			if !ok {
				//log.Info("No timeout task found in the queue")
				continue
			}
			// 2. 发布超时事件
			for _, task := range tasks {
				log.Info(fmt.Sprintf("Task %s at index %d has timed out", task.TxUUID, task.Index))
				// 在这里发布你的超时事件
				eventbus.PublishAsync(ctx, &events.PipelineStepRunTimeoutEvent{
					TxUUID: task.TxUUID,
					Index:  task.Index,
				})
			}
		}
	}
}

// ListenPipelineLoggerActionTransfer 监听未完成的流水线日志转移
func ListenPipelineLoggerActionTransfer(ctx context.Context) {
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("%v", r)
			log.ErrorE("Listen pipeline logger action transfer panic: %v", err)
		}
	}()
	// 创建一个定时任务
	transferActionLoggerCron := config.Items().Pipeline.TransferActionLoggerCron
	if transferActionLoggerCron == "" {
		log.Warn("Transfer action logger cron is empty, skip listen pipeline logger action transfer")
		return
	}
	log.Info(fmt.Sprintf("Listen pipeline logger action transfer started, cron: %s", transferActionLoggerCron))
	// 创建一个新的cron调度器
	c := cron.New()
	// 添加定时任务
	if _, err := c.AddFunc(transferActionLoggerCron, func() {
		log.Info("Starting pipeline logger action transfer...")
		// 获取数据库连接
		engine := mysql.GetDB()
		// 查询未完成的流水线日志转移
		var txUUIDList []string
		// 默认查询前一天的未完成的流水线日志转移
		cutoffTime := time.Now().AddDate(0, 0, -1)
		if err := engine.Model(&models.ActionState{}).
			Select("DISTINCT SUBSTRING_INDEX(log_filename, '-', 1) as tx_uuid").
			Where("log_in_storage = false and start_time < ?", cutoffTime.Unix()).
			Find(&txUUIDList).Error; err != nil {
			log.ErrorE("Failed to query pipeline logger action state: %v", err)
			return
		}
		for _, txUUID := range txUUIDList {
			// empty txUUID
			if txUUID == "" {
				continue
			}
			// 发布日志转移事件
			if err := eventbus.Publish(ctx, &events.PipelineTransferLogsEvent{
				TxUUID: txUUID,
				Force:  true, // 文件不存在时删除
			}); err != nil {
				log.ErrorE("Failed to publish pipeline transfer logs event: %v", err)
				return
			}
			log.Info(fmt.Sprintf("Successed transfer logs event for txUUID: %s", txUUID))
		}
		// 针对数据库状态已经更新为true，但是实际的日志文件还在dbfs中
		if err := eventbus.Publish(ctx, &events.LogTransferReconciliationEvent{
			Before: cutoffTime,
		}); err != nil {
			log.ErrorE("Failed to publish pipeline transfer logs event: %v", err)
			return
		}

	}); err != nil {
		log.ErrorE("Failed to add cron job: %v", err)
		return
	}
	// 启动cron调度器
	c.Start()
	// 等待上下文取消
	<-ctx.Done()
	c.Stop()
	log.Info("Listen pipeline logger action transfer stopped")
}
