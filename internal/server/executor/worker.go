package executor

import (
	"git.makeblock.com/makeblock-go/log"
	"pipeline/pkg/pipeline"
)

var pipelineDispatcher *Dispatcher

type Job struct {
	ctx     *pipeline.ExecuteContext
	JobFunc func()
}

type Worker struct {
	ID            int
	WorkerChannel chan chan Job
	Channel       chan Job
	End           chan struct{}
}

func startWorker(workerList chan chan Job, i int) Worker {
	worker := Worker{
		ID:            i,
		WorkerChannel: workerList,
		Channel:       make(chan Job),
		End:           make(chan struct{}),
	}
	go func() {
		for {
			worker.WorkerChannel <- worker.Channel
			select {
			case job := <-worker.Channel:
				job.JobFunc()
			case <-worker.End:
				return
			}
		}
	}()
	return worker
}

type Dispatcher struct {
	WorkerPool chan chan Job
	MaxWorkers int
	Workers    []Worker
	JobQueue   chan Job
}

func NewDispatcher(maxWorkers int) *Dispatcher {
	pool := make(chan chan Job, maxWorkers)
	workers := make([]Worker, maxWorkers)
	jobQueue := make(chan <PERSON>, 100) // Adjust size as needed
	dispatcher := Dispatcher{WorkerPool: pool, MaxWorkers: maxWorkers, Workers: workers, JobQueue: jobQueue}
	return &dispatcher
}

func (d *Dispatcher) Run() {
	for i := 0; i < d.MaxWorkers; i++ {
		worker := startWorker(d.WorkerPool, i)
		d.Workers[i] = worker
	}
	go d.dispatch()
}

func (d *Dispatcher) Stop() {
	for _, worker := range d.Workers {
		worker.End <- struct{}{}
	}
}

func (d *Dispatcher) SubmitJob(job Job) {
	d.JobQueue <- job
}

func (d *Dispatcher) dispatch() {
	for job := range d.JobQueue {
		go func(job Job) {
			workerJobQueue := <-d.WorkerPool
			workerJobQueue <- job
		}(job)
	}
}

func InitializeWorker() *Dispatcher {
	pipelineDispatcher = NewDispatcher(10)
	pipelineDispatcher.Run()
	return pipelineDispatcher
}

func buildJob(opt *pipeline.PipelineContextOption) (*Job, error) {
	ctx, err := NewPipelineContext(opt)
	if err != nil {
		log.ErrorE("error building pipeline context : %v", err)
		return nil, err
	}
	return &Job{
		ctx: ctx,
		JobFunc: func() {
			if err = NewEngine().ExecutePipeline(ctx); err != nil {
				log.ErrorE("error executing pipeline : %v", err)
			}
		},
	}, nil
}
