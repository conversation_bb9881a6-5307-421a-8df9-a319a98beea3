package executor

import (
	"context"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/hex"
	"encoding/json"
	"strings"

	"pipeline/internal/server/runner"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"

	"connectrpc.com/connect"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"golang.org/x/crypto/pbkdf2"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const (
	uuidHeaderKey  = "x-runner-uuid"
	tokenHeaderKey = "x-runner-token"
)

var withRunner = connect.WithInterceptors(connect.UnaryInterceptorFunc(func(unaryFunc connect.UnaryFunc) connect.UnaryFunc {
	return func(ctx context.Context, request connect.AnyRequest) (connect.AnyResponse, error) {

		methodName := getMethodName(request)
		// Register/Ping method doesn't need to check runner token
		if methodName == "Register" || methodName == "Ping" {
			return unaryFunc(ctx, request)
		}
		uuid := request.Header().Get(uuidHeaderKey)
		token := request.Header().Get(tokenHeaderKey)

		// get from redis cache
		runnerEntry, err := GetCacheRunner(ctx, uuid)

		// if not found in cache, get from db
		if err != nil {
			// get from db
			runnerEntry, err := runner.NewRunnerRepo(mysql.GetDB()).GetRunnerByUUID(uuid)
			if err != nil {
				return nil, status.Error(codes.Unauthenticated, "unregistered runner")
			}
			// check token
			if subtle.ConstantTimeCompare([]byte(runnerEntry.TokenHash), []byte(HashToken(token, runnerEntry.TokenSalt))) != 1 {
				return nil, status.Error(codes.Unauthenticated, "unregistered runner")
			}
			// set to cache
			if err := eventbus.Publish(ctx, &events.PipelineRunnerUpdateEvent{
				PipelineRunner: runnerEntry,
			}); err != nil {
				return nil, err
			}
		}

		ctx = context.WithValue(ctx, runnerCtxKey{}, runnerEntry)

		return unaryFunc(ctx, request)
	}
}))

func runnerKey(uuid string) string {
	return "pipeline:runner:" + uuid
}

func GetCacheRunner(ctx context.Context, uuid string) (*models.PipelineRunnerCacheEntry, error) {
	result, err := redis.GetClient().Get(ctx, runnerKey(uuid)).Result()
	if err != nil {
		return nil, err
	}
	// unmarshal
	var runner *models.PipelineRunnerCacheEntry
	if err := json.Unmarshal([]byte(result), &runner); err != nil {
		return nil, err
	}
	return runner, nil
}

func getMethodName(req connect.AnyRequest) string {
	splits := strings.Split(req.Spec().Procedure, "/")
	if len(splits) > 0 {
		return splits[len(splits)-1]
	}
	return ""
}

// HashToken return the hashable salt
func HashToken(token, salt string) string {
	tempHash := pbkdf2.Key([]byte(token), []byte(salt), 10000, 50, sha256.New)
	return hex.EncodeToString(tempHash)
}

type runnerCtxKey struct{}

func GetRunner(ctx context.Context) *models.PipelineRunnerCacheEntry {
	if v := ctx.Value(runnerCtxKey{}); v != nil {
		if r, ok := v.(*models.PipelineRunnerCacheEntry); ok {
			return r
		}
	}
	return nil
}
