package executor

import (
	"encoding/json"
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"gorm.io/gorm"
	"pipeline/config"
	"pipeline/internal/runner/step"
	"pipeline/internal/runner/step/factory"
	"pipeline/internal/server/app"
	"pipeline/internal/server/app/plugins"
	"pipeline/internal/server/credential"
	"pipeline/internal/server/logger"
	"pipeline/internal/server/pipeline"
	"pipeline/internal/server/secret"
	"pipeline/internal/server/source"
	"pipeline/pkg/common"
	"pipeline/pkg/models"
	pkgPipeline "pipeline/pkg/pipeline"
	"pipeline/pkg/util"
	"strings"
	"time"
)

// PipelineContextBuilder is used to build the ExecuteContext step by step.
type PipelineContextBuilder struct {
	opt            *pkgPipeline.PipelineContextOption
	dbEngine       *gorm.DB
	pipelineSvc    *pipeline.InfoService
	loggerSvc      *logger.LoggerService
	secretSvc      *secret.SecretService
	pipelineInfo   *models.PipelineInfo
	pipelineDefine *pkgPipeline.PipelineDefine
	stageMatrix    *pkgPipeline.PipelineStageMatrix
	projectApp     *models.ProjectApp
	codeSource     *source.ProjectAppCodeSource
	loggerModel    *models.PipelineLogger
	secretList     []*secret.PipelineSecret
	credentials    map[string]*credential.PipelineCredential
	configPlugins  map[string]*models.AppConfigEntry
}

// NewPipelineContextBuilder creates a new instance of PipelineContextBuilder.
func newPipelineContextBuilder(opt *pkgPipeline.PipelineContextOption) *PipelineContextBuilder {
	dbEngine := mysql.GetDB()
	return &PipelineContextBuilder{
		opt:         opt,
		dbEngine:    dbEngine,
		pipelineSvc: pipeline.NewInfoService(nil, dbEngine, redis.GetClient()),
		loggerSvc:   logger.NewLoggerService(nil, dbEngine, redis.GetClient()),
		secretSvc:   secret.NewSecretService(nil, dbEngine, redis.GetClient()),
	}
}

func NewPipelineContext(opt *pkgPipeline.PipelineContextOption) (*pkgPipeline.ExecuteContext, error) {
	builder := newPipelineContextBuilder(opt)
	return builder.Build()
}

func RenewPipelineContext(txUUID string) (*pkgPipeline.ExecuteContext, error) {
	builder := newPipelineContextBuilder(&pkgPipeline.PipelineContextOption{TxUUID: txUUID, Renew: true})
	return builder.Build()
}

// Build constructs the ExecuteContext.
func (b *PipelineContextBuilder) Build() (*pkgPipeline.ExecuteContext, error) {
	// if the pipeline is rebuild
	if err := b.loadPipelineContextOption(); err != nil {
		return nil, err
	}
	if err := b.loadPipelineInfo(); err != nil {
		return nil, err
	}
	if err := b.loadPipelineDefine(); err != nil {
		return nil, err
	}
	if err := b.loadProjectApp(); err != nil {
		return nil, err
	}
	if err := b.loadCodeSource(); err != nil {
		return nil, err
	}
	if err := b.loadSecrets(); err != nil {
		return nil, err
	}
	if err := b.loadCheckout(); err != nil {
		return nil, err
	}
	if err := b.savePipelineLogger(); err != nil {
		return nil, err
	}
	if err := b.saveStageStepState(); err != nil {
		return nil, err
	}
	if err := b.loadCertAndConfig(); err != nil {
		return nil, err
	}
	// inject env to pipeline context
	b.injectPipelineEnv()
	// buildContext
	return b.buildContext()
}

// loadPipelineContextOption loads the pipeline context option. when the pipeline is rebuild, it will load the logger model.
func (b *PipelineContextBuilder) loadPipelineContextOption() error {
	// only load the logger model when the pipeline is rebuilt
	if !b.opt.Renew {
		return nil
	}
	model, err := b.loggerSvc.GetByUUID(b.opt.TxUUID)
	if err != nil { // must exist when rebuild
		return err
	}
	if model.ID > 0 {
		b.loggerModel = model
		// 如果是重建ctx则需要从快照中取配置
		var opt pkgPipeline.PipelineContextOption
		if len(model.TriggerContent) > 0 {
			if err = json.Unmarshal([]byte(model.TriggerContent), &opt); err != nil {
				log.ErrorE("unmarshal trigger content error", err)
				return err
			}
			opt.Renew = true
			b.opt = &opt
		}
	}
	return nil
}

func (b *PipelineContextBuilder) loadPipelineInfo() error {
	var err error
	b.pipelineInfo, err = b.pipelineSvc.GetByUUID(b.opt.PipelineUUID)
	return err
}

func (b *PipelineContextBuilder) loadPipelineDefine() error {
	var err error
	var content string
	// 如果是重建ctx需要从快照中取
	if b.opt.Renew {
		content = b.loggerModel.PipelineSnapshot
	} else {
		content = b.pipelineInfo.Content
	}
	b.pipelineDefine, err = pkgPipeline.ParsePipeline(content)
	if err != nil {
		return err
	}
	b.stageMatrix = pkgPipeline.ParseStepMatrix(b.pipelineDefine.Stages)
	if b.stageMatrix == nil {
		return pkgPipeline.StageMatrixError
	}
	return nil
}

func (b *PipelineContextBuilder) loadProjectApp() error {
	var err error
	b.projectApp, err = app.NewAppRepo(b.dbEngine).GetByUUID(b.pipelineInfo.AppUuid)
	return err
}

func (b *PipelineContextBuilder) loadCodeSource() error {
	var err error
	b.codeSource, err = source.NewSourceRepo(b.dbEngine).GetByUUID(b.projectApp.Source)
	return err
}

func (b *PipelineContextBuilder) loadSecrets() error {
	var err error
	b.secretList, err = b.secretSvc.List(secret.ListRequest{
		PipelineUuid: b.pipelineInfo.UUID,
		ShowValue:    true,
		Decrypt:      true,
	})
	return err
}

func (b *PipelineContextBuilder) savePipelineLogger() error {
	// if the pipeline is rebuild, the logger model is already created.
	if b.opt.Renew {
		return nil
	}
	now := time.Now()
	status := pkgPipeline.Waiting
	if b.pipelineDefine.Blocking {
		status = pkgPipeline.Blocking
	}
	// 序列化opt
	triggerContent, err := json.Marshal(b.opt)
	if err != nil {
		return err
	}
	// 更新流水线构建号
	number, err := b.pipelineSvc.IncrementBuildNumber(b.pipelineInfo.UUID, status)
	if err != nil {
		return err
	}
	m := &models.PipelineLogger{
		UUID:             b.opt.TxUUID,
		Status:           status,
		GmtCreate:        &now,
		TriggerTime:      &now,
		PipelineUuid:     b.pipelineInfo.UUID,
		PipelineSnapshot: b.pipelineInfo.Content,
		TriggerWay:       b.opt.TriggerWay,
		TriggerUser:      b.opt.TriggerUser,
		TriggerContent:   string(triggerContent),
		BuildNumber:      number,
	}
	b.loggerModel, err = b.loggerSvc.Create(m)
	return err
}

func (b *PipelineContextBuilder) saveStageStepState() error {

	// if the pipeline is rebuild, the stage and step state is already created.
	if b.opt.Renew {
		return nil
	}

	stageState := make([]models.StageState, 0)
	stepState := make([]models.StepState, 0)
	// get all stage and step
	for stageIdx, stage := range b.pipelineDefine.Stages {
		rows := pkgPipeline.DependsOn(stage.Steps)
		for stepRowIdx, row := range rows {
			for stepIdx := range row {
				stepState = append(stepState, models.StepState{
					TxUuid:     b.loggerModel.UUID,
					State:      pkgPipeline.Waiting,
					StepIdx:    stepIdx,
					StepRowIdx: stepRowIdx,
					StageIdx:   stageIdx,
				})
			}
		}
		// 阶段
		stageState = append(stageState, models.StageState{
			TxUuid:   b.loggerModel.UUID,
			State:    pkgPipeline.Waiting,
			StageIdx: stageIdx,
			Strategy: stage.Strategy,
			Trigger:  stage.Trigger,
		})
	}
	// save
	return mysql.GetDB().Transaction(func(tx *gorm.DB) error {
		if err := tx.Save(&stageState).Error; err != nil {
			return err
		}
		return tx.Save(&stepState).Error
	})
}

func (b *PipelineContextBuilder) loadCertAndConfig() error {
	certUUIDList := make([]string, 0)
	configUUIDList := make([]string, 0)
	// 遍历pipeline的step检查是否需要凭证
	for _, stage := range b.pipelineDefine.Stages {
		for _, stepMap := range stage.Steps {
			// 解析step
			stepConfig := factory.GetStepConfig(stepMap)
			if stepConfig != nil {
				// 获取凭证
				certUUIDList = append(certUUIDList, stepConfig.GetCredentialKey()...)
				// 获取配置
				if sc, ok := stepConfig.(step.StepConfigPlugin); ok {
					configUUIDList = append(configUUIDList, sc.GetConfigPluginKey()...)
				}
			}
		}
	}
	// 查询凭证
	certMap := make(map[string]*credential.PipelineCredential)
	if len(certUUIDList) > 0 {
		list, err := credential.NewCredentialRepo(mysql.GetDB()).GetByUUIDList(credential.ListRequest{
			UUID: certUUIDList, Decrypt: true, ExcludeDeleted: true,
		})
		if err != nil {
			log.Error(err.Error())
			return err
		}
		for _, c := range list {
			certMap[c.UUID] = c
		}
	}
	// 查询配置
	configMap := make(map[string]*models.AppConfigEntry)
	if len(configUUIDList) > 0 {
		list, err := plugins.NewConfigRepo(mysql.GetDB()).
			GetConfigEntryList(configUUIDList, string(models.ApproveStatePassed), b.projectApp.UUID) // 必须只能是审核通过的配置
		if err != nil {
			log.Error(err.Error())
			return err
		}
		for _, c := range list {
			configMap[c.UUID] = c
		}
	}
	b.credentials, b.configPlugins = certMap, configMap
	return nil
}

func (b *PipelineContextBuilder) injectPipelineEnv() {

	// must check nil
	b.pipelineDefine.Env = util.EnsureMapInit(b.pipelineDefine.Env)

	// 注入基础环境变量
	b.pipelineDefine.Env["APP_NAME"] = b.projectApp.Name
	b.pipelineDefine.Env["APP_IDENTITY"] = b.projectApp.Identity
	b.pipelineDefine.Env["BUILD_NUMBER"] = fmt.Sprintf("%d", b.loggerModel.BuildNumber)
	b.pipelineDefine.Env["PIPELINE_NAME"] = b.pipelineDefine.Name
	b.pipelineDefine.Env["PIPELINE_UUID"] = b.loggerModel.PipelineUuid

	if b.opt != nil {
		// 针对于webhook触发的流水线，需要注入webhook的相关信息
		if b.opt.TriggerWay == pkgPipeline.Webhook && b.opt.WebhookParam != nil {
			for k, v := range b.opt.WebhookParam {
				value := fmt.Sprintf("%v", v)
				if len(value) > pkgPipeline.EnvValueMaxLength {
					value = value[:pkgPipeline.EnvValueMaxLength]
				}
				b.pipelineDefine.Env[k] = value
			}
		}
		// 注入动态选择注入的环境变量
		for k, v := range b.opt.Envs {
			b.pipelineDefine.Env[k] = v
		}
	}
}

func (b *PipelineContextBuilder) getGitEnvMap(checkout pkgPipeline.Checkout) map[string]string {
	envMap := make(map[string]string)
	// 注入环境变量
	envMap["GIT_DEFAULT_BRANCH"] = checkout.DefaultBranch
	// 参数检查
	if checkout.Branch != "" {
		envMap["GIT_BRANCH"] = checkout.Branch
	}
	if checkout.Tag != nil {
		envMap["GIT_TAG"] = *checkout.Tag
	}
	if checkout.Ref != nil {
		envMap["GIT_REF"] = *checkout.Ref
	}
	if checkout.CheckoutSHA != nil {
		envMap["GIT_CHECKOUT_SHA"] = *checkout.CheckoutSHA
	}
	if checkout.CommitMessage != nil {
		envMap["GIT_COMMIT_MESSAGE"] = strings.TrimSuffix(*checkout.CommitMessage, "\n")
	}
	if checkout.Message != nil {
		envMap["GIT_MESSAGE"] = strings.TrimSuffix(*checkout.Message, "\n")
	}
	return envMap
}

func (b *PipelineContextBuilder) loadCheckout() error {
	// 如果是手动运行需要去查询分支的最新commit和commit message 且 不是rebuild的时候
	if b.opt.Renew {
		return nil
	}
	// 默认分支
	b.opt.Checkout.DefaultBranch = b.pipelineDefine.Branch
	b.opt.Checkout.CacheRepo = b.pipelineDefine.CacheRepo
	b.opt.Checkout.Submodule = b.pipelineDefine.Submodule
	b.opt.Checkout.Lfs = b.pipelineDefine.Lfs
	b.opt.Checkout.Depth = b.pipelineDefine.Depth
	// manual need to query
	if b.opt.TriggerWay == pkgPipeline.Manual ||
		b.opt.TriggerWay == pkgPipeline.Webhook ||
		b.opt.TriggerWay == pkgPipeline.Cron {
		// 通过tag查询
		if util.NotEmpty(b.opt.Tag) {
			if tag, err := source.NewCodeSourceService(b.codeSource).GetTag(source.TagOptions{
				ProjectId: b.projectApp.RepoId,
				TagName:   b.opt.Tag,
			}); err != nil {
				return err
			} else {
				b.opt.Checkout.Message = &tag.Message
				b.opt.Checkout.CheckoutSHA = &tag.CommitId
				b.opt.Checkout.CommitMessage = &tag.CommitMessage
				b.opt.Checkout.CommitURL = &tag.CommitURL
				b.opt.Checkout.Tag = &b.opt.Tag
				b.opt.Checkout.Ref = util.String2Ptr(common.RefTags + b.opt.Tag)
				return nil // 通过tag查询不需要再查询branch
			}
		}
		// 通过branch查询
		if util.IsEmpty(b.opt.Branch) {
			b.opt.Branch = b.pipelineDefine.Branch
		}
		branch, err := source.NewCodeSourceService(b.codeSource).GetBranch(source.BranchOptions{
			ProjectId:  b.projectApp.RepoId,
			BranchName: b.opt.Branch,
		})
		if err != nil {
			return err
		}
		b.opt.Checkout.CheckoutSHA = &branch.CommitId
		b.opt.Checkout.CommitMessage = &branch.CommitMessage
		b.opt.Checkout.CommitURL = &branch.CommitURL
		b.opt.Checkout.Ref = util.String2Ptr(common.RefHeads + b.opt.Branch)
		b.opt.Checkout.Branch = b.opt.Branch
	}
	return nil
}

func (b *PipelineContextBuilder) buildContext() (*pkgPipeline.ExecuteContext, error) {
	// server config
	cfg := config.Items()
	// for step
	stepCtx := pkgPipeline.StepContext{
		Sonar: pkgPipeline.Sonar{
			Endpoint: cfg.SonarQube.Endpoint,
			Token:    cfg.SonarQube.Token,
		},
		OSS: pkgPipeline.OSS{
			Endpoint:            cfg.OSS.Minio.Endpoint,
			AccessKey:           cfg.OSS.Minio.AccessKey,
			SecretKey:           cfg.OSS.Minio.SecretKey,
			CachesBucketName:    cfg.OSS.CachesBucketName,
			LoggerBucketName:    cfg.OSS.LoggerBucketName,
			ArtifactsBucketName: cfg.OSS.ArtifactsBucketName,
		},
		App:              b.projectApp,
		Secret:           b.secretList,
		Source:           b.codeSource,
		Pipeline:         b.pipelineInfo,
		Checkout:         b.opt.Checkout,
		Credentials:      b.credentials,
		ConfigPluginMap:  b.configPlugins,
		TriggerUser:      b.opt.TriggerUser,
		TxUUID:           b.loggerModel.UUID,
		Debug:            b.pipelineDefine.Debug,
		BuildNumber:      b.loggerModel.BuildNumber,
		MaxArtifactSize:  cfg.Pipeline.MaxArtifactSize,
		ActionMaxRetries: cfg.Pipeline.ActionMaxRetries,
		CacheType:        pkgPipeline.CacheType(cfg.Pipeline.CacheType),
		Env:              util.MergeMap(b.pipelineDefine.Env, b.getGitEnvMap(b.opt.Checkout)),
	}
	// execute
	executeContext := &pkgPipeline.ExecuteContext{
		TxUuid:              b.loggerModel.UUID,
		PipelineDefine:      b.pipelineDefine,
		PipelineStageMatrix: b.stageMatrix,
		Logger:              b.loggerModel,
		Secrets:             b.secretList,
		App:                 b.projectApp,
		PipelineInfo:        b.pipelineInfo,
		Source:              b.codeSource,
		Credentials:         b.credentials, // 凭证
		Checkout:            b.opt.Checkout,
		TriggerUser:         b.opt.TriggerUser, // 触发人
		StepCtx:             stepCtx,
	}
	// encrypt
	if err := pkgPipeline.EncryptStepContext(&executeContext.StepCtx); err != nil {
		return nil, err
	}
	return executeContext, nil
}
