package executor

import (
	"context"
	"os"

	"pipeline/config"
	"pipeline/pkg/models"
	runnerv1 "pipeline/pkg/proto/runner/v1"
	tmock "pipeline/test/mock"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"gorm.io/gorm"
)

var _ = Describe("Runner Metrics", func() {
	var (
		ctx      context.Context
		rediscli *redis.Client
		ms       *miniredis.Miniredis
		db       *gorm.DB
		mock     sqlmock.Sqlmock
		req      *runnerv1.MetricRequest
		err      error
	)

	BeforeEach(func() {
		config.Load()
		ctx = context.Background()
		ms, err = miniredis.Run()
		Expect(err).To(BeNil())
		rediscli = redis.NewClient(&redis.Options{
			Addr: ms.Addr(),
		})

		db, mock, err = tmock.GetDBMock()
		Expect(err).To(BeNil())

		req = &runnerv1.MetricRequest{}
	})

	Context("HealthUpdate", func() {
		// 每次执行完都要清理一下数据
		AfterEach(func() {
			ms.Del(models.RunnerHeartBeat)
		})

		When("runner is not found", func() {
			BeforeEach(func() {
				mock.ExpectQuery("SELECT * FROM `pipeline_runner` WHERE uuid = ? ORDER BY `pipeline_runner`.`id` LIMIT 1").
					WithArgs("uuid1").
					WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "disable", "labels", "token", "token_hash", "token_salt", "version"}).
						AddRow("uuid1", "runner1", false, "labels", "token", "token_hash", "token_salt", "version"))
			})
			It("should add runner to redis", func() {
				req = &runnerv1.MetricRequest{
					RunnerUuid: "uuid1",
				}
				healthUpdate(ctx, rediscli, db, req)
				runnerStr := ms.HGet(models.RunnerHeartBeat, "uuid1")
				entry := &models.RunnerHealthEntry{}
				entry.Unmarshal(runnerStr)
				Expect(entry.Runner.Name).To(Equal("runner1"))
				Expect(entry.TaskCount).To(Equal(0))
			})
		})

		When("runner is found and task count is not zero", func() {
			var activeRunner models.RunnerHealthEntry
			BeforeEach(func() {
				// add runner
				active, err := os.ReadFile("../../../test/testdata/runners/runner_active.json")
				Expect(err).To(BeNil())
				activeRunner.Unmarshal(string(active))

				ms.HSet(models.RunnerHeartBeat, activeRunner.UUID, activeRunner.Marshal())
			})
			It("should update task count", func() {
				req = &runnerv1.MetricRequest{
					RunnerUuid: "uuid2",
					Task: &runnerv1.RunnerTaskMetric{
						Tasks: []*runnerv1.RunnerTask{
							{
								TxUuid: "03e22f60b520442885f8426e61522c37",
							},
						},
					},
				}
				healthUpdate(ctx, rediscli, db, req)
				runnerStr := ms.HGet(models.RunnerHeartBeat, "uuid2")
				entry := &models.RunnerHealthEntry{}
				entry.Unmarshal(runnerStr)
				Expect(entry.TaskCount).To(Equal(1))
			})
		})
	})
})
