package executor

import (
	"context"
	"fmt"
	"io"
	"pipeline/pkg/middleware"
	"time"

	"pipeline/internal/runner/step/tool"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	"pipeline/pkg/pipeline"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Run 手动运行流水线
func Run(c *gin.Context) {
	type EnvEntry struct {
		Name  string
		Value string
	}
	type RunRequest struct {
		UUID   string     `json:"uuid"`
		Envs   []EnvEntry `json:"envs"`   // 指定当前执行流水线的环境变量
		Branch string     `json:"branch"` // 指定当前执行流水线的分支
		Tag    string     `json:"tag"`    // 指定当前执行流水线的tag
	}
	var req RunRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// envs
	envs := make(map[string]string)
	for _, env := range req.Envs {
		envs[env.Name] = env.Value
	}
	// tx uuid
	txUUID := util.UUID()
	user := middleware.GetUserName(c)
	// 触发执行
	if err := eventbus.Publish(c, &events.RunPipelineEvent{
		Opts: pipeline.NewPipelineContextOption(
			pipeline.WithTxUUID(txUUID),
			pipeline.WithPipelineUUID(req.UUID),
			pipeline.WithTriggerWay(pipeline.Manual),
			pipeline.WithTriggerUser(user),
			pipeline.WithEnvs(envs),
			pipeline.WithBranch(req.Branch),
			pipeline.WithTag(req.Tag),
		),
	}); err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, txUUID)
}

// SkipStep 跳过step
func SkipStep(c *gin.Context) {
	type SkipStepRequest struct {
		Index        int    `json:"index"`
		TxUUID       string `json:"txUUID"`
		PipelineUUID string `json:"pipelineUUID"`
		Identity     string `json:"identity"`
		pipeline.StepIndex
	}
	var req SkipStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	engine := mysql.GetDB()
	if err := engine.Transaction(func(tx *gorm.DB) error {
		// step 状态
		if ret := engine.Model(models.StepState{}).
			Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
				req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, pipeline.Running).UpdateColumns(map[string]any{
			"state":    pipeline.Skipped,
			"end_time": time.Now().Unix(),
		}); ret.Error != nil {
			return ret.Error
		} else if ret.RowsAffected <= 0 {
			return fmt.Errorf("step %d-%d-%d-%d already executed", req.StageIdx, req.StepRowIdx, req.StepIdx, req.Index)
		}
		// 发送通知
		eventbus.PublishAsync(context.Background(), &events.StepStateUpdateEvent{
			StepIndexes: []pipeline.StepIndex{req.StepIndex},
			TxUUID:      req.TxUUID,
			State:       pipeline.Skipped,
		})
		return nil
	}); err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// normal cancel, try to execute next step
	if err := NewEngine().ExecuteNext(req.TxUUID, pipeline.Skipped, "", req.StepIndex); err != nil {
		log.Printf("execute next step error: %s", err.Error())
		response.Err(c, response.UnknownError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// Cancel 取消step执行
func Cancel(c *gin.Context) {
	type CancelStepRequest struct {
		pipeline.StepIndex
		TxUUID string `json:"txUUID"`
	}
	var req CancelStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	engine := mysql.GetDB()
	// TODO: must is admin or app owner/member

	// open transaction
	if err := engine.Transaction(func(tx *gorm.DB) error {
		// 设置 step 状态为取消状态
		if ret := tx.Model(models.StepState{}).
			Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
				req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, pipeline.Running).
			UpdateColumns(map[string]any{
				"state":    pipeline.Cancel,
				"end_time": time.Now().Unix(),
			}); ret.Error != nil {
			return ret.Error
		} else if ret.RowsAffected <= 0 {
			return fmt.Errorf("step state already change")
		}
		// 发送通知
		eventbus.PublishAsync(context.Background(), &events.StepStateUpdateEvent{
			StepIndexes: []pipeline.StepIndex{req.StepIndex},
			TxUUID:      req.TxUUID,
			State:       pipeline.Cancel,
		})
		return nil
	}); err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}

	// 后置处理
	eventbus.PublishAsync(c, &events.CancelStepEvent{
		TxUUID:    req.TxUUID,
		StepIndex: req.StepIndex,
		User:      middleware.GetUserName(c),
	})

	// ok
	response.OK(c, nil)
}

// Stage 对stage进行操作(执行)
func Stage(c *gin.Context) {
	type StageRunRequest struct {
		TxUUID   string `json:"txUUID"`
		StageIdx int    `json:"stageIdx"`
	}
	var req StageRunRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// 构建ctx
	context, err := RenewPipelineContext(req.TxUUID)
	context.StageTrigger = middleware.GetUserName(c)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 执行当前stage
	err = NewEngine().ExecuteStage(context, context.PipelineStageMatrix.Stages[req.StageIdx])
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// ok
	response.OK(c, nil)
}

// Terminate 终止流水线执行
func Terminate(c *gin.Context) {
	type TerminateStepRequest struct {
		UUIDList []string `json:"uuidList"`
	}
	var req TerminateStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	for _, uuid := range req.UUIDList {
		if err := eventbus.Publish(c,
			&events.TerminatePipelineEvent{
				UUID: uuid,
				Ops:  middleware.GetUserName(c),
			}); err != nil {
			response.Err(c, response.UnknownError(err.Error()))
			return
		}
	}
	response.OK(c, nil)
}

// Validate 人工卡点验证
func Validate(c *gin.Context) {
	type ApproveStepRequest struct {
		pipeline.StepIndex
		Index    int    `json:"index"`
		UUID     string `json:"uuid"`
		Identity string `json:"identity"`
		Validate string `json:"validate"` //invalidate、validate
	}
	var req ApproveStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	engine := mysql.GetDB()
	// step 状态
	var step models.StepState
	if err := engine.Model(models.StepState{}).
		Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
			req.UUID, req.StageIdx, req.StepRowIdx, req.StepIdx, pipeline.Running).First(&step).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 查询审批人员
	content := util.JSONStringToStruct[tool.ApproveStepContent](step.Content)
	// 配置了审批人员需要检查当前用户是否在审批人员列表中
	email := middleware.GetUserEmail(c)
	if content.HasConfigApproveUsers() {
		if !content.UserInApproves(email) {
			response.Err(c, response.OperatePermissionDeniedError("当前用户不在审批人员列表中"))
			return
		}
	}
	// 清理超时队列
	eventbus.PublishAsync(c, &events.CancelListenPipelineStepRunTimeoutEvent{
		TxUUID:  req.UUID,
		Indexes: []pipeline.StepIndex{req.StepIndex},
	})
	// 审批
	content.Approve(email, req.Validate)
	// 通过审批
	if req.Validate == tool.ValidateStep {
		// 如果是会签需要等待所有人审批通过,或者是或签需要任意一个人审批通过,所以这里先加载当前已经审批的用户
		state := pipeline.Running
		if content.ConditionType == tool.AndConditionType {
			// check if all approve done
			if content.UserApproveDone() {
				state = pipeline.Success
			}
		}
		// 如果是或签需要任意一个人审批通过,如果没有配置审批人直接任意一个人审批通过
		if content.ConditionType == tool.OrConditionType || len(content.Reviewer) == 0 {
			state = pipeline.Success
		}
		columns := map[string]any{
			"state":   state,
			"content": util.ToJSONString(content),
		}
		if state == pipeline.Success {
			columns["end_time"] = time.Now().Unix()
		}
		// 如果所有人审批通过，执行下一个step或者stage
		ret := engine.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
			req.UUID, req.StageIdx, req.StepRowIdx, req.StepIdx, pipeline.Running).UpdateColumns(columns)
		if ret.Error != nil {
			log.Printf("update step state error: %s", ret.Error.Error())
			return
		}
		if ret.RowsAffected <= 0 {
			log.Printf("step %d-%d-%d-%d already executed", req.StageIdx, req.StepRowIdx, req.StepIdx, req.Index)
			return
		}
		// 发送通知
		eventbus.PublishAsync(context.Background(), &events.StepStateUpdateEvent{
			StepIndexes: []pipeline.StepIndex{req.StepIndex},
			TxUUID:      req.UUID,
			State:       state,
		})
		// do next
		if state == pipeline.Success {
			_ = NewEngine().ExecuteNext(req.UUID, state, "", req.StepIndex)
			response.OK(c, nil)
			return
		}
	}
	// 审批不通过
	if req.Validate == tool.InvalidateStep {
		// 验证不通过，当前行后续step以及后续的stage不在执行,但是不影响当前stage的其他行执行, 需要检查pipeline是否已经被取消
		if err := engine.Transaction(func(tx *gorm.DB) error {
			// 更新step状态
			if ret := tx.Model(models.StepState{}).
				Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
					req.UUID, req.StageIdx, req.StepRowIdx, req.StepIdx, pipeline.Running).
				UpdateColumns(map[string]any{
					"state":    pipeline.Failed,
					"end_time": time.Now().Unix(),
					"content":  util.ToJSONString(content),
				}); ret.Error != nil {
				return ret.Error
			} else if ret.RowsAffected <= 0 {
				return fmt.Errorf("step %d-%d-%d-%d already executed", req.StageIdx, req.StepRowIdx, req.StepIdx, req.Index)
			}
			// 发送通知
			eventbus.PublishAsync(context.Background(), &events.StepStateUpdateEvent{
				StepIndexes: []pipeline.StepIndex{req.StepIndex},
				TxUUID:      req.UUID,
				State:       pipeline.Failed,
			})
			return nil
		}); err != nil {
			log.Printf("update step state error: %s", err.Error())
			response.Err(c, response.UnknownError("审批失败"))
			return
		}
		// 更新当前行后续step状态为取消状态
		if err := eventbus.Publish(c, &events.PipelineStepRowStateUpdateEvent{
			TxUUID:    req.UUID,
			StepIndex: req.StepIndex,
			State:     pipeline.Cancel,
			PrevState: pipeline.Waiting,
		}); err != nil {
			log.ErrorE("update step row state error: %s", err)
		}
		// 更新成功尝试更新流水线状态
		if err := engine.Transaction(func(tx *gorm.DB) error {
			return eventbus.Publish(c, &events.PipelineLoggerStatusUpdateEvent{
				DB:               tx,
				TxUUID:           req.UUID,
				ExpectedStatuses: []string{pipeline.Running},
				TargetStatus:     pipeline.Failed,
			})
		}); err != nil {
			log.Printf("update pipeline state error: %s", err.Error())
		} else { // 更新成功才会发布通知事件
			// update pipeline success , publish event to notify user
			eventbus.PublishAsync(c, &events.PipelineStatusUpdateNotifyEvent{
				TxUUID:  req.UUID,
				Status:  pipeline.Failed,
				Message: fmt.Sprintf("%s invalidate", middleware.GetUserName(c)),
			})
		}
		// 如果审批不通过，且没有待执行的step，执行完成，清理构建缓存
		eventbus.PublishAsync(c, &events.CheckPipelineRunFinishEvent{
			TxUUID:    req.UUID,
			StageIdx:  req.StepIndex.StageIdx,
			StepState: pipeline.Failed,
		})
	}
	// ok
	response.OK(c, nil)
}

// ValidatePage 人工卡点step分页查询
func ValidatePage(c *gin.Context) {
	// 获取分页参数
	type ValidatePageRequest struct {
		request.PageRequest
		ProjectUUID  string `json:"projectUUID"`
		AppUUID      string `json:"appUUID"`
		PipelineUUID string `json:"pipelineUUID"`
		PipelineName string `json:"pipelineName"`
	}
	var req ValidatePageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	email := middleware.GetUserEmail(c)
	if util.IsEmpty(email) {
		response.Err(c, response.OperatePermissionDeniedError("用户邮箱不能为空"))
		return
	}
	type ValidatePageResponseEntry struct {
		models.StepState
		AppName      string `json:"appName"`
		PipelineName string `json:"pipelineName"`
		BuildNumber  int64  `json:"buildNumber"`
		TriggerUser  string `json:"triggerUser"`
	}
	var total int64
	var entries []ValidatePageResponseEntry
	engine := mysql.GetDB()
	/*
		select plss.*,pi.name as pipelineName,pa.name as appName, pl.build_number as buildNumber
		from (
		select * from pipeline_logger_step_state
		where
		state = 'running' and
		content like '%<EMAIL>%') plss
		LEFT JOIN pipeline_logger pl on plss.tx_uuid = pl.uuid
		LEFT JOIN pipeline_info pi on pl.pipeline_uuid = pi.uuid
		LEFT JOIN project_app pa on pi.app_uuid = pa.uuid
		LEFT JOIN project p on pa.project_uuid = p.uuid WHERE p.uuid = ?;
	*/
	// 构建基础查询条件 - 使用子查询优化性能
	subQuery := engine.Table("pipeline_logger_step_state").
		Select("*").Where("state = ?", pipeline.Running).
		Where("content LIKE ?", util.Like("onditionType%"+email))

	query := engine.Table("(?) as plss", subQuery).
		Select("plss.*, pi.name as pipeline_name, pa.name as app_name, pl.build_number as build_number, pl.trigger_user as trigger_user").
		Joins("LEFT JOIN pipeline_logger pl on plss.tx_uuid = pl.uuid").
		Joins("LEFT JOIN pipeline_info pi on pl.pipeline_uuid = pi.uuid").
		Joins("LEFT JOIN project_app pa on pi.app_uuid = pa.uuid")

	if req.ProjectUUID != "" {
		query = query.Joins("LEFT JOIN project_info pji on pa.project_uuid = pji.uuid").
			Where("pji.uuid = ?", req.ProjectUUID)
	}
	if req.AppUUID != "" {
		query = query.Where("pi.app_uuid = ?", req.AppUUID)
	}
	if req.PipelineUUID != "" {
		query = query.Where("pl.pipeline_uuid = ?", req.PipelineUUID)
	}
	if req.PipelineName != "" {
		query = query.Where("pi.name like ?", util.Like(req.PipelineName))
	}
	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 查询分页数据
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("pl.gmt_create DESC").Offset(offset).
		Limit(req.PageSize).Find(&entries).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 返回分页数据
	response.OK(c, response.PageModelV2[ValidatePageResponseEntry]{
		Total:    total,
		List:     entries,
		Page:     req.Page,
		PageSize: req.PageSize,
	})
}

// 查询流水线状态

func State(c *gin.Context) {
	type (
		StateRequest struct {
			TxUUID string `json:"txUUID" binding:"required"`
		}
		StateResponse struct {
			PipelineState string              `json:"pipelineState"`
			RunningSteps  int64               `json:"runningSteps"`
			Stages        []models.StageState `json:"stages"`
			Steps         []models.StepState  `json:"steps"`
		}
	)
	var req StateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	engine := mysql.GetDB()
	// 查询当前流水线状态
	var pipelineState string
	// 查询当前流水线状态
	var runningSteps int64
	// 查询当前流水线下的所有stage状态
	var stageStates []models.StageState
	// 查询当前流水线下的所有step状态
	var stepStates []models.StepState
	if err := engine.Transaction(func(tx *gorm.DB) error {
		// 查询流水线状态
		if err := tx.Table("pipeline_logger").Where("uuid = ?", req.TxUUID).Pluck("status", &pipelineState).Error; err != nil {
			return err
		}
		// 查询当前流水线下的running step数量
		if err := tx.Model(models.StepState{}).Where("tx_uuid = ? and state = ?", req.TxUUID, pipeline.Running).Count(&runningSteps).Error; err != nil {
			return err
		}
		// 查询当前流水线下的所有stage状态
		if err := tx.Where("tx_uuid = ?", req.TxUUID).Find(&stageStates).Error; err != nil {
			return err
		}
		// 查询当前流水线下的所有step状态
		if err := tx.Where("tx_uuid = ?", req.TxUUID).Find(&stepStates).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, StateResponse{
		PipelineState: pipelineState,
		RunningSteps:  runningSteps,
		Stages:        stageStates,
		Steps:         stepStates,
	})
}

// Action 查询 action 状态
func Action(c *gin.Context) {
	type (
		ActionStateRequest struct {
			TxUUID string `json:"txUUID"`
			pipeline.StepIndex
			Cursor int64 `json:"cursor"` // action 游标
		}
		ActionStateResponseEntry struct {
			ActionName string `gorm:"column:action_name" json:"action"`    // 名称
			State      string `gorm:"column:state;NOT NULL" json:"status"` // 状态
			ActionIdx  int    `gorm:"column:action_idx" json:"actionIdx"`  // 下标
			StartTime  int64  `gorm:"column:start_time" json:"startTime"`  // 开始时间
			EndTime    int64  `gorm:"column:end_time" json:"endTime"`      // 结束时间
		}
		ActionStateResponse struct {
			StepState string                     `json:"stepState"`
			Actions   []ActionStateResponseEntry `json:"actions"`
		}
	)
	var req ActionStateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// 查询当前流水线下的step状态
	var step models.StepState
	// 查询当前流水线下的step所有action状态
	var actionStates []ActionStateResponseEntry
	engine := mysql.GetDB()
	if err := engine.Transaction(func(tx *gorm.DB) error {
		if err := tx.Select("id,state").Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
			req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx).First(&step).Error; err != nil {
			return err
		}
		if err := tx.Model(models.ActionState{}).Where("step_id = ?", step.Id).Order("action_idx ASC").
			Find(&actionStates).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, ActionStateResponse{
		StepState: step.State,
		Actions:   actionStates,
	})
}

// 查询action执行日志

func Logs(c *gin.Context) {

	type LogsReq struct {
		TxUUID    string `json:"txUUID"`
		ActionIdx int    `json:"actionIdx"`
		pipeline.StepIndex
		Cursor int64 `json:"cursor"`
	}

	type LogsResponse struct {
		ActionState string  `json:"actionState"`
		Cursor      int64   `json:"cursor"` // action 游标
		Logs        *string `json:"logs"`
		NoMoreLogs  bool    `json:"noMoreLogs"`
	}

	var req LogsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}

	// 查询action状态
	action := models.ActionState{}
	if err := mysql.GetDB().Where("log_filename = ?", fmt.Sprintf("%s-%d-%d-%d-%d.log",
		req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, req.ActionIdx)).First(&action).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}

	index := req.Cursor
	validCursor := index >= 0 && index < action.LogLength && index < int64(len(action.LogIndexes))

	var ret = LogsResponse{
		ActionState: action.State,
		NoMoreLogs:  action.LogInStorage,
		Cursor:      req.Cursor,
	}

	if validCursor {
		length := action.LogLength - req.Cursor
		offset := action.LogIndexes[index]
		// 限制最大读取长度
		if length > MaxReadLine {
			length = MaxReadLine
			if req.Cursor+length < action.LogLength {
				ret.NoMoreLogs = false
			}
		}
		var err error
		// 获取日志行（通过游标）
		sb, logRows, err := ReadLogs(c, action.LogInStorage, req.TxUUID, action.LogFilename, offset, length)
		if err != nil {
			response.Err(c, response.UnknownError(err.Error()))
			return
		}
		logContent := sb.String()
		ret.Logs = &logContent
		ret.Cursor += logRows
	}
	// 游标无效
	response.OK(c, ret)
}

func OriginLogger(c *gin.Context) {
	type DownloadLoggerRequest struct {
		ActionIdx  int    `form:"actionIdx"`
		TxUUID     string `form:"txUUID"`
		StageIdx   int    `form:"stageIdx"`
		StepRowIdx int    `form:"stepRowIdx"`
		StepIdx    int    `form:"stepIdx"`
	}
	var req DownloadLoggerRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// 查询action状态
	logFileName := fmt.Sprintf("%s-%d-%d-%d-%d.log",
		req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, req.ActionIdx)
	action := models.ActionState{}
	if err := mysql.GetDB().Where("log_filename = ?", logFileName).First(&action).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 打开日志文件
	file, err := OpenLogs(c, action.LogInStorage, req.TxUUID, action.LogFilename)
	if err != nil {
		response.Err(c, response.UnknownError(err.Error()))
		return
	}
	defer file.Close()
	// 设置响应头
	c.Writer.Header().Set("Content-Type", "text/plain;charset=utf-8")
	// 告知客户端文件的大小
	stat, err := file.Stat()
	if err != nil {
		response.Err(c, response.UnknownError(err.Error()))
		return
	}
	c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", stat.Size()))
	// 写入给客户端
	_, err = io.Copy(c.Writer, file)
	// 检查是否在读取文件时发生错误
	if err != nil {
		response.Err(c, response.UnknownError(err.Error()))
	}
}

func DownloadLogger(c *gin.Context) {
	type DownloadLoggerRequest struct {
		ActionIdx  int    `form:"actionIdx"`
		TxUUID     string `form:"txUUID"`
		StageIdx   int    `form:"stageIdx"`
		StepRowIdx int    `form:"stepRowIdx"`
		StepIdx    int    `form:"stepIdx"`
	}
	var req DownloadLoggerRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// 查询action状态
	logFileName := fmt.Sprintf("%s-%d-%d-%d-%d.log",
		req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, req.ActionIdx)
	action := models.ActionState{}
	if err := mysql.GetDB().Where("log_filename = ?", logFileName).First(&action).Error; err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 打开日志文件
	file, err := OpenLogs(c, action.LogInStorage, req.TxUUID, action.LogFilename)
	if err != nil {
		response.Err(c, response.UnknownError(err.Error()))
		return
	}
	defer file.Close()
	// 设置响应头
	c.Writer.Header().Set("Content-Type", "application/octet-stream")

	filename := fmt.Sprintf("%s-%d-%d-%d-%d-%s.log",
		req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, req.ActionIdx, action.ActionName)
	disposition := fmt.Sprintf("attachment; filename=\"%s\"", filename)
	c.Writer.Header().Set("Content-Disposition", disposition)
	// 告知客户端文件的大小
	stat, err := file.Stat()
	if err != nil {
		response.Err(c, response.UnknownError(err.Error()))
		return
	}
	c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", stat.Size()))
	// 写入给客户端
	_, err = io.Copy(c.Writer, file)
	// 检查是否在读取文件时发生错误
	if err != nil {
		response.Err(c, response.UnknownError(err.Error()))
	}
}

func Dynamic(c *gin.Context) {
	type DynamicEnvInputConfirmRequest struct {
		pipeline.StepIndex
		TxUUID string         `json:"txUUID" binding:"required"`
		EnvMap map[string]any `json:"envMap"`
	}
	var req DynamicEnvInputConfirmRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// 将动态环境变量值强转为字符串
	var envMap = make(map[string]string)
	for k, v := range req.EnvMap {
		value := fmt.Sprintf("%v", v)
		// 限制环境变量值的长度
		if len(value) > 1024 {
			value = value[:1024]
		}
		envMap[k] = value
	}
	engine := mysql.GetDB()
	// 添加动态环境变量
	if err := engine.Transaction(func(tx *gorm.DB) error {
		// 更新step状态
		if ret := tx.Model(models.StepState{}).
			Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ? and state = ?",
				req.TxUUID, req.StageIdx, req.StepRowIdx, req.StepIdx, pipeline.Running).
			UpdateColumns(map[string]any{
				"content":  util.ToJSONString(envMap), // 存储动态环境变量
				"state":    pipeline.Success,
				"end_time": time.Now().Unix(),
			}); ret.Error != nil {
			return ret.Error
		} else if ret.RowsAffected <= 0 {
			return fmt.Errorf("step %d-%d-%d already confirm", req.StageIdx, req.StepRowIdx, req.StepIdx)
		}
		// 更新流水线环境变量
		var dbEnvMap = make(map[string]string)
		var envs string
		if err := tx.Model(models.PipelineLogger{}).Where("uuid = ?", req.TxUUID).Limit(1).Pluck("envs", &envs).Error; err != nil {
			log.ErrorE("get envs error", err)
		}
		if envs != "" {
			dbEnvMap = util.JSONStringToStruct[map[string]string](envs)
		}
		saveEnvMap := util.MergeMap(envMap, dbEnvMap)
		if err := tx.Model(models.PipelineLogger{}).Where("uuid = ?", req.TxUUID).
			Update("envs", util.ToJSONString(saveEnvMap)).Error; err != nil {
			return err
		}
		return nil

	}); err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	// 清理超时队列
	eventbus.PublishAsync(c, &events.CancelListenPipelineStepRunTimeoutEvent{
		TxUUID:  req.TxUUID,
		Indexes: []pipeline.StepIndex{req.StepIndex},
	})
	// 执行下一个step或者stage
	_ = NewEngine().ExecuteNext(req.TxUUID, pipeline.Success, "", req.StepIndex)
	// ok
	response.OK(c, nil)
}
