package executor

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"pipeline/internal/server/logger"
	"strings"

	"pipeline/config"
	"pipeline/pkg/dbfs"
	"pipeline/pkg/oss"

	"git.makeblock.com/makeblock-go/log"
)

const (
	MaxReadLine       = 5000      // 每次最大读取行数
	MaxLineSize       = 64 * 1024 // 每行最大长度
	DBFSPrefix        = "action_log/"
	defaultBufSize    = MaxLineSize
	loggerSizeOverTip = "logger size: %d  over limit, limit is %d bytes"
)

// 写入日志

func WriteLogs(ctx context.Context, filename string, offset int64, rows []string) ([]int, bool, error) {
	flag := os.O_WRONLY
	if offset == 0 {
		// Create file only if offset is 0, or it could result in content holes if the file doesn't exist.
		flag |= os.O_CREATE
	}
	name := DBFSPrefix + filename
	f, err := dbfs.OpenFile(ctx, name, flag)
	if err != nil {
		return nil, false, fmt.Errorf("dbfs OpenFile %q: %w", name, err)
	}
	defer f.Close()

	stat, err := f.Stat()
	if err != nil {
		return nil, false, fmt.Errorf("dbfs Stat %q: %w", name, err)
	}
	if stat.Size() < offset {
		// If the size is less than offset, refuse to write, or it could result in content holes.
		// However, if the size is greater than offset, we can still write to overwrite the content.
		return nil, false, fmt.Errorf("size of %q is less than offset", name)
	}

	if _, err = f.Seek(offset, io.SeekStart); err != nil {
		return nil, false, fmt.Errorf("dbfs Seek %q: %w", name, err)
	}
	// write
	writer := bufio.NewWriterSize(f, defaultBufSize)
	maxLogSize := config.Items().Pipeline.MaxActionLogSize
	ns := make([]int, 0, len(rows))
	logSize := stat.Size()
	logSizeLimit := false
	for _, row := range rows {
		// check total oversize
		rowLen := int64(len(row))
		if logSize+rowLen > maxLogSize {
			var n int
			n, err = writer.WriteString(FormatLog(fmt.Sprintf(loggerSizeOverTip, logSize+rowLen, maxLogSize)))
			if err != nil {
				return nil, logSizeLimit, err
			}
			ns = append(ns, n)
			logSizeLimit = true
			break
		}
		// normal write
		var n int
		n, err = writer.WriteString(FormatLog(row) + "\n")
		if err != nil {
			return nil, logSizeLimit, err
		}
		ns = append(ns, n)
		logSize += int64(n)
	}
	// flush
	if err = writer.Flush(); err != nil {
		return nil, logSizeLimit, err
	}
	return ns, logSizeLimit, nil
}

func FormatLog(content string) string {
	// Content shouldn't contain new line, it will break log indexes, other control chars are safe.
	content = strings.ReplaceAll(content, "\n", `\n`)
	if len(content) > MaxLineSize {
		content = content[:MaxLineSize]
	}
	return content
}

// 通过游标读取日志

func ReadLogs(ctx context.Context, inStorage bool, txUUID, filename string, offset, limit int64) (*strings.Builder, int64, error) {
	f, err := OpenLogs(ctx, inStorage, txUUID, filename)
	if err != nil {
		return nil, 0, err
	}
	defer f.Close()

	if _, err = f.Seek(offset, io.SeekStart); err != nil {
		return nil, 0, fmt.Errorf("file seek: %w", err)
	}
	scanner := bufio.NewScanner(f)
	maxLineSize := MaxLineSize + 1
	scanner.Buffer(make([]byte, maxLineSize), maxLineSize)

	var rows int64
	var sb strings.Builder
	for scanner.Scan() && (rows < limit || limit < 0) {
		c := scanner.Text()
		sb.WriteString(c + "\n")
		rows++
	}
	// err check
	if err = scanner.Err(); err != nil {
		return nil, 0, fmt.Errorf("scan: %w", err)
	}
	return &sb, rows, nil
}

// 将日志从dbfs转移到存储

func TransferLogs(ctx context.Context, txUUID, filename string) (func(), error) {
	name := DBFSPrefix + filename
	remove := func() {
		if err := dbfs.Remove(ctx, name); err != nil {
			log.Printf("dbfs remove %q: %v", name, err)
		}
	}
	f, err := dbfs.Open(ctx, name)
	if err != nil {
		return nil, fmt.Errorf("dbfs open %q: %w", name, err)
	}
	defer f.Close()

	// Save to storage
	storageFilename := FormatLogFileName(txUUID, filename)
	fileInfo, err := f.Stat()
	if err != nil {
		return nil, err
	}
	bucket := config.Items().OSS.LoggerBucketName
	if err = oss.GetService().Save(ctx, bucket, storageFilename, f, fileInfo.Size()); err != nil {
		return nil, fmt.Errorf("storage save %q: %w", filename, err)
	}
	return remove, nil
}

// 获取日志

func OpenLogs(ctx context.Context, inStorage bool, txUUID, filename string) (oss.Object, error) {
	// in db
	if !inStorage {
		name := DBFSPrefix + filename
		f, err := dbfs.Open(ctx, name)
		if err != nil {
			return nil, fmt.Errorf("dbfs open %q: %w", name, err)
		}
		return f, nil
	}
	storageFileName := FormatLogFileName(txUUID, filename)
	bucket := config.Items().OSS.LoggerBucketName
	f, err := oss.GetService().Open(ctx, bucket, storageFileName)
	if err != nil {
		return nil, fmt.Errorf("storage open %q: %w", filename, err)
	}
	return f, nil
}

func RemoveLogs(ctx context.Context, inStorage bool, filename string) error {
	if !inStorage {
		name := DBFSPrefix + filename
		err := dbfs.Remove(ctx, name)
		if err != nil {
			return fmt.Errorf("dbfs remove %q: %w", name, err)
		}
		return nil
	}
	bucket := config.Items().OSS.LoggerBucketName
	err := oss.GetService().Delete(ctx, bucket, filename)
	if err != nil {
		return fmt.Errorf("storage delete %q: %w", filename, err)
	}
	return nil
}

// 存储目录: app/pipeline_uuid/tx_uuid/logger/filename

func FormatLogFileName(txUUID, filename string) string {
	return fmt.Sprintf("%s%s/logger/%s", logger.StoragePrefix(txUUID), txUUID, filename)
}
