package executor

import (
	"context"
	"crypto/subtle"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"pipeline/internal/server/artifact"
	innerPipe "pipeline/internal/server/pipeline"
	"pipeline/pkg/common"
	"time"

	"pipeline/config"
	"pipeline/internal/server/runner"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	"pipeline/pkg/notify/send"
	"pipeline/pkg/pipeline"
	v1 "pipeline/pkg/proto/runner/v1"
	"pipeline/pkg/proto/runner/v1/runnerv1connect"
	"pipeline/pkg/queue"
	"pipeline/pkg/runner/agent"
	"pipeline/pkg/util"

	"connectrpc.com/connect"
	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	mbredis "git.makeblock.com/makeblock-go/redis"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
)

var _ runnerv1connect.RunnerServiceClient = (*RunnerService)(nil)

type RunnerService struct {
	runnerv1connect.UnimplementedRunnerServiceHandler
	repo *runner.Repo
}

func NewRunnerServiceHandler() (string, http.Handler) {
	return runnerv1connect.NewRunnerServiceHandler(
		&RunnerService{
			repo: runner.NewRunnerRepo(mysql.GetDB()),
		},
		connect.WithCompressMinBytes(1024),
		withRunner,
	)
}

func (r *RunnerService) Ping(_ context.Context, req *connect.Request[v1.PingRequest]) (*connect.Response[v1.PingResponse], error) {
	return connect.NewResponse(&v1.PingResponse{
		Data: fmt.Sprintf("Hello, %s!", req.Msg.Data),
	}), nil
}

func (r *RunnerService) Register(ctx context.Context, req *connect.Request[v1.RegisterRequest]) (*connect.Response[v1.RegisterResponse], error) {

	// get runner token
	token, err := r.repo.GetRunnerToken(req.Msg.Token)
	if err != nil {
		return nil, errors.New("runner registration token not found")
	}
	// token has been invalidated
	if token.Active {
		return nil, errors.New("runner registration token has been invalidated, please use the latest one")
	}
	// 检查token是否在有效期内
	if token.ExpiredAt.Unix() < time.Now().Unix() {
		return nil, errors.New("runner registration token has expired, please use the latest one")
	}
	// generate token
	Token, TokenSalt, TokenHash, _, err := util.GenerateSaltedToken()
	if err != nil {
		return nil, err
	}
	disable := false
	var isPublic bool = true
	pipelineRunner := &models.PipelineRunner{
		Name:      req.Msg.Name,
		Token:     Token,
		TokenSalt: TokenSalt,
		TokenHash: TokenHash,
		Labels:    util.ArrayToString(req.Msg.Labels),
		Disable:   &disable,
		IsPublic:  &isPublic,
	}
	pipelineRunner.Create("register")

	// create runner
	id, err := r.repo.Add(pipelineRunner)
	if err != nil {
		return nil, err
	}

	// set token active
	err = r.repo.ActiveToken(req.Msg.Token)
	if err != nil {
		return nil, err
	}

	if err = eventbus.Publish(ctx, &events.PipelineRunnerUpdateEvent{
		PipelineRunner: pipelineRunner,
	}); err != nil {
		return nil, err
	}

	return connect.NewResponse(&v1.RegisterResponse{
		Runner: &v1.RunnerResponse{
			Id:     id,
			Token:  Token,
			Name:   req.Msg.Name,
			Labels: req.Msg.Labels,
			Uuid:   pipelineRunner.UUID,
		},
	}), nil
}

func (r *RunnerService) Declare(ctx context.Context, req *connect.Request[v1.DeclareRequest]) (*connect.Response[v1.DeclareResponse], error) {
	// get runner from db
	runnerModel, err := r.repo.GetRunnerByUUID(req.Msg.Uuid)
	if err != nil || subtle.ConstantTimeCompare([]byte(runnerModel.TokenHash),
		[]byte(util.HashToken(req.Msg.Token, runnerModel.TokenSalt))) != 1 {
		return nil, status.Error(codes.Unauthenticated, "unregistered runner")
	}
	// update database runner labels
	runnerModel.Labels = util.ArrayToString(req.Msg.Labels)
	runnerModel.Version = req.Msg.Version
	runnerModel.Arch = req.Msg.Arch
	runnerModel.OS = req.Msg.Os
	runnerModel.Ip = req.Msg.Ip
	_, err = r.repo.Update(runnerModel)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "update runner: %v", err)
	}
	// load in cache
	if err = eventbus.Publish(ctx, &events.PipelineRunnerUpdateEvent{
		PipelineRunner: runnerModel,
	}); err != nil {
		return nil, err
	}
	// response
	return connect.NewResponse(&v1.DeclareResponse{
		Runner: &v1.RunnerResponse{
			Id:      runnerModel.ID,
			Uuid:    runnerModel.UUID,
			Name:    runnerModel.Name,
			Token:   req.Msg.Token,
			Labels:  req.Msg.Labels,
			Version: req.Msg.Version,
		},
	}), nil
}

func (r *RunnerService) FetchTask(ctx context.Context, req *connect.Request[v1.FetchTaskRequest]) (*connect.Response[v1.FetchTaskResponse], error) {

	runnerEntry := GetRunner(ctx)

	if runnerEntry == nil {
		return nil, status.Error(codes.Internal, "runner not found")
	}

	if runnerEntry.Disable != nil && *runnerEntry.Disable {
		return nil, status.Error(codes.Internal, "runner is disabled")
	}

	response := &v1.FetchTaskResponse{}

	for _, label := range runnerEntry.Labels {
		task, ok := queue.PopTask(label)
		if ok {
			log.Info(fmt.Sprintf("fetch task successed, tx uuid: %s, index: %v", task.Ctx.TxUUID, task.Ctx.Index))
			// 需要去获取制品的最新版本（某些制品是使用了latest的版本）
			artifact.DefaultArtifactService().ArtifactsLatestVersion(task)
			// 需要去查询最新输出的ENV给后续的任务使用
			task.Ctx.Env = util.MergeMap(task.Ctx.Env, pipelineEnvOutput(task.Ctx.TxUUID))
			// 序列化
			response.Content = task.StepContextToString()
			// 记录执行当前任务的runner
			eventbus.PublishAsync(ctx, &events.StepRunOnRunnerEvent{
				Index:       task.Ctx.Index,
				TxUUID:      task.Ctx.TxUUID,
				RunnerToken: req.Msg.Token,
			})
			// 监听step执行超时
			eventbus.PublishAsync(ctx, &events.ListenPipelineStepRunTimeoutEvent{
				Index:      task.Ctx.Index,
				TxUUID:     task.Ctx.TxUUID,
				StepDefine: task.Ctx.StepDefine,
				Time:       time.Now(),
			})
			break
		}
	}

	// 获取清理任务
	cli := mbredis.GetClient()
	key := runnerPurgeTaskKey(runnerEntry.Token)
	now := time.Now().Unix()
	values := cli.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min: "-inf", Max: fmt.Sprintf("%d", now),
	}).Val()
	if len(values) > 0 {
		cli.ZRem(ctx, key, values)
		log.Info("purge task", log.Any("token", runnerEntry.Token), log.Any("values", values))
		tasks := make([]*v1.PurgeTask, 0)
		for _, value := range values {
			entry := util.JSONStringToStruct[v1.PurgeTask](value)
			tasks = append(tasks, &entry)
		}
		response.PurgeTasks = tasks
	}

	return connect.NewResponse(response), nil
}

func runnerPurgeTaskKey(token string) string {
	return fmt.Sprintf("%spurge:%s", config.Items().Redis.Prefix, token)
}

func pipelineEnvOutput(uuid string) map[string]string {
	envMap := make(map[string]string)
	// 查询最新的环境变量输出
	engine := mysql.GetDB()
	var envs string
	if err := engine.Model(models.PipelineLogger{}).Where("uuid = ?", uuid).Limit(1).Pluck("envs", &envs).Error; err != nil {
		log.ErrorE("get envs error", err)
	}
	if envs != "" {
		envMap = util.JSONStringToStruct[map[string]string](envs)
	}
	return envMap
}

func (r *RunnerService) UpdateLog(ctx context.Context, req *connect.Request[v1.UpdateLogRequest]) (*connect.Response[v1.UpdateLogResponse], error) {

	response := &v1.UpdateLogResponse{
		AckIndex: 0,
	}
	engine := mysql.GetDB()
	// 查询当前任务日志进度
	action := models.ActionState{}
	if err := engine.Where("log_filename = ?", fmt.Sprintf("%s-%d-%d-%d-%d.log",
		req.Msg.TxUuid, req.Msg.StageIdx, req.Msg.StepRowIdx, req.Msg.StepIdx, req.Msg.ActionIdx)).
		First(&action).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "get task: %v", err)
	}
	ack := action.LogLength // 拿到已经存储了的日志长度
	// 如果 请求插入的地址 大于 ack ：那其实还有部分数据还未写入，不给予写入，返回 ack
	// 如果 请求的 rows 长度 + 请求的 index 小于 ack ：那其实这部分数据已经写入过了，不给予写入，返回 ack
	if len(req.Msg.Rows) == 0 || req.Msg.Index > ack || int64(len(req.Msg.Rows))+req.Msg.Index <= ack {
		response.AckIndex = ack
		return connect.NewResponse(response), nil
	}
	// 如果日志已经存储
	if action.LogInStorage {
		return nil, status.Errorf(codes.AlreadyExists, "log file has been archived")
	}
	// 获取真正需要写入的日志
	rows := req.Msg.Rows[ack-req.Msg.Index:]
	// 直接进行写入日志
	ns, logSizeLimit, err := WriteLogs(ctx, action.LogFilename, action.LogSize, rows)
	// oversize response
	response.LogSizeLimit = logSizeLimit
	if err != nil {
		return nil, status.Errorf(codes.Internal, "write logs: %v", err)
	}
	// 更新日志长度记录
	action.LogLength += int64(len(ns))
	for _, n := range ns {
		action.LogIndexes = append(action.LogIndexes, action.LogSize)
		action.LogSize += int64(n)
	}
	// 记录当前的日志长度作为 ack 响应给 runner
	response.AckIndex = action.LogLength
	// 更新数据库
	if err = engine.Model(models.ActionState{}).Where("id = ?", action.Id).
		UpdateColumns(map[string]any{
			"log_indexes": action.LogIndexes,
			"log_length":  action.LogLength,
			"log_size":    action.LogSize,
		}).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "error update action state: %v", err)
	}
	// no more logs, transfer logs to object storage
	if req.Msg.NoMore || response.LogSizeLimit {
		// 从数据库写入到存储
		var remove func()
		remove, err = TransferLogs(ctx, req.Msg.TxUuid, action.LogFilename)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "transfer logs: %v", err)
		}
		// 更新数据库
		if err = engine.Model(models.ActionState{}).Where("id = ?", action.Id).
			Update("log_in_storage", true).Error; err != nil {
			return nil, status.Errorf(codes.Internal, "error update action log_in_storage: %v", err)
		}
		if remove != nil {
			go func() {
				// prevent panic
				defer func() {
					if rec := recover(); rec != nil {
						log.Error("recover from panic", log.Any("error", rec))
					}
				}()
				time.Sleep(3 * time.Second) // 因为如果立即执行删除会导致前端拉取日志出现异常，所以延迟 5 秒
				remove()                    // 执行 remove 函数
			}()
		}
	}

	return connect.NewResponse(response), nil
}

func (r *RunnerService) UpdateTask(ctx context.Context, req *connect.Request[v1.UpdateTaskRequest]) (*connect.Response[v1.UpdateTaskResponse], error) {

	// response
	ret := &v1.UpdateTaskResponse{}
	engine := mysql.GetDB()

	// pipeline state
	if err := engine.Transaction(func(tx *gorm.DB) error {
		// 查询流水线状态
		if err := tx.Model(models.PipelineLogger{}).Where("uuid = ?", req.Msg.TxUuid).Pluck("status", &ret.PipelineState).Error; err != nil {
			return err
		}
		// 查询step状态（step可能被跳过、取消）
		if err := tx.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
			req.Msg.TxUuid, req.Msg.StageIdx, req.Msg.StepRowIdx, req.Msg.StepIdx).Pluck("state", &ret.StepState).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return connect.NewResponse(ret), err
	}

	// action
	if req.Msg.State != nil {
		if err := r.updateStepState(ctx, req, ret); err != nil {
			return nil, status.Errorf(codes.Internal, "update task: %v", err)
		}
	}
	// return
	return connect.NewResponse(ret), nil
}

func (r *RunnerService) updateStepState(ctx context.Context, request *connect.Request[v1.UpdateTaskRequest], ret *v1.UpdateTaskResponse) error {
	req := request.Msg
	engine := mysql.GetDB()
	// 更新step状态
	stepState := &models.StepState{
		StartTime: req.State.StartTime,
		EndTime:   req.State.EndTime,
	}
	// 处理output
	if len(req.Outputs) > 0 {
		ret.SentOutputs = r.handleOutputs(ctx, req)
	}
	// 获取当前step的id
	var dbStepState models.StepState
	if err := engine.Model(stepState).Select("id,state").
		Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
			req.TxUuid, req.StageIdx, req.StepRowIdx, req.StepIdx).First(&dbStepState).Error; err != nil {
		return err
	}
	// update action status
	if err := r.updateActionState(req, engine, dbStepState); err != nil {
		log.Error("update action state error", log.Any("error", err))
		return err
	}

	// 更新step状态、content、开始时间、结束时间等
	if err := engine.Transaction(func(tx *gorm.DB) error {
		condition := tx.Model(stepState).Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
			req.TxUuid, req.StageIdx, req.StepRowIdx, req.StepIdx)
		// 更新content、开始时间、结束时间等
		if err := condition.Updates(stepState).Error; err != nil {
			return err
		}
		// 更新step状态
		if result := condition.Where("state in (?)", []string{pipeline.Waiting, pipeline.Running, pipeline.Pending}).
			Update("state", req.State.State); result.Error != nil {
			log.Error("update step state error", log.Any("error", result.Error))
			return result.Error
		} else if result.RowsAffected <= 0 {
			log.Info("step state has been updated", log.Any("state", req.State.State))
			return nil
		}
		eventbus.PublishAsync(ctx, &events.StepStateUpdateEvent{
			TxUUID: req.TxUuid,
			State:  req.State.State,
			StepIndexes: []pipeline.StepIndex{{
				StageIdx:   int(req.StageIdx),
				StepRowIdx: int(req.StepRowIdx),
				StepIdx:    int(req.StepIdx),
			}},
		})
		return nil
	}); err != nil {
		return err
	}

	if req.DoNext {
		// try to execute next
		if err := NewEngine().ExecuteNext(req.TxUuid, req.State.State, req.State.Message, pipeline.StepIndex{
			StageIdx:   int(req.StageIdx),
			StepRowIdx: int(req.StepRowIdx),
			StepIdx:    int(req.StepIdx),
		}); err != nil {
			log.ErrorE("execute next step error", err)
			return nil
		}
	}
	// 当runner升级或者重启的时候，会将正在执行的任务设置为cancel状态，这个时候因为req.DoNext是false，
	// 所以不会执行下一个任务，如果只有一个step，或者是当前流水线的最后一个step，就会导致不去检查流水线是否已经执行完成流水线一直处于running状态，需要去更新流水线为cancel状态
	// 因为手动进行cancel也会被runner返回cancel状态，所以这里需要去检查是否是runner主动返回的cancel状态（此时数据库状态非cancel，因为手动cancel会主动）
	if req.State.State == pipeline.Cancel && dbStepState.State == pipeline.Running {
		eventbus.PublishAsync(ctx, &events.CancelStepEvent{
			User:   "runner",
			TxUUID: req.TxUuid,
			StepIndex: pipeline.StepIndex{
				StageIdx:   int(req.StageIdx),
				StepRowIdx: int(req.StepRowIdx),
				StepIdx:    int(req.StepIdx),
			},
		})
	}

	return nil
}

func (r *RunnerService) updateActionState(req *v1.UpdateTaskRequest, engine *gorm.DB, dbStepState models.StepState) error {
	actions := make([]models.ActionState, 0)
	for _, action := range req.State.Actions {
		actions = append(actions, models.ActionState{
			StepId:     dbStepState.Id,
			ActionName: action.Name,
			ActionIdx:  int(action.Index),
			State:      action.State,
			StartTime:  action.StartTime,
			EndTime:    action.EndTime,
			LogFilename: fmt.Sprintf("%s-%d-%d-%d-%d.log",
				req.TxUuid, req.StageIdx, req.StepRowIdx, req.StepIdx, action.Index),
		})
	}

	// action
	var count int64
	if err := engine.Model(&models.ActionState{}).Where("step_id = ?", dbStepState.Id).Count(&count).Error; err != nil {
		return err
	} else if count > 0 {
		// update if action is already exists
		var oldActions []models.ActionState
		if err = engine.Where("step_id = ?", dbStepState.Id).Find(&oldActions).Error; err != nil {
			return err
		}
		oldActionMap := make(map[int]models.ActionState)
		for _, action := range oldActions {
			oldActionMap[action.ActionIdx] = action
		}
		// 没找到匹配的则是说明需要进行创建，否则是更新
		var updates []models.ActionState
		var creates []models.ActionState
		for _, act := range actions {
			action := act
			// 已经存在则进行更新
			if v, ok := oldActionMap[action.ActionIdx]; ok {
				dist := v
				err = copier.Copy(&dist, &action) // 复制远程的给数据库的模型(copier.Copy零值也会进行复制，所以这里id需要重新赋值)
				if err != nil {
					log.ErrorE("copy action state error", err)
					return err
				}
				dist.Id = v.Id
				updates = append(updates, dist)
			} else {
				// 不存在则新增
				creates = append(creates, action)
			}
		}
		// 更新
		if len(updates) > 0 {
			if e := engine.Transaction(func(tx *gorm.DB) error {
				for _, update := range updates {
					u := update
					if e := tx.Where("id = ?", u.Id).Updates(&u).Error; err != nil {
						return e
					}
				}
				return nil
			}); e != nil {
				log.Printf("update actions error: %+v", updates)
				return e
			}
		}
		// 新增（action重试的时候会新增）
		if len(creates) > 0 {
			log.Printf("create actions: %+v", creates)
			if err = engine.Create(&creates).Error; err != nil {
				log.ErrorE("create actions error", err)
				return err
			}
		}
	} else {
		// insert action（first）
		if err = engine.Create(&actions).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r *RunnerService) handleOutputs(ctx context.Context, req *v1.UpdateTaskRequest) []string {
	engine := mysql.GetDB()
	outputs := req.Outputs
	// 需要先查询
	var content string
	if err := engine.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
		req.TxUuid, req.StageIdx, req.StepRowIdx, req.StepIdx).Pluck("content", &content).Error; err != nil {
		log.Error("get step content error", log.Any("error", err))
		return nil
	}
	// 反序列化content成map
	contentMap := make(map[string]any)
	if content != "" {
		if err := json.Unmarshal([]byte(content), &contentMap); err != nil {
			log.Error("unmarshal step content error", log.Any("error", err))
			return nil
		}
	}
	// 反序列化所有的output成map
	ret := make([]string, 0)
	for k, v := range outputs {
		// 因为v是string，所以需要再次反序列化
		outputData := new(any)
		if err := json.Unmarshal([]byte(v), outputData); err != nil {
			log.Error("unmarshal step content error", log.Any("error", err))
			return nil
		}
		contentMap[k] = *outputData
		ret = append(ret, k)
		// 制品输出
		if k == agent.ArtifactsKey {
			eventbus.PublishAsync(ctx, &events.StepArtifactsOutputEvent{
				TxUUID:  req.TxUuid,
				Outputs: util.ToJSONString(outputData),
			})
		}
		// 环境变量输出
		if k == pipeline.EnvPipelineKey {
			eventbus.PublishAsync(ctx, &events.StepEnvOutputEvent{
				TxUUID:       req.TxUuid,
				EnvMapString: util.ToJSONString(outputData),
			})
		}
	}
	// 更新content
	if err := engine.Model(models.StepState{}).Where("tx_uuid = ? and stage_idx = ? and step_row_idx = ? and step_idx = ?",
		req.TxUuid, req.StageIdx, req.StepRowIdx, req.StepIdx).Update("content", util.ToJSONString(contentMap)).Error; err != nil {
		log.Error("update step content error", log.Any("error", err))
		return nil
	}
	return ret
}

func (r *RunnerService) Metrics(ctx context.Context, req *connect.Request[v1.MetricRequest]) (*connect.Response[v1.MetricResponse], error) {
	// tasks
	if req.Msg.Task != nil {
		healthUpdate(ctx, mbredis.GetClient(), mysql.GetDB(), req.Msg)
	}
	// metric
	if req.Msg.Metric != nil {
		// send notification if runner metric over threshold
		if config.Items().RunnerNotify.Enable {
			runnerThresholdCheck(req.Msg)
		}
		// save metric to influxdb
		runner.GetMetricServer().Save(req.Msg)
		log.Info("save metric to influxdb", log.Any("metric", req.Msg))
	}
	return connect.NewResponse(&v1.MetricResponse{}), nil
}

func healthUpdate(ctx context.Context, redisCli *redis.Client, db *gorm.DB, req *v1.MetricRequest) {
	entry := &models.RunnerHealthEntry{}
	cacheRunner, err := redisCli.HGet(ctx, models.RunnerHeartBeat, req.RunnerUuid).Result()
	if errors.Is(err, redis.Nil) {
		// runner not found
		var dbRunner *models.PipelineRunner
		dbRunner, err = GetRunnerByUUID(db, req.RunnerUuid)
		if err != nil {
			log.Error("find runner error", log.Any("error", err))
			return
		}
		entry = models.RunnerConvertToEntry(dbRunner)
	} else if err != nil {
		log.Error("hget runner heartbeat error", log.Any("error", err))
		return
	} else {
		entry.Unmarshal(cacheRunner)
	}
	entry.ActiveTime = time.Now()
	if req.Task != nil {
		entry.TaskCount = len(req.Task.Tasks)
		entry.Tasks = req.Task.Tasks
	}
	redisCli.HSet(ctx, models.RunnerHeartBeat, entry.UUID, entry.Marshal())
}

func GetRunnerByUUID(db *gorm.DB, uuid string) (*models.PipelineRunner, error) {
	runner := &models.PipelineRunner{}
	if err := db.Where("uuid = ?", uuid).First(runner).Error; err != nil {
		return nil, err
	}
	return runner, nil
}

// runnerThresholdCheck 检查是否满足发送通知
func runnerThresholdCheck(req *v1.MetricRequest) {
	type Metric struct {
		UseRate    int
		Threshold  int
		MetricName string
		Used       uint64
		Total      uint64
	}
	metrics := []Metric{
		{
			MetricName: "CPU",
			Used:       req.Metric.Cpu.Used,
			Total:      req.Metric.Cpu.Total,
			UseRate:    int(float64(req.Metric.Cpu.Used) / float64(req.Metric.Cpu.Total) * 100),
			Threshold:  config.Items().RunnerNotify.CPUThresholdRate,
		},
		{
			MetricName: "MEM",
			Used:       req.Metric.Memory.Used,
			Total:      req.Metric.Memory.Total,
			UseRate:    int(float64(req.Metric.Memory.Used) / float64(req.Metric.Memory.Total) * 100),
			Threshold:  config.Items().RunnerNotify.MemThresholdRate,
		},
		{
			MetricName: "DISK",
			Used:       req.Metric.Disk.Used,
			Total:      req.Metric.Disk.Total,
			UseRate:    int(float64(req.Metric.Disk.Used) / float64(req.Metric.Disk.Total) * 100),
			Threshold:  config.Items().RunnerNotify.DiskThresholdRate,
		},
	}
	// check and send notify
	for _, metric := range metrics {
		// 重置持续时间
		metricThreadDurationKey := fmt.Sprintf("runner:metric:thread:duration:%s:%s",
			req.RunnerUuid, metric.MetricName)
		if metric.UseRate < metric.Threshold {
			mbredis.GetClient().Del(context.Background(), metricThreadDurationKey)
		}
		if metric.UseRate >= metric.Threshold {
			// 指标超出阈值持续时间
			if metric.MetricName == "CPU" || metric.MetricName == "MEM" {
				// 检查是否存在
				timeStamp := mbredis.GetClient().Exists(context.Background(), metricThreadDurationKey).Val()
				if timeStamp == 0 {
					mbredis.GetClient().Set(context.Background(),
						metricThreadDurationKey, time.Now().Unix(), 0)
					continue
				}
				// 存在则检查是否超过阈值,小于则不发送通知
				if time.Now().Unix()-timeStamp < int64(config.Items().RunnerNotify.ThresholdDuration.Seconds()) {
					continue
				}
			}
			// 检查预警频率
			if mbredis.GetClient().Exists(context.Background(),
				fmt.Sprintf("runner:notify:%s:%s", req.RunnerUuid, metric.MetricName)).Val() == 1 {
				log.Info("runner notify already send", log.Any("metric", metric))
				continue
			}
			// 发送通知
			message := fmt.Sprintf("%s使用率超过阈值: %d, used: %d, total: %d。",
				metric.MetricName, metric.UseRate, metric.Used, metric.Total)
			receivers := []string{config.Items().RunnerNotify.Receiver}
			if err := send.RunnerMonitorNotice(req, receivers, message); err != nil {
				log.Error("send runner monitor notice error", log.Any("error", err))
			}
			// 设置key
			mbredis.GetClient().Set(context.Background(),
				fmt.Sprintf("runner:notify:%s:%s", req.RunnerUuid, metric.MetricName), "1",
				config.Items().RunnerNotify.NotifyInterval)
			// 清理持续时间key
			mbredis.GetClient().Del(context.Background(), metricThreadDurationKey)
		}
	}
}

func (r *RunnerService) CacheExpire(ctx context.Context, req *connect.Request[v1.CacheExpireRequest]) (*connect.Response[v1.CacheExpireResponse], error) {
	// check pipeline uuid
	if req.Msg.PipelineUuid == "" {
		return nil, status.Error(codes.InvalidArgument, "pipeline uuid is empty")
	}
	repo := innerPipe.NewInfoRepo(mysql.GetDB())
	// query pipeline info
	pipelineInfo, err := repo.GetByUUID(req.Msg.PipelineUuid)
	if err != nil {
		// pipeline may already deleted
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return connect.NewResponse(&v1.CacheExpireResponse{Expired: true}), nil
		}
		return nil, status.Errorf(codes.Internal, "get pipeline info: %v", err)
	}
	// check cache expire
	if pipelineInfo.TriggerTime == nil ||
		common.IsTimestampExpired(pipelineInfo.TriggerTime, config.Items().Runner.CacheExpire) {
		return connect.NewResponse(&v1.CacheExpireResponse{Expired: true}), nil
	}
	return connect.NewResponse(&v1.CacheExpireResponse{Expired: false}), nil
}
