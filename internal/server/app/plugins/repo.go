package plugins

import (
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
)

type configRepo struct {
	db *gorm.DB
}

func NewConfigRepo(db *gorm.DB) *configRepo {
	return &configRepo{db: db}
}

func (repo *configRepo) Info(id int64) (*models.AppConfig, error) {
	var result models.AppConfig
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *configRepo) Add(m *models.AppConfig) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *configRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.AppConfig{}).Error
}

func (repo *configRepo) Update(m *models.AppConfig) (rowsAffected int64, err error) {
	db := repo.db.Model(models.AppConfig{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *configRepo) Exist(ac models.AppConfig) (*models.AppConfig, error) {
	var result models.AppConfig
	if err := repo.db.Where("app_uuid = ? and `key` = ?", ac.AppUUID, ac.Key).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

// GetConfigPluginList 获取配置插件列表
func (repo *configRepo) GetConfigPluginList(uuid string) ([]ApplicationConfigPlugin, error) {
	ret := make([]ApplicationConfigPlugin, 0)
	// 查询配置插件列表
	list := make([]models.AppConfig, 0)
	if err := repo.db.Where("app_uuid = ?", uuid).Find(&list).Error; err != nil {
		return nil, err
	}
	// 遍历配置插件列表获取具体的配置数据
	for _, item := range list {
		it := item
		plugin := NewAppConfigPlugin(PluginType(item.Key), &it)
		cpg := ApplicationConfigPlugin{
			Key:   item.Key,
			Name:  item.Name,
			Value: plugin.Load(),
		}
		ret = append(ret, cpg)
	}
	// 返回结果
	return ret, nil
}

func (repo *configRepo) History(req ConfigHistoryRequest) (*response.PageModelV2[VersionInfoResponse], error) {
	query := repo.db.Model(models.AppConfigEntryVersion{}).
		Where("config_entry_uuid = ?", req.UUID)
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	var list []VersionInfoResponse
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return nil, err
	}
	// 返回结果
	return &response.PageModelV2[VersionInfoResponse]{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     list,
	}, nil
}

func (repo *configRepo) Version(req ConfigVersionRequest) (*VersionInfoResponse, error) {
	var result VersionInfoResponse
	if err := repo.db.Model(models.AppConfigEntryVersion{}).
		Where("config_entry_uuid = ? and version = ?", req.ConfigEntryUUID, req.Version).
		First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *configRepo) GetConfigEntryByUUID(uuid string) (*models.AppConfigEntry, error) {
	var result models.AppConfigEntry
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil

}

func (repo *configRepo) UpdateConfigEntry(entry *models.AppConfigEntry) error {
	return repo.db.Save(entry).Error
}

func (repo *configRepo) GetConfigPluginInfo(uuid string) (*models.AppConfig, error) {
	var result models.AppConfig
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *configRepo) GetConfigPluginInfoByAppUUIDAndKey(uuid string, key string) (*models.AppConfig, error) {
	var result models.AppConfig
	if err := repo.db.Where("app_uuid = ? and `key` = ?", uuid, key).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

// GetConfigEntryList 查询配置列表
func (repo *configRepo) GetConfigEntryList(uuids []string, state string, appUUID string) ([]*models.AppConfigEntry, error) {
	var result []*models.AppConfigEntry
	query := repo.db.Table("project_app_config pac").Select("pace.*").
		Joins("left join project_app_config_entry pace on pac.app_uuid = ? and pac.uuid = pace.config_uuid", appUUID).
		Where("pace.uuid in (?)", uuids)
	if state != "" {
		query = query.Where("pace.state = ?", state)
	}
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (repo *configRepo) GetEditConfigEntryByUUID(uuid string) (*models.AppConfigEntryEdit, error) {
	var result models.AppConfigEntryEdit
	if err := repo.db.Where("entry_uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *configRepo) SaveConfigEntryEdit(edit *models.AppConfigEntryEdit) error {
	return repo.db.Save(edit).Error
}
