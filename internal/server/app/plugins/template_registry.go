package plugins

import (
	"fmt"
	"pipeline/config"
	"pipeline/pkg/models"
	"pipeline/pkg/util"
	"strings"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
)

// InitializeTemplatePlugins 后端部署到Kubernetes上的配置插件
func InitializeTemplatePlugins() {
	// 注册配置插件
	RegisterTemplatePluginAction(KubernetesConfigTemplatePluginAction,
		strings.Split(config.Items().AppConfigPlugins.Kubernetes, ",")...) // 普通kubernetes插件

	// 前端oss部署的配置插件...
}

func KubernetesConfigTemplatePluginAction(r CreateConfigPluginRequest) (map[string]any, error) {
	// 创建配置插件(已有则不创建)
	engine := mysql.GetDB()
	repo := NewConfigRepo(engine)
	ret := make(map[string]any)
	var ac models.AppConfig
	repo.db.Where("app_uuid = ? and `key` = ?",
		r.App.UUID, string(PluginTypeKubernetes)).First(&ac)
	// 如果已经存在则不创建
	if ac.ID > 0 {
		log.Info("config plugin already exists",
			log.Any("app_uuid", r.App.UUID),
			log.Any("key", PluginTypeKubernetes))
		return nil, nil
	}
	// 创建配置插件
	ac = models.AppConfig{
		AppUUID: r.App.UUID,
		Key:     string(PluginTypeKubernetes),
		Name:    string(PluginTypeKubernetes),
	}
	ac.Create(r.Ops)
	if err := repo.db.Create(&ac).Error; err != nil {
		return nil, err
	}
	// 创建子配置项(开发、测试、生产)三个配置项
	configs := make([]models.AppConfigEntry, 0)
	pluginKey := string(PluginTypeKubernetes)
	// 如果是ai的模板
	aikList := strings.Split(config.Items().AppConfigPlugins.AIKubernetes, ",")
	if util.Contains(aikList, r.Template) {
		pluginKey = fmt.Sprintf("%s-ai", pluginKey)
	}
	tplRepo := NewConfigTemplateRepo(engine)
	list, err := tplRepo.List(ConfigTemplateListRequest{Plugin: pluginKey})
	if err != nil {
		return nil, err
	}
	// 组装成map
	tplMap := make(map[string]*models.AppConfigEntryTemplate)
	for _, tpl := range list {
		tplMap[tpl.Key] = tpl
	}
	// 构建渲染配置项
	if r.Params == nil {
		r.Params = make(map[string]any)
	}
	r.Params["APP_ID"] = r.App.Identity
	for _, env := range []string{"dev", "test", "prod"} {
		// 查找对应的配置模板
		tpl, ok := tplMap[env]
		if !ok {
			log.Error("config template not found", log.Any("key", env))
			continue
		}
		// 渲染配置项
		var content string
		content, err = util.RenderContent(tpl.Content, r.Params)
		if err != nil {
			log.ErrorE("render content error", err)
			continue
		}
		entry := models.AppConfigEntry{
			Name:       env,
			Content:    content,
			ConfigUUID: ac.UUID,
			State:      models.ApproveStatePassed, // 默认通过
		}
		entry.Create(r.Ops)
		configs = append(configs, entry)
		// 返回配置项uuid给流水线进行渲染
		ret[fmt.Sprintf("%s_%s", PluginTypeKubernetes, env)] = entry.UUID
	}
	// 创建配置插件子项
	if err = repo.db.Create(&configs).Error; err != nil {
		return nil, err
	}
	return ret, nil
}
