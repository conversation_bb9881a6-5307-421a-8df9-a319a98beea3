package plugins

import (
	"context"
	"encoding/json"
	"git.makeblock.com/makeblock-go/log"
	"gorm.io/gorm"
	"pipeline/pkg/common/response"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
)

var _ ConfigPlugin = (*KubernetesConfigPlugin)(nil)

type KubernetesConfigPlugin struct {
	db   *gorm.DB
	ac   *models.AppConfig
	data []models.AppConfigEntry
}

func NewKubernetesConfigPlugin(db *gorm.DB, ac *models.AppConfig) *KubernetesConfigPlugin {
	return &KubernetesConfigPlugin{db: db, ac: ac}
}

// Parse 解析配置值
func (p *KubernetesConfigPlugin) Parse(data any) error {
	// 解析配置到p.data
	bytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, &p.data)
}

// Create 创建配置
func (p *KubernetesConfigPlugin) Create(ops string) error {
	entryList := make([]models.AppConfigEntry, 0)
	for _, entry := range p.data {
		// 名称和内容都为空则不创建
		if entry.Name == "" || entry.Content == "" {
			log.Error("name or content is empty", log.Any("entry", entry))
			continue
		}
		entry.Create(ops)
		entry.State = models.ApproveStatePending
		entry.ConfigUUID = p.ac.UUID
		entryList = append(entryList, entry)
	}
	err := p.db.Create(&entryList).Error

	// 通知管理员进行审批
	eventbus.PublishAsync(context.Background(), &events.AppConfigPluginNeedApprovalEvent{
		ConfigPlugin: string(PluginTypeKubernetes),
		AppUUID:      p.ac.AppUUID,
		ConfigUUID:   p.ac.UUID,
		User:         ops,
	})

	return err
}

// Update 更新配置（需要保存最近10条记录）
func (p *KubernetesConfigPlugin) Update(ops string) error {
	// 获取所有的配置
	var oldEntries []models.AppConfigEntry
	if err := p.db.Where("config_uuid = ?", p.ac.UUID).Find(&oldEntries).Error; err != nil {
		return err
	}
	// 通过uuid进行映射
	oldEntryMap := make(map[string]models.AppConfigEntry)
	for _, entry := range oldEntries {
		oldEntryMap[entry.UUID] = entry
	}
	// 检查所有的配置
	entryList := make([]models.AppConfigEntry, 0)
	editEntryList := make([]models.AppConfigEntryEdit, 0)
	for _, entry := range p.data {
		// 名称和内容都为空则不更新
		if entry.Name == "" || entry.Content == "" {
			log.Error("name or content is empty", log.Any("entry", entry))
			continue
		}
		oldEntry, ok := oldEntryMap[entry.UUID]
		delete(oldEntryMap, entry.UUID)
		// 内容和名称都没有变化
		if ok && !(oldEntry.Name == entry.Name && oldEntry.Content == entry.Content) {
			// 如果有变化则更新
			editEntry := models.AppConfigEntryEdit{
				State:     models.ApproveStatePending,
				EntryUUID: oldEntry.UUID,
				Content:   entry.Content,
				Name:      entry.Name,
			}
			editEntry.Update(ops)
			editEntryList = append(editEntryList, editEntry)
		}
		// 如果不存在则创建
		if !ok {
			entry.Create(ops)
			entry.State = models.ApproveStatePending
			entry.ConfigUUID = p.ac.UUID
			entryList = append(entryList, entry)
		}
	}
	// 未提交的数据认为是删除的配置
	deleteIds := make([]string, 0)
	for _, entry := range oldEntryMap {
		deleteIds = append(deleteIds, entry.UUID)
	}
	if len(deleteIds) > 0 {
		if err := p.db.Where("uuid in (?)", deleteIds).
			Delete(models.AppConfigEntry{}).Error; err != nil {
			return err
		}
	}
	// 检查是否需要更新
	if len(entryList) == 0 && len(editEntryList) == 0 {
		return nil
	}
	if len(entryList) > 0 {
		if err := p.db.Save(&entryList).Error; err != nil {
			return err
		}
	}
	if len(editEntryList) > 0 {
		if err := p.db.Save(&editEntryList).Error; err != nil {
			return err
		}
	}
	// 通知管理员进行审批
	return eventbus.Publish(context.Background(), &events.AppConfigPluginNeedApprovalEvent{
		ConfigPlugin: string(PluginTypeKubernetes),
		AppUUID:      p.ac.AppUUID,
		ConfigUUID:   p.ac.UUID,
		User:         ops,
	})
}

// Load 从数据库加载配置
func (p *KubernetesConfigPlugin) Load() any {
	var entries []models.AppConfigEntry
	if err := p.db.Where("config_uuid = ?",
		p.ac.UUID).Find(&entries).Error; err != nil {
		return nil
	}
	// 如果对应的编辑版本存在则使用编辑版本的状态
	if len(entries) > 0 {
		uuidList := make([]string, 0)
		for _, entry := range entries {
			uuidList = append(uuidList, entry.UUID)
		}
		var editEntries []models.AppConfigEntryEdit
		if err := p.db.Where("entry_uuid in (?)",
			uuidList).Find(&editEntries).Error; err != nil {
			return nil
		}
		editEntryMap := make(map[string]models.AppConfigEntryEdit)
		for _, editEntry := range editEntries {
			editEntryMap[editEntry.EntryUUID] = editEntry
		}
		for i, entry := range entries {
			if editEntry, ok := editEntryMap[entry.UUID]; ok {
				entries[i].State = editEntry.State
				entries[i].Reason = editEntry.Reason
			}
		}
	}
	return entries
}

// Select 选择配置
func (p *KubernetesConfigPlugin) Select() ([]response.SelectResponse, error) {
	var entries []models.AppConfigEntry
	if err := p.db.Where("config_uuid = ? and state = ?",
		p.ac.UUID, models.ApproveStatePassed).Find(&entries).Error; err != nil {
		return nil, err
	}
	var ret []response.SelectResponse
	for _, entry := range entries {
		ret = append(ret, response.SelectResponse{
			Value: entry.UUID,
			Label: entry.Name,
		})
	}
	return ret, nil
}
