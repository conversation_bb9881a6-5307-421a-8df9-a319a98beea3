package plugins

import (
	"pipeline/pkg/common/request"
	"pipeline/pkg/models"
)

type ApplicationConfigPlugin struct {
	Key   string `json:"key"`   //  配置标识
	Name  string `json:"name"`  //  配置名称
	Value any    `json:"value"` //  配置内容
}

type ConfigHistoryRequest struct {
	UUID string `json:"uuid"`
	request.PageRequest
}

type ConfigVersionRequest struct {
	ConfigEntryUUID string `json:"configEntryUUID"`
	Version         uint64 `json:"version"`
}

type ConfigApprovalRequest struct {
	UUID   string              `json:"uuid"`
	Reason string              `json:"reason"` //  理由
	State  models.ApproveState `json:"state"`  //  状态(部分配置需要审批通过才能使用)
}

type ConfigSelectRequest struct {
	AppUUID   string `json:"appUUID"`
	ConfigKey string `json:"key"`
}

type CreateConfigPluginRequest struct {
	App      *models.ProjectApp `json:"app"`      // 应用
	Ops      string             `json:"ops"`      // 操作人
	Params   map[string]any     `json:"param"`    // 渲染参数
	Template string             `json:"template"` // 应用模板或者流水线模板
}

type ConfigTemplateListRequest struct {
	Plugin string `json:"plugin"` // 插件名称
}
