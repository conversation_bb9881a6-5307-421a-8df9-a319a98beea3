package plugins

import (
	"git.makeblock.com/makeblock-go/mysql/v2"
	"pipeline/config"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
)

type ConfigPlugin interface {
	Parse(value any) error
	Create(ops string) error
	Update(ops string) error
	Load() any
	Select() ([]response.SelectResponse, error)
}

type PluginType string

const (
	PluginTypeKubernetes PluginType = "kubernetes"
)

func NewAppConfigPlugin(key PluginType, ac *models.AppConfig) ConfigPlugin {
	engine := mysql.GetDB()
	switch key {
	case PluginTypeKubernetes:
		return NewKubernetesConfigPlugin(engine, ac)
	default:
		return nil
	}
}

// SaveVersion 保存一个版本
func SaveVersion(entry *models.AppConfigEntry, requester, reviewer string) error {
	engine := mysql.GetDB()
	version := models.AppConfigEntryVersion{
		ConfigEntryUUID: entry.UUID,
		Name:            entry.Name,
		Content:         entry.Content,
		State:           entry.State,
	}
	var versionNo uint64
	engine.Model(models.AppConfigEntryVersion{}).
		Where("config_entry_uuid = ?", entry.UUID).
		Order("version desc").Limit(1).Pluck("version", &versionNo)
	version.Version = versionNo + 1
	version.Create(requester)   // 版本创建人
	version.Modifier = reviewer // 版本审批人
	if err := engine.Create(&version).Error; err != nil {
		return err
	}
	// 保留最新的30条记录
	maxHistoryVersion := config.Items().AppConfigPlugins.MaxHistoryVersion
	var records []int64
	engine.Where("config_entry_uuid = ?", entry.UUID).
		Order("gmt_create desc").Offset(maxHistoryVersion).Pluck("id", &records)
	if len(records) > 0 {
		// 删除除最新10条以外的所有记录
		return engine.Where("config_entry_uuid = ? AND id in (?)", entry.UUID, records).
			Delete(models.AppConfigEntryVersion{}).Error
	}
	return nil
}
