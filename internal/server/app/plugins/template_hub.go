package plugins

import (
	"fmt"
	"git.makeblock.com/makeblock-go/log"
)

// TemplatePluginAction 模板插件动作(用于创建流水线之前需要创建的配置插件)
type TemplatePluginAction func(CreateConfigPluginRequest) (map[string]any, error)

var templatePluginHub = make(map[string]TemplatePluginAction)

func GetTemplatePluginAction(key string) TemplatePluginAction {
	return templatePluginHub[key]
}

func RegisterTemplatePluginAction(action TemplatePluginAction, keys ...string) {
	for _, name := range keys {
		templatePluginHub[name] = action
	}
}

func CreateAppConfigPlugin(template string, r CreateConfigPluginRequest) (map[string]any, error) {
	// 获取创建器
	if action := GetTemplatePluginAction(template); action != nil {
		log.Info(fmt.Sprintf("create necessary config plugin for template: %s", template))
		return action(r)
	}
	return nil, nil
}
