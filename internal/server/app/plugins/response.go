package plugins

import (
	"pipeline/pkg/common/model"
	"pipeline/pkg/models"
)

type VersionInfoResponse struct {
	UUID            string              `json:"uuid"`                                                                   //  uuid
	Creator         string              `json:"creator"`                                                                //  创建人
	Modifier        string              `json:"modifier"`                                                               //  修改人
	GmtCreate       model.Time          `json:"gmtCreate"`                                                              //  创建时间
	GmtModified     model.Time          `json:"gmtModified"`                                                            //  修改时间
	ConfigEntryUUID string              `gorm:"column:config_entry_uuid" db:"config_entry_uuid" json:"configEntryUUID"` //  配置关联UUID
	Version         uint64              `gorm:"column:version" db:"version" json:"version"`                             //  版本号码
	Name            string              `gorm:"column:name" db:"name" json:"name"`                                      //  版本名称
	Content         string              `gorm:"column:content;type:LONGTEXT" db:"content" json:"content"`               //  配置内容
	State           models.ApproveState `gorm:"column:state" db:"state" json:"state"`                                   //  状态(部分配置需要审批通过才能使用)
	Admin           bool                `gorm:"-" json:"admin,omitempty"`                                               //  是否是管理员
}
