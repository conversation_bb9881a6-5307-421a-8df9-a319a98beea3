package plugins

import (
	"gorm.io/gorm"
	"pipeline/pkg/models"
)

type configTemplateRepo struct {
	db *gorm.DB
}

func NewConfigTemplateRepo(db *gorm.DB) *configTemplateRepo {
	return &configTemplateRepo{db: db}
}

func (repo *configTemplateRepo) Info(id int64) (*models.AppConfigEntryTemplate, error) {
	var result models.AppConfigEntryTemplate
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *configTemplateRepo) Add(m *models.AppConfigEntryTemplate) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *configTemplateRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.AppConfigEntryTemplate{}).Error
}

func (repo *configTemplateRepo) Update(m *models.AppConfigEntryTemplate) (rowsAffected int64, err error) {
	db := repo.db.Model(models.AppConfigEntryTemplate{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *configTemplateRepo) List(r ConfigTemplateListRequest) ([]*models.AppConfigEntryTemplate, error) {
	var result []*models.AppConfigEntryTemplate
	query := repo.db.Model(&models.AppConfigEntryTemplate{})
	if r.Plugin != "" {
		query = query.Where("plugin = ?", r.Plugin)
	}
	if err := query.Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}
