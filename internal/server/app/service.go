package app

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"pipeline/internal/server/system/user"
	"pipeline/pkg/common/model"
	"pipeline/pkg/notify"
	"pipeline/pkg/notify/types"

	"os"
	"path/filepath"
	"pipeline/internal/server/app/plugins"
	"pipeline/internal/server/source"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"time"

	"pipeline/internal/server/credential"
	"pipeline/internal/server/logger"
	"pipeline/internal/server/pipeline"
	"pipeline/internal/server/project"

	"gopkg.in/yaml.v3"

	"pipeline/config"
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/transport/http"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

const (
	TemplateInitDir  = "appboot"
	ParamFileName    = "appboot.yaml"
	PipelineFileName = "pipeline.yaml"
)

type appService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewAppService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *appService {
	svc := &appService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *appService) Info(uuid, email string, admin, reviewer bool) (*InfoResponse, error) {
	repo := NewAppRepo(s.db)
	m, err := repo.GetByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	//查询绑定的关系
	list, err := repo.GetAppRelationShipWithName(m.UUID)
	if err != nil {
		return nil, err
	}
	info.Members = make([]RelationShipResponse, 0)
	info.Principals = make([]RelationShipResponse, 0)
	// rpc query account
	emails := make([]string, 0)
	for _, r := range list {
		emails = append(emails, r.UserUuid)
	}
	accounts, err := user.NewUserService(nil, s.db, s.redisClient).Page(user.PageRequest{
		Emails: emails,
		PageRequest: request.PageRequest{
			PageSize: model.MaxPageSize,
		},
	})
	if err != nil {
		return nil, err
	}
	// map account
	accountsMap := make(map[string]models.User)
	for _, acc := range accounts.List {
		accountsMap[acc.Email] = acc
	}
	//对关系进行分组
	for _, r := range list {
		user := RelationShipResponse{
			TypeOf: r.TypeOf,
		}
		acc, ok := accountsMap[r.UserUuid]
		if ok {
			user.Name = acc.Name
			user.Uuid = acc.Email
		} else {
			log.Printf("failed to get account by email: %v", r)
		}
		if r.TypeOf == common.Principal {
			info.Principals = append(info.Principals, user)
			if r.UserUuid == email {
				info.RelationShip = common.Principal
			}
		}
		if r.TypeOf == common.Member {
			info.Members = append(info.Members, user)
			if r.UserUuid == email {
				info.RelationShip = common.Member
			}
		}
	}
	//对应的项目
	project, err := project.NewInfoRepo(s.db).GetByUUID(m.ProjectUuid)
	if err != nil {
		return nil, err
	}
	info.Project = ProjectResponse{
		Name:  project.Name,
		Value: project.UUID,
	}

	//是否是管理员
	if admin {
		info.RelationShip = common.Principal
	}

	// 查找插件配置
	configList, err := plugins.NewConfigRepo(s.db).GetConfigPluginList(m.UUID)
	if err != nil {
		return nil, err
	}
	info.Configs = configList

	info.Admin = admin
	info.Reviewer = reviewer

	return &info, nil
}

func (s *appService) Add(r request.AppAddRequest, userName string) (path *string, err error) {
	repo := NewAppRepo(s.db)
	var m models.ProjectApp
	err = copier.Copy(&m, &r)
	m.Create(userName)
	if err != nil {
		return nil, err
	}
	//渲染模板
	absPath, err := util.RemoveHomeDir(config.Items().Workspace)
	if err != nil {
		return nil, err
	}
	fromPath := filepath.Join(absPath, "templates", r.Template)
	toPath := filepath.Join(absPath, "app", r.Identity)
	ctx := context.Background()
	// 适配模板
	if r.UseAppTemplate {
		// 多实例情况下保证模板文件存在,判断目录下的文件夹是否存在, 不存在则进行拉取
		var exist bool
		if exist, err = util.HasDir(fromPath); err != nil {
			return nil, err
		}
		if !exist {
			log.Info("template file not exist, try to fetch from remote")
			if err = s.TemplatesRefresh(); err != nil {
				return nil, err
			}
		}
		// 前置处理一些参数
		_ = eventbus.Publish(ctx, &events.ProjectAppCreateBeforeEvent{
			App:     &m,
			Request: &r,
		})
		// 因为需要提前创建配置插件
		var configPluginMaps map[string]any
		if configPluginMaps, err = plugins.CreateAppConfigPlugin(r.Template,
			plugins.CreateConfigPluginRequest{
				App:      &m,
				Ops:      userName,
				Params:   r.Params,
				Template: r.Template,
			}); err != nil {
			return nil, err
		}
		r.Params = util.MergeMapAny(r.Params, configPluginMaps)
		// 渲染模板
		if err = util.RenderFiles(fromPath, toPath, r.Params); err != nil {
			return nil, err
		}
	}
	// 判断是否需要创建pipeline
	if r.CreatePipeline {
		if err = s.createPipeline(m.UUID, toPath, userName); err != nil {
			return nil, err
		}
	}
	// 移除掉.init文件夹
	err = util.DeleteFolder(filepath.Join(toPath, TemplateInitDir))
	if err != nil {
		return nil, err
	}
	// gitlab提交代码
	sourceRepo := source.NewSourceRepo(mysql.GetDB())
	sc, err := sourceRepo.GetByUUID(r.Source)
	if err != nil {
		return nil, err
	}
	codeSource := source.NewCodeSourceService(sc)
	//创建仓库
	cr, err := codeSource.CreateRepository(source.CreateRepoOptions{
		Name:        r.Identity,
		NamespaceId: r.NamespaceId,
		Desc:        r.Description,
	})
	if err != nil {
		return nil, err
	}
	// 上传代码
	if r.UseAppTemplate {
		if _, err = codeSource.UploadFiles(source.UploadOptions{
			Branch:        "main",
			CommitMessage: "Init commit",
			ProjectId:     cr.ProjectId,
			Path:          toPath,
		}); err != nil {
			return nil, err
		}
	}
	//保存关联关系
	ref := make([]models.ProjectAppOfUser, 0)
	now := time.Now()
	for _, principal := range r.Principals {
		ref = append(ref, models.ProjectAppOfUser{
			UserUuid:  principal,
			AppUuid:   m.UUID,
			Creator:   userName,
			TypeOf:    common.Principal,
			GmtCreate: now,
		})
	}
	for _, principal := range r.Members {
		ref = append(ref, models.ProjectAppOfUser{
			UserUuid:  principal,
			AppUuid:   m.UUID,
			Creator:   userName,
			TypeOf:    common.Member,
			GmtCreate: now,
		})
	}
	err = repo.AddAppOfUser(ref)
	if err != nil {
		return nil, err
	}
	// 设置app基本信息
	m.RepoId = cr.ProjectId
	m.SSHURLToRepo = cr.SSHURLToRepo
	m.HTTPURLToRepo = cr.HTTPURLToRepo
	// 保存
	if _, err = repo.Add(&m); err != nil {
		return nil, err
	}
	// 将当前用户加入到 gitlab 项目成员中
	if err = putUserToProject(ref, cr.ProjectId, sc, r.Source); err != nil {
		return nil, err
	}
	// 创建webhook回调
	if err = s.CreateAPPWebhook(m, codeSource); err != nil {
		return nil, err
	}
	//是否需要下载代码,需要则进行压缩成zip
	zipPath := toPath + common.ZipCompressionExtension
	if r.Download {
		if err = util.ZipFolder(toPath, zipPath); err != nil {
			return nil, err
		}
	}

	// publish event to monitor
	eventbus.PublishAsync(ctx, &events.ProjectAppMemberUpdateEvent{App: &m})

	// init some resources
	eventbus.PublishAsync(ctx, &events.ProjectAppCreatedAfterEvent{
		App:     &m,
		Request: &r,
	})

	// 如果当前用户不在项目中, 则需要将当前用户加入到项目中, 默认是参与者
	eventbus.PublishAsync(ctx, &events.AutoAddUserToProjectEvent{
		ProjectUUID: r.ProjectUuid,
		Principals:  r.Principals,
		Members:     r.Members,
		Operator:    userName,
	})

	return &zipPath, nil
}

//将当前用户加入到 gitlab 项目成员中

func putUserToProject(ref []models.ProjectAppOfUser, projectId int, sc *source.ProjectAppCodeSource, so string) error {

	sourceRepo := source.NewSourceRepo(mysql.GetDB())
	codeSource := source.NewCodeSourceService(sc)
	Map := make(map[string]struct{})
	for _, user := range ref {
		Map[user.UserUuid] = struct{}{}
	}
	Emails := make([]string, 0)
	for email := range Map {
		Emails = append(Emails, email)
	}
	if Emails == nil || len(Emails) > 0 {
		codeSourceUsers, err := sourceRepo.ListCodeSourceUser(
			source.ListCodeSourceUserRequest{
				Source: so,
				Emails: Emails,
			})
		if err != nil {
			return fmt.Errorf("failed to get code source user: %v", err)
		}
		ids := make([]string, 0)
		for _, user := range codeSourceUsers {
			ids = append(ids, user.UserID)
		}
		// 添加成员
		if len(ids) > 0 {
			opt := source.AddProjectMemberOptions{
				ProjectId: projectId,
				UserIds:   ids,
			}
			_, err = codeSource.AddRepositoryMember(opt)
			if err != nil {
				return fmt.Errorf("failed to add project member: %v", err)
			}
		}
	}
	return nil
}

// 创建pipeline
func (s *appService) createPipeline(uuid, toPath, ops string) error {
	log.Info("auto create app pipeline | app_uuid", log.Any("app_uuid", uuid))
	// 读取本地pipeline.yaml文件
	localPath := filepath.Join(fmt.Sprintf("%v/%v/%v", toPath, TemplateInitDir, PipelineFileName))
	_, err := os.Stat(localPath)
	if err == nil {
		paramBytes, pErr := os.ReadFile(localPath)
		if pErr != nil {
			return fmt.Errorf("failed to auto create app pipeline:  %s  err : %v", localPath, err)
		}

		if len(paramBytes) != 0 {
			// 2. 渲染后调取创建app pipeline
			err = s.AddDefaultAppPipeline(ops, uuid, paramBytes)
			if err != nil {
				return fmt.Errorf("failed to auto create app pipeline:  %s  err : %v", localPath, err)
			}
			log.Info("auto create app pipeline success")
		}
	}
	return nil
}

func (s *appService) AddDefaultAppPipeline(ops string, appUUID string, paramBytes []byte) error {
	repo := pipeline.NewInfoRepo(s.db)

	pipelines := bytes.Split(paramBytes, []byte("---"))
	pipelineModel := make([]*models.PipelineInfo, 0)
	type PipelineDefine struct {
		Name string
		Desc string
	}
	for _, content := range pipelines {
		if len(content) == 0 {
			continue
		}
		define := string(content)
		var parsePipeline PipelineDefine
		err := yaml.Unmarshal([]byte(define), &parsePipeline)
		if err != nil {
			return err
		}
		m := &models.PipelineInfo{
			AppUuid:     appUUID,
			Content:     define,
			Name:        parsePipeline.Name,
			Description: parsePipeline.Desc,
		}
		m.Create(ops)
		pipelineModel = append(pipelineModel, m)
	}

	return repo.AddBatch(pipelineModel)
}

// CreateAPPWebhook 一个项目只创建一条
func (s *appService) CreateAPPWebhook(pApp models.ProjectApp, sc source.CodeSource) error {
	_, err := sc.CreateWebhook(source.CreateWebhookOptions{
		WebhookURL: fmt.Sprintf("%s/api/v1/pipeline/run/gitlab/%s", config.Items().Pipeline.WebHookEndpoint, pApp.UUID),
		ProjectId:  pApp.RepoId,
	})
	if err != nil {
		log.ErrorE("failed to create webhook: %v", err)
	}
	return err
}

func (s *appService) Delete(ids []string, ops string) error {
	repo := NewAppRepo(s.db)
	if err := repo.SoftDelete(ids, ops); err != nil {
		return err
	}
	// delete pipeline
	pr := pipeline.NewInfoRepo(s.db)
	for _, uuid := range ids {
		if err := pr.SoftDeleteByAppUUID(uuid); err != nil {
			return err
		}
	}
	return nil
}

func (s *appService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewAppRepo(s.db)
	m, err := repo.GetByUUID(r.UUID)
	if err != nil {
		return 0, err
	}
	if err = model.CheckVersionConflict(r.GmtModified, m.GmtModified); err != nil {
		return 0, err
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	// 处理关联关系
	if err = s.updateRelationShip(r, m, ops); err != nil {
		return 0, err
	}
	// 更新配置
	if err = s.updateConfig(r, ops); err != nil {
		return 0, err
	}
	m.Update(ops)
	// 如果当前用户不在项目中, 则需要将当前用户加入到项目中, 默认是参与者
	eventbus.PublishAsync(context.Background(), &events.AutoAddUserToProjectEvent{
		ProjectUUID: r.ProjectUuid,
		Principals:  r.Principals,
		Members:     r.Members,
		Operator:    ops,
	})
	return repo.Update(m)
}

// UpdateRelationShip 更新关系
func (s *appService) updateRelationShip(req UpdateRequest, m *models.ProjectApp, ops string) error {
	repo := NewAppRepo(s.db)
	//处理关联关系
	ships, err := repo.GetProjectRelationShipList(m.UUID)
	if err != nil {
		return err
	}
	principalsMap := make(map[string]models.ProjectAppOfUser)
	membersMap := make(map[string]models.ProjectAppOfUser)
	//对关系进行分组
	for _, r := range ships {
		if r.TypeOf == common.Principal {
			principalsMap[r.UserUuid] = r
		}
		if r.TypeOf == common.Member {
			membersMap[r.UserUuid] = r
		}
	}
	now := time.Now()
	addProjectInfoOfUser := make([]models.ProjectAppOfUser, 0)
	//成员
	for _, member := range req.Members {
		//已经存在无需操作
		if _, ok := membersMap[member]; ok {
			delete(membersMap, member)
			continue
		}
		//需要新增
		addProjectInfoOfUser = append(addProjectInfoOfUser, models.ProjectAppOfUser{
			UserUuid:  member,
			AppUuid:   m.UUID,
			TypeOf:    common.Member,
			Creator:   ops,
			GmtCreate: now,
		})
	}
	//map中剩下的都是需要删除的
	//管理员
	for _, principal := range req.Principals {
		//已经存在无需操作
		if _, ok := principalsMap[principal]; ok {
			delete(principalsMap, principal)
			continue
		}
		//需要新增
		addProjectInfoOfUser = append(addProjectInfoOfUser, models.ProjectAppOfUser{
			UserUuid:  principal,
			AppUuid:   m.UUID,
			TypeOf:    common.Principal,
			Creator:   ops,
			GmtCreate: now,
		})
	}

	// 先删除, 因为如果先新增, 有可能会导致key冲突
	deleteIds := make([]int64, 0)
	for _, shipResponse := range principalsMap {
		deleteIds = append(deleteIds, shipResponse.ID)
	}
	for _, shipResponse := range membersMap {
		deleteIds = append(deleteIds, shipResponse.ID)
	}
	if len(deleteIds) > 0 {
		if err = repo.DeleteRelationShip(deleteIds); err != nil {
			return err
		}
	}

	// publish event to monitor
	eventbus.PublishAsync(context.Background(), &events.ProjectAppMemberUpdateEvent{App: m})

	// 新增
	return repo.AddAppOfUser(addProjectInfoOfUser)
}

// updateConfig 更新配置
func (s *appService) updateConfig(req UpdateRequest, ops string) error {
	repo := plugins.NewConfigRepo(s.db)
	for _, cf := range req.Configs {
		// 新增/更新配置
		ac := models.AppConfig{
			Key:     cf.Key,
			Name:    cf.Name,
			AppUUID: req.UUID,
		}
		// 查询是否存在
		exist, _ := repo.Exist(ac)
		// 已经存在则进行更新
		if exist != nil {
			exist.Name = cf.Name
			if err := s.updateConfigPlugin(exist, cf, ops); err != nil {
				return err
			}
			// 不存在则进行新增
		} else {
			// 新增
			if err := s.addConfigPlugin(&ac, cf, ops); err != nil {
				return err
			}
		}
	}
	return nil
}

// updateConfigPlugin 更新配置插件
func (s *appService) updateConfigPlugin(ac *models.AppConfig, cf plugins.ApplicationConfigPlugin, ops string) error {
	repo := plugins.NewConfigRepo(s.db)
	// 解析配置类型
	acp := plugins.NewAppConfigPlugin(plugins.PluginType(cf.Key), ac)
	if err := acp.Parse(cf.Value); err != nil {
		return err
	}
	if err := acp.Update(ops); err != nil {
		return err
	}
	// 保存配置
	ac.Update(ops)
	_, err := repo.Update(ac)
	return err
}

// addConfigPlugin 新增配置插件
func (s *appService) addConfigPlugin(ac *models.AppConfig, cf plugins.ApplicationConfigPlugin, ops string) error {
	repo := plugins.NewConfigRepo(s.db)
	// 解析配置类型
	ac.Create(ops)
	acp := plugins.NewAppConfigPlugin(plugins.PluginType(cf.Key), ac)
	if err := acp.Parse(cf.Value); err != nil {
		return err
	}
	if err := acp.Create(ops); err != nil {
		return err
	}
	// 保存配置
	_, err := repo.Add(ac)
	return err
}

func (s *appService) Page(r PageRequest, uuid string, isAdmin, isAppReviewer bool) (response.PageModel, error) {
	repo := NewAppRepo(s.db)
	pm, err := repo.Page(r, uuid, isAdmin, isAppReviewer)
	return pm, err
}

func (s *appService) List(r ListRequest, uuid string, admin bool) (results []*InfoResponse, err error) {
	repo := NewAppRepo(s.db)
	return repo.List(r, uuid, admin)
}

func (s *appService) TemplatesRefresh() error {
	path := config.Items().Workspace
	absPath, err := util.RemoveHomeDir(path)
	if err != nil {
		return err
	}
	templatesDir := filepath.Join(absPath, "templates")
	// 获取配置
	cert, err := credential.NewCredentialRepo(s.db).GetByUUID(credential.InfoRequest{
		UUID:    "dd4f12518ff5440d9a32f276f9bb7521",
		Decrypt: true,
	})
	if err != nil {
		return err
	}
	// define entry
	type FetchTemplateCertEntry struct {
		Url    string `json:"url"`
		Token  string `json:"token"`
		Branch string `json:"branch"`
	}
	entry := util.JSONStringToStruct[FetchTemplateCertEntry](cert.Value)
	auth := &http.BasicAuth{
		Username: "token",
		Password: entry.Token,
	}
	// 检查本地目录是否存在
	if _, err = os.Stat(templatesDir); os.IsNotExist(err) {
		// 本地目录不存在，执行git.Clone
		cloneOptions := git.CloneOptions{
			URL:           entry.Url,
			Auth:          auth,
			ReferenceName: plumbing.NewBranchReferenceName(entry.Branch),
			//Depth:         1, // 提速
		}
		_, err = git.PlainCloneContext(context.Background(), templatesDir, false, &cloneOptions)
		if err != nil {
			log.Error(fmt.Sprintf("Unable to clone %v : %v", cloneOptions, err))
			return err
		}
		if err = os.Chmod(templatesDir, 0o755); err != nil {
			return err
		}
	} else {
		// 本地目录存在，执行git.Pull
		r, err := git.PlainOpen(templatesDir)
		if err != nil {
			log.Error(fmt.Sprintf("Unable to open repository : %v", err))
			return err
		}
		// 获取工作树对象
		worktree, err := r.Worktree()
		if err != nil {
			log.Error(fmt.Sprintf("Unable to get worktree : %v", err))
			return err
		}
		pullOptions := git.PullOptions{
			//RemoteName:    "origin",
			ReferenceName: plumbing.NewBranchReferenceName(entry.Branch),
			Auth:          auth,
			//Depth:         1,
		}
		err = worktree.PullContext(context.Background(), &pullOptions)
		if err != nil && !errors.Is(err, git.NoErrAlreadyUpToDate) {
			log.Error(fmt.Sprintf("Unable to pull %v : %v", templatesDir, err))
			return err
		}
	}
	return nil
}

func (s *appService) Templates() (templates []map[string]string, err error) {
	path := config.Items().Workspace
	absPath, err := util.RemoveHomeDir(path)
	if err != nil {
		return nil, err
	}
	ret := make([]map[string]string, 0)
	dir := filepath.Join(absPath, "templates")
	files, err := os.ReadDir(dir)

	// check if the file is not exist
	if errors.Is(err, os.ErrNotExist) {
		//e := s.TemplatesRefresh()
		//if e != nil {
		//	return nil, e
		//}
		err = nil
	}

	// error
	if err != nil {
		return nil, err
	}

	// check if the file is not a directory
	for _, file := range files {
		//排查掉隐藏文件 如 .git
		if file.Name() != "" && file.Name()[0] == '.' {
			continue
		}
		if file.IsDir() {
			entry := map[string]string{"name": file.Name()}
			// 读取该目录下的 appboot/appboot.yaml 中的desc字段
			fullPath := filepath.Join(dir, file.Name(), TemplateInitDir, ParamFileName)
			// Open and read the file
			fileBytes, err := os.ReadFile(fullPath)
			if err != nil {
				log.Printf("Failed to read file: %v", err)
			} else {
				// Unmarshal the file content into a map
				var fileContent map[string]any
				err = yaml.Unmarshal(fileBytes, &fileContent)
				if err != nil {
					log.Printf("Failed to unmarshal yaml: %v", err)
				} else {
					// Get the desc field and add it to the entry map
					if desc, ok := fileContent["desc"]; ok {
						entry["desc"] = fmt.Sprintf("%v", desc)
					}
				}
			}
			ret = append(ret, entry)
		}
	}
	return ret, nil
}

func (s *appService) Parameters(r request.ParametersRequest) (string, error) {
	absPath, err := util.RemoveHomeDir(config.Items().Workspace)
	if err != nil {
		return "", err
	}
	absPath = filepath.Join(absPath, "templates")
	//
	fullPath := filepath.Join(absPath, fmt.Sprintf("%s/%s/%s", r.Name, TemplateInitDir, ParamFileName))
	var param string
	if _, err := os.Stat(fullPath); err == nil {
		paramBytes, err := os.ReadFile(fullPath)
		if err != nil {
			return "", fmt.Errorf("failed to read file:  %s/%s/%s  err : %v", r.Name, TemplateInitDir, ParamFileName, err)
		}
		param = string(paramBytes)
	}
	return param, nil
}

func (s *appService) Branches(r request.BranchesRequest) ([]*BranchesResponse, error) {
	app, err := NewAppRepo(mysql.GetDB()).GetByUUID(r.AppUUID)
	if err != nil {
		return nil, err
	}
	sce, err := source.NewSourceRepo(s.db).GetByUUID(app.Source)
	if err != nil {
		return nil, err
	}
	//获取代码源实例
	branchesResponse, err := source.NewCodeSourceService(sce).ListBranches(
		source.BranchesOptions{
			Name:      r.Name,
			ProjectId: app.RepoId,
		},
	)
	if err != nil {
		return nil, err
	}
	branches := make([]*BranchesResponse, 0)
	for _, v := range branchesResponse.List {
		branches = append(branches, &BranchesResponse{
			Label: v,
			Value: v,
		})
	}
	return branches, nil
}

func (s *appService) BranchesOrTags(r request.BranchesRequest) ([]BranchesOrTagsOptions, error) {
	app, err := NewAppRepo(mysql.GetDB()).GetByUUID(r.AppUUID)
	if err != nil {
		return nil, err
	}
	sce, err := source.NewSourceRepo(s.db).GetByUUID(app.Source)
	if err != nil {
		return nil, err
	}
	branchesOrTagsOptions := make([]BranchesOrTagsOptions, 0)
	// 获取代码源实例
	sourceSvc := source.NewCodeSourceService(sce)
	// 获取分支
	branchesResponse, err := sourceSvc.ListBranches(
		source.BranchesOptions{
			Name:      r.Name,
			ProjectId: app.RepoId,
		},
	)
	if err != nil {
		return nil, err
	}
	branches := make([]BranchesOrTagsEntry, 0)
	for _, b := range branchesResponse.List {
		branches = append(branches, BranchesOrTagsEntry{
			Key:   b,
			Label: b,
			Value: b,
			Type:  common.Branch,
		})
	}
	branchesOrTagsOptions = append(branchesOrTagsOptions, BranchesOrTagsOptions{
		Label:   common.Branch,
		Options: branches,
	})
	// 查询tag
	tagsResponse, err := sourceSvc.ListTags(source.TagsOptions{
		Name:      r.Name,
		ProjectId: app.RepoId,
	})
	if err != nil {
		return nil, err
	}
	tags := make([]BranchesOrTagsEntry, 0)
	for _, t := range tagsResponse.List {
		tags = append(tags, BranchesOrTagsEntry{
			Key:   t.Name,
			Label: t.Name,
			Value: t.Name,
			Type:  common.Tag,
		})
	}
	branchesOrTagsOptions = append(branchesOrTagsOptions, BranchesOrTagsOptions{
		Label:   common.Tag,
		Options: tags,
	})
	return branchesOrTagsOptions, nil
}

func (s *appService) Import(r ImportRequest, ops string) error {
	repo := NewAppRepo(s.db)
	var m models.ProjectApp
	err := copier.Copy(&m, &r)
	m.Create(ops)
	if err != nil {
		return err
	}
	//保存用户关联关系
	ref := make([]models.ProjectAppOfUser, 0)
	now := time.Now()
	for _, principal := range r.Principals {
		ref = append(ref, models.ProjectAppOfUser{
			UserUuid:  principal,
			AppUuid:   m.UUID,
			Creator:   ops,
			TypeOf:    common.Principal,
			GmtCreate: now,
		})
	}
	for _, principal := range r.Members {
		ref = append(ref, models.ProjectAppOfUser{
			UserUuid:  principal,
			AppUuid:   m.UUID,
			Creator:   ops,
			TypeOf:    common.Member,
			GmtCreate: now,
		})
	}
	err = repo.AddAppOfUser(ref)
	if err != nil {
		return err
	}
	// 设置app基本信息
	m.RepoId = r.RepoId
	sourceRepo := source.NewSourceRepo(mysql.GetDB())
	sc, err := sourceRepo.GetByUUID(r.Source)
	if err != nil {
		return err
	}
	codeSource := source.NewCodeSourceService(sc)
	infoRepo, err := codeSource.GetRepository(source.InfoRepoOptions{
		ProjectId: r.RepoId,
	})
	if err != nil {
		return err
	}
	//查询仓库地址
	m.SSHURLToRepo = infoRepo.SSHURLToRepo
	m.HTTPURLToRepo = infoRepo.HTTPURLToRepo
	// 保存
	_, err = repo.Add(&m)
	if err != nil {
		return err
	}
	// 创建webhook
	if err = s.CreateAPPWebhook(m, codeSource); err != nil {
		return err
	}
	// publish event to monitor
	eventbus.PublishAsync(context.Background(), &events.ProjectAppMemberUpdateEvent{App: &m})

	// 如果当前用户不在项目中, 则需要将当前用户加入到项目中, 默认是参与者
	eventbus.PublishAsync(context.Background(), &events.AutoAddUserToProjectEvent{
		ProjectUUID: r.ProjectUuid,
		Principals:  r.Principals,
		Members:     r.Members,
		Operator:    ops,
	})
	return nil
}

func (s *appService) GetByLoggerUUID(loggerUUID, email string, isAdmin bool) (*InfoResponse, error) {
	//日志查流水线
	loggerModel, err := logger.NewLoggerRepo(s.db).GetByUUID(loggerUUID)
	if err != nil {
		return nil, err
	}
	//流水线查应用
	pipelineInfo, err := pipeline.NewInfoRepo(s.db).GetByUUID(loggerModel.PipelineUuid)
	if err != nil {
		return nil, err
	}
	//对应的应用
	appRepoSvc := NewAppRepo(s.db)
	appModel, err := appRepoSvc.GetByUUID(pipelineInfo.AppUuid)
	if err != nil {
		return nil, err
	}
	r := &InfoResponse{}
	err = copier.Copy(&r, &appModel)
	if err != nil {
		return nil, err
	}
	// 查询当前用户在应用中的角色
	if isAdmin {
		r.RelationShip = common.Principal
	} else {
		// get from db
		relationShip, err := appRepoSvc.GetAppRelationShipByAppUUIDAndEmail(appModel.UUID, email)
		if err != nil {
			log.Error("failed to get app relation ship",
				log.Any("app_uuid", appModel.UUID), log.Any("email", email),
				log.Any("error", err.Error()))
		}
		if relationShip != nil {
			r.RelationShip = relationShip.TypeOf
		}
	}
	return r, nil
}

func (s *appService) History(req plugins.ConfigHistoryRequest) (*response.PageModelV2[plugins.VersionInfoResponse], error) {
	repo := plugins.NewConfigRepo(s.db)
	return repo.History(req)
}

func (s *appService) Version(req plugins.ConfigVersionRequest) (*plugins.VersionInfoResponse, error) {
	repo := plugins.NewConfigRepo(s.db)
	// 如果查找的上一个版本是0那说明只有一个旧版本,所以返回当前版本去对比
	if req.Version <= 0 {
		return nil, nil
	}
	ret, err := repo.Version(req)
	// 如果找不到版本信息，返回空数据
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return ret, err
}

// Approval 审批
func (s *appService) Approval(req plugins.ConfigApprovalRequest, admin bool, reviewer string) error {
	// must be admin
	if !admin {
		return errors.New("permission denied")
	}
	repo := plugins.NewConfigRepo(s.db)
	// get entry, online version
	entry, err := repo.GetConfigEntryByUUID(req.UUID)
	if err != nil {
		return err
	}
	// get edit entry, edit version
	edit, _ := repo.GetEditConfigEntryByUUID(req.UUID)
	// requester
	requester := util.SetDefaultIfEmpty(entry.Modifier, entry.Creator)
	if edit != nil {
		requester = edit.Modifier
	}
	// 审批通过更新线上版本
	if req.State == models.ApproveStatePassed {
		entry.State = req.State
		// 新创建的没有编辑版本
		if edit != nil {
			// 防止重复审批
			if edit.State != models.ApproveStatePending {
				return response.ConfigStateInvalidError(edit.Name)
			}
			entry.Name = edit.Name
			entry.Content = edit.Content
			edit.State = req.State
		}
		entry.Update(requester)
		if err = repo.UpdateConfigEntry(entry); err != nil {
			return err
		}
		// 保存一个版本
		if e := plugins.SaveVersion(entry, requester, reviewer); e != nil {
			return err
		}
	}
	ev := &events.AppConfigPluginApprovalDoneEvent{
		AppConfigEntry: entry,
		State:          req.State,
		Reason:         req.Reason,
	}
	// 更新编辑版本
	if edit != nil {
		edit.State = req.State
		edit.Reason = req.Reason
		ev.Receiver = edit.Modifier
		edit.Update(reviewer)
		if err = repo.SaveConfigEntryEdit(edit); err != nil {
			return err
		}
	} else {
		// 没有编辑版本则创建
		edit = &models.AppConfigEntryEdit{
			EntryUUID: entry.UUID,
			Name:      entry.Name,
			Content:   entry.Content,
			State:     req.State,
			Reason:    req.Reason,
		}
		edit.Create(entry.Creator)
		if err = repo.SaveConfigEntryEdit(edit); err != nil {
			return err
		}
	}
	// notify receiver
	if ev.Receiver == "" {
		ev.Receiver = entry.Creator
	}
	return eventbus.Publish(context.Background(), ev)
}

// Select 提供给前端下拉框数据选择
func (s *appService) Select(req plugins.ConfigSelectRequest) ([]response.SelectResponse, error) {
	repo := plugins.NewConfigRepo(s.db)
	pluginInfo, err := repo.GetConfigPluginInfoByAppUUIDAndKey(req.AppUUID, req.ConfigKey)
	// 找不到默认返回空
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	// 非空错误
	if err != nil {
		return nil, err
	}
	return plugins.NewAppConfigPlugin(plugins.PluginTypeKubernetes, pluginInfo).Select()
}

func (s *appService) Review(req ReviewRequest, ops string) error {
	repo := plugins.NewConfigRepo(s.db)
	m, err := repo.GetConfigEntryByUUID(req.UUID)
	if err != nil {
		return err
	}
	// 获取编辑版本
	edit, _ := repo.GetEditConfigEntryByUUID(req.UUID)
	if edit != nil && edit.State != models.ApproveStatePending {
		return errors.New("approval state is not pending")
	}
	info, err := repo.GetConfigPluginInfo(m.ConfigUUID)
	if err != nil {
		return err
	}
	// 通知管理员进行审批
	eventbus.PublishAsync(context.Background(), &events.AppConfigPluginNeedApprovalEvent{
		ConfigPlugin: info.Key,
		AppUUID:      info.AppUUID,
		ConfigUUID:   m.ConfigUUID,
		User:         ops,
	})
	return nil
}

// Edit 编辑版本
func (s *appService) Edit(req ReviewRequest) (*models.AppConfigEntryEdit, error) {
	repo := plugins.NewConfigRepo(s.db)
	edit, err := repo.GetEditConfigEntryByUUID(req.UUID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 没有则使用当前版本
		entry, e := repo.GetConfigEntryByUUID(req.UUID)
		if e != nil {
			return nil, e
		}
		return &models.AppConfigEntryEdit{
			Content: entry.Content,
		}, nil
	}
	if err != nil {
		return nil, err
	}
	return edit, nil
}

// Entry 获取配置实例一个配置项
func (s *appService) Entry(req ReviewRequest) (*models.AppConfigEntry, error) {
	repo := plugins.NewConfigRepo(s.db)
	entry, err := repo.GetConfigEntryByUUID(req.UUID)
	if err != nil {
		return nil, err
	}
	return entry, nil
}

func (s *appService) RequestJoin(req RequestJoinRequest, email string) error {
	// 发生通知信息给项目管理员
	var appPrincipals []string
	if err := s.db.Model(&models.ProjectAppOfUser{}).
		Select("distinct user_uuid").
		Where("app_uuid = ? and type_of = ?", req.AppUUID, common.Principal).
		Find(&appPrincipals).Error; err != nil {
		return err
	}
	if len(appPrincipals) == 0 {
		log.Error("app has no principal",
			log.Any("app_uuid", req.AppUUID))
		return nil
	}
	// 查询应用
	var projectApp models.ProjectApp
	if err := s.db.Where("uuid = ?", req.AppUUID).
		First(&projectApp).Error; err != nil {
		return err
	}
	dataSource := make(map[string]any)
	dataSource["time"] = util.NowDateTime()
	dataSource["user"] = email
	dataSource["role"] = req.Role
	dataSource["operator"] = ""
	dataSource["remark"] = ""
	dataSource["disable"] = false
	dataSource["reason"] = req.Reason
	dataSource["resource"] = projectApp.UUID
	dataSource["color"] = models.ColorMap[models.ApproveStatePending]
	dataSource["name"] = fmt.Sprintf("%s (应用)", projectApp.Name)
	// 保存申请信息
	entry := models.ProjectAppRequest{
		Type: models.RequestTypeApp,
	}
	entry.Create(email)
	dataSource["uuid"] = entry.UUID
	entry.DataSource = util.ToJSONString(dataSource)
	if err := notify.SendNotification(context.Background(), types.NotifyData{
		Type:         types.FeishuPerson,
		Receivers:    appPrincipals,
		TemplateType: types.ProjectAppRequest,
		DataSource:   dataSource,
	}); err != nil {
		return err
	}
	return s.db.Create(&entry).Error
}
