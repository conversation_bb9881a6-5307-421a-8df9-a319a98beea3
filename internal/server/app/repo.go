package app

import (
	"gorm.io/gorm"
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
	"pipeline/pkg/models"
	"pipeline/pkg/util"
)

type appRepo struct {
	db *gorm.DB
}

func NewAppRepo(db *gorm.DB) *appRepo {
	return &appRepo{db: db}
}

func (repo *appRepo) Info(id int64) (*models.ProjectApp, error) {
	var result models.ProjectApp
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *appRepo) Add(m *models.ProjectApp) (id int64, err error) {
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *appRepo) SoftDelete(ids []string, osp string) error {
	//因为identity是唯一的，所以软删除时，需要将deleted字段置为true，将identity置为identity-uuid防止重复
	return repo.db.Model(&models.ProjectApp{}).
		Where("uuid in (?)", ids).
		Updates(map[string]any{
			"identity": gorm.Expr("concat(identity,concat('-',uuid))"),
			"deleted":  true,
			"modifier": osp,
		}).Error
}

func (repo *appRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&models.ProjectApp{}).Error
}

func (repo *appRepo) Update(m *models.ProjectApp) (rowsAffected int64, err error) {
	db := repo.db.Model(models.ProjectApp{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *appRepo) Page(r PageRequest, uuid string, isAdmin, isAppReviewer bool) (pm response.PageModel, err error) {
	var query *gorm.DB
	// (系统管理员)查询所有app
	if isAdmin || r.TypeOf == common.All {
		query = repo.db.Table("project_app pa").
			Select("pa.*,pi.name pn").
			Joins("left join project_info pi on pa.project_uuid = pi.uuid")
	} else {
		//（普通用户）只查询当前用户管理和参与的app
		query = repo.db.Table("project_app_of_user paou").
			Select("pa.*,paou.type_of").
			Joins("left join project_app pa on paou.app_uuid = pa.uuid and paou.user_uuid = ?", uuid).
			Joins("left join project_info pi on pa.project_uuid = pi.uuid").
			Where("pa.uuid is not null")
		if r.TypeOf != "" {
			// 管理者/参与者
			if r.TypeOf != common.All && r.TypeOf != common.MemberAndPrincipal {
				query = query.Where("paou.type_of = ?", r.TypeOf)
			}
		}
	}
	//指定项目
	if r.Project != "" {
		query = query.Where("pi.uuid = ?", r.Project)
	}
	//名称模糊查询
	if r.Name != "" {
		query = query.Where("pa.name like ?", util.Like(r.Name))
	}
	//删除标记
	query = query.Where("pa.deleted = false")
	//查询记录数
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*InfoResponse
	if err := query.Order("gmt_create desc").Limit(limit).
		Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	// 如果是管理员或者审核员，则查询全部应用
	if isAdmin || isAppReviewer {
		for i := range list {
			list[i].RelationShip = common.Principal
		}
	} else if r.TypeOf == common.All {
		// 查询全部应用（全局，用于申请加入）需要返回当前用户与应用的关系
		appUUIDList := make([]string, 0)
		for _, app := range list {
			appUUIDList = append(appUUIDList, app.Uuid)
		}
		if len(appUUIDList) == 0 {
			return pm, nil
		}
		var appOfUserList []models.ProjectAppOfUser
		repo.db.Table("project_app_of_user paou").
			Select("paou.app_uuid,paou.type_of").
			Where("paou.user_uuid = ?", uuid).
			Where("paou.app_uuid in (?)", appUUIDList).
			Find(&appOfUserList)
		// 使用map优化查找，将O(n^2)优化为O(n)
		appOfUserMap := make(map[string]string)
		for _, appOfUser := range appOfUserList {
			appOfUserMap[appOfUser.AppUuid] = appOfUser.TypeOf
		}
		for _, app := range list {
			if typeOf, exists := appOfUserMap[app.Uuid]; exists {
				app.RelationShip = typeOf
			}
		}
	}
	return pm, nil
}

func (repo *appRepo) List(r ListRequest, uuid string, isAdmin bool) (list []*InfoResponse, err error) {
	var query *gorm.DB
	//管理员: 查询所有app
	if isAdmin {
		query = repo.db.Table("project_app pa")
	} else {
		//普通用户: 只查询当前用户管理和参与的app
		query = repo.db.Table("project_app_of_user paou").
			Select("pa.*,paou.type_of").
			Joins("left join project_app pa on paou.app_uuid = pa.uuid and paou.user_uuid = ?", uuid).
			Joins("left join project_info pi on pa.project_uuid = pi.uuid").
			Where("pa.uuid is not null")
	}
	//名称模糊查询
	if r.Name != "" {
		query = query.Where("pa.name like ?", util.Like(r.Name))
	}
	//删除标记
	query = query.Where("pa.deleted = false")

	if err := query.Order("gmt_create desc,name asc").Find(&list).Error; err != nil {
		return nil, err
	}
	//
	if isAdmin {
		for i := range list {
			list[i].RelationShip = common.Principal
		}
	}
	return list, nil
}

func (repo *appRepo) AddAppOfUser(ref []models.ProjectAppOfUser) error {
	db := repo.db.Create(ref)
	if err := db.Error; err != nil {
		return err
	}
	return nil
}

func (repo *appRepo) DeleteRelationShip(ids []int64) error {
	return repo.db.Where("id in (?)", ids).Delete(models.ProjectAppOfUser{}).Error
}

func (repo *appRepo) GetByUUID(uuid string) (*models.ProjectApp, error) {
	var result models.ProjectApp
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *appRepo) GetByIdentity(id string) (*models.ProjectApp, error) {
	var result models.ProjectApp
	if err := repo.db.Where("identity = ?", id).First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *appRepo) GetAppRelationShipWithName(uuid string) (list []models.ProjectAppOfUser, err error) {
	if err = repo.db.Model(models.ProjectAppOfUser{}).
		//Select("project_app_of_user.*,su.name name,su.value uuid").
		//Joins(fmt.Sprintf("left join `%s`.users su on su.value = project_app_of_user.user_uuid", config.Items().Mysql.DBName)).
		Where("app_uuid = ?", uuid).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *appRepo) GetProjectRelationShipList(uuid string) (list []models.ProjectAppOfUser, err error) {
	if err = repo.db.Model(models.ProjectAppOfUser{}).Where("app_uuid = ?", uuid).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *appRepo) GetAppRelationShipByAppUUIDAndEmail(uuid, email string) (*models.ProjectAppOfUser, error) {
	var ret models.ProjectAppOfUser
	if err := repo.db.Model(models.ProjectAppOfUser{}).
		Where("app_uuid = ? and user_uuid = ?", uuid, email).First(&ret).Error; err != nil {
		return nil, err
	}
	return &ret, nil
}

// GetAppGatewayInfo get app gateway info
func (repo *appRepo) GetAppGatewayInfo(gateway, env string) (*models.ProjectAppGateway, error) {
	var result models.ProjectAppGateway
	if err := repo.db.Where("gateway = ? and env = ?", gateway, env).
		First(&result).Error; err != nil {
		return nil, err
	}
	return &result, nil
}
