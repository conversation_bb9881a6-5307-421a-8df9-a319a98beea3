package app

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/project/app")
	{
		cAPI.Use(middleware.AuthIsAdmin(), middleware.AuthUToken())
		cAPI.POST("/info", Info)
		cAPI.GET("/info/:uuid", InfoByLoggerUUID)
		cAPI.POST("/page", Page)
		cAPI.POST("/list", List)
		cAPI.GET("/templates", Templates)
		cAPI.GET("/templates/refresh", TemplatesRefresh)
		cAPI.POST("/parameters", Parameters)
		cAPI.POST("/branches", Branches)
		cAPI.POST("/branchesOrTags", BranchesOrTags)
		cAPI.POST("/add", Add)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
		cAPI.POST("/import", Import)
		cAPI.POST("/request", Request)

		// 应用配置
		configAPI := cAPI.Group("/config")
		{
			configAPI.POST("/history", History)   // 配置历史
			configAPI.POST("/version", Version)   // 版本详情
			configAPI.POST("/approval", Approval) // 审批配置
			configAPI.POST("/select", Select)     // 下拉选项
			configAPI.POST("/review", Review)     // 催审配置
			configAPI.POST("/entry", Entry)       // 版本详情
			configAPI.POST("/edit", Edit)         // 编辑版本
		}
	}
}
