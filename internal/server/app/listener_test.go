package app

import (
	"bytes"
	"fmt"
	"pipeline/pkg/common/request"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/models"
	"strings"
	"testing"
	"text/template"
)

func TestProjectAppCreateBeforeEventHandler(t *testing.T) {
	tests := []struct {
		name               string
		params             map[string]interface{}
		expectDevEndpoint  string
		expectTestEndpoint string
		expectProdEndpoint string
		expectDevDomain    string
		expectTestDomain   string
		expectProdDomain   string
		expectDevGateway   string
		expectTestGateway  string
		expectProdGateway  string
	}{
		{
			name: "基础参数测试",
			params: map[string]interface{}{
				"Name":        "test-app",
				"Description": "test description",
			},
		},
		{
			name: "网关参数测试",
			params: map[string]interface{}{
				"Name":              "test-app",
				"Description":       "test description",
				"Export":            true,
				"DevDomainGateway":  "makeblock.com/makeblock-gateway",
				"TestDomainGateway": "makeblock.com/makeblock-gateway",
				"ProdDomainGateway": "makeblock.com/makeblock-gateway",
			},
			expectDevDomain:    "makeblock.com",
			expectTestDomain:   "makeblock.com",
			expectProdDomain:   "makeblock.com",
			expectDevEndpoint:  "test-app-api-dev.makeblock.com",
			expectTestEndpoint: "test-app-api-test.makeblock.com",
			expectProdEndpoint: "test-app-api.makeblock.com",
			expectDevGateway:   "makeblock-gateway",
			expectTestGateway:  "makeblock-gateway",
			expectProdGateway:  "makeblock-gateway",
		},
		{
			name: "网关参数格式错误",
			params: map[string]interface{}{
				"Name":              "test-app-api",
				"Description":       "test description",
				"Export":            true,
				"DevDomainGateway":  "xtool.com/xtool-gateway",
				"TestDomainGateway": "xtool.com/xtool-gateway",
				"ProdDomainGateway": "xtool.com/xtool-gateway",
			},
			expectDevDomain:    "xtool.com",
			expectTestDomain:   "xtool.com",
			expectProdDomain:   "xtool.com",
			expectDevEndpoint:  "test-app-api-dev.xtool.com",
			expectTestEndpoint: "test-app-api-test.xtool.com",
			expectProdEndpoint: "test-app-api.xtool.com",
			expectDevGateway:   "xtool-gateway",
			expectTestGateway:  "xtool-gateway",
			expectProdGateway:  "xtool-gateway",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &events.ProjectAppCreateBeforeEvent{
				App: &models.ProjectApp{
					Identity:    tt.params["Name"].(string),
					Description: tt.params["Description"].(string),
				},
				Request: &request.AppAddRequest{
					Params: tt.params,
				},
			}

			err := ProjectAppCreateBeforeEventHandler(nil, event)
			if err != nil {
				t.Errorf("handler error: %v", err)
				return
			}

			// 验证网关参数处理
			if tt.params["Export"] == "true" {
				for _, env := range []string{"Dev", "Test", "Prod"} {

					domain := event.Request.Params[fmt.Sprintf("%sDomain", env)]
					endpoint := event.Request.Params[fmt.Sprintf("%sEndpoint", env)]
					gateway := event.Request.Params[fmt.Sprintf("%sGateway", env)]

					var expectedDomain, expectedEndpoint, expectedGateway string
					switch env {
					case "Dev":
						expectedDomain = tt.expectDevDomain
						expectedEndpoint = tt.expectDevEndpoint
						expectedGateway = tt.expectDevGateway
					case "Test":
						expectedDomain = tt.expectTestDomain
						expectedEndpoint = tt.expectTestEndpoint
						expectedGateway = tt.expectTestGateway
					case "Prod":
						expectedDomain = tt.expectProdDomain
						expectedEndpoint = tt.expectProdEndpoint
						expectedGateway = tt.expectProdGateway
					}

					if expectedDomain != "" && domain != expectedDomain {
						t.Errorf("%s domain mismatch: got %v, want %v", env, domain, expectedDomain)
						return
					}

					if expectedEndpoint != "" && endpoint != expectedEndpoint {
						t.Errorf("%s endpoint mismatch: got %v, want %v", env, endpoint, expectedEndpoint)
						return
					}

					if expectedGateway != "" && gateway != expectedGateway {
						t.Errorf("%s gateway mismatch: got %v, want %v", env, gateway, expectedGateway)
						return
					}
				}
			}
		})
	}
}

func TestProjectAppCreatedAfterEventHandler(t *testing.T) {
	t.Skip()
	tests := []struct {
		name     string
		template string
		params   map[string]interface{}
	}{
		{
			name:     "非http应用不处理",
			template: "java-springboot",
			params:   map[string]interface{}{},
		},
		{
			name:     "http应用但无Export参数不处理",
			template: "http-go",
			params:   map[string]interface{}{},
		},
		{
			name:     "http应用但Export=false不处理",
			template: "http-go",
			params: map[string]interface{}{
				"Export": "false",
			},
		},
		{
			name:     "http应用且Export=true但无endpoint不处理",
			template: "http-go",
			params: map[string]interface{}{
				"Export": "true",
			},
		},
		{
			name:     "http应用且Export=true且有endpoint正常处理",
			template: "http-go",
			params: map[string]interface{}{
				"Export":       "true",
				"DevEndpoint":  "test-app-api-dev.makeblock.com",
				"TestEndpoint": "test-app-api-test.makeblock.com",
				"ProdEndpoint": "test-app-api.makeblock.com",
				"DevGateway":   "makeblock-gateway",
				"TestGateway":  "makeblock-gateway",
				"ProdGateway":  "makeblock-gateway",
				"DevDomain":    "makeblock.com",
				"TestDomain":   "makeblock.com",
				"ProdDomain":   "makeblock.com",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &events.ProjectAppCreatedAfterEvent{
				Request: &request.AppAddRequest{
					Template: tt.template,
					Params:   tt.params,
				},
			}
			if err := ProjectAppCreatedAfterEventHandler(nil, event); err != nil {
				t.Errorf("handler error: %v", err)
			}
		})
	}
}

func TestRender(t *testing.T) {
	content := `{{if .Export}}
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{.APP_ID}}
spec:
  host: {{.APP_ID}}
  trafficPolicy:
    loadBalancer:
      simple: RANDOM
  subsets:
  - name: v1
    labels:
      version: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{.APP_ID}}
spec:
  gateways:
  - gateway/{{.DevGateway}}
  hosts:
  - "{{.APP_ID}}-api-dev.{{.DevDomain}}"
  http:
  - route:
    - destination:
        host: {{.APP_ID}}
        port:
          number: 8080
        subset: v1
{{end}}`

	tests := []struct {
		name     string
		params   map[string]any
		expected string
	}{
		{
			name: "正常导出网关配置",
			params: map[string]any{
				"Export":     true,
				"APP_ID":     "test-service",
				"DevGateway": "makeblock-gateway",
				"DevDomain":  "makeblock.com",
			},
			expected: `
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: test-service
spec:
  host: test-service
  trafficPolicy:
    loadBalancer:
      simple: RANDOM
  subsets:
  - name: v1
    labels:
      version: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: test-service
spec:
  gateways:
  - gateway/makeblock-gateway
  hosts:
  - "test-service-api-dev.makeblock.com"
  http:
  - route:
    - destination:
        host: test-service
        port:
          number: 8080
        subset: v1
`,
		},
		{
			name: "不导出网关配置",
			params: map[string]any{
				"Export": false,
				"APP_ID": "test-service",
			},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpl, err := template.New("test").Parse(content)
			if err != nil {
				t.Fatalf("解析模板失败: %v", err)
			}

			var buf bytes.Buffer
			err = tmpl.Execute(&buf, tt.params)
			if err != nil {
				t.Fatalf("渲染模板失败: %v", err)
			}

			// 去除首尾空白字符进行比较
			actual := strings.TrimSpace(buf.String())
			expected := strings.TrimSpace(tt.expected)
			if actual != expected {
				t.Errorf("渲染结果不匹配\n期望:\n%s\n实际:\n%s", expected, actual)
			}
		})
	}
}
