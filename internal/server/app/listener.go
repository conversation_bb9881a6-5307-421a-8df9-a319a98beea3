package app

import (
	"context"
	"fmt"
	"pipeline/config"
	"pipeline/internal/server/ai/review"
	"pipeline/internal/server/app/plugins"
	"pipeline/internal/server/project"
	"pipeline/pkg/cloud"
	"pipeline/pkg/eventbus"
	"pipeline/pkg/eventbus/events"
	"pipeline/pkg/llm"
	"pipeline/pkg/models"
	"pipeline/pkg/notify"
	"pipeline/pkg/notify/types"
	"pipeline/pkg/sentry"
	"pipeline/pkg/util"
	"strings"

	"git.makeblock.com/makeblock-go/redis"

	"git.makeblock.com/makeblock-go/log"
	"git.makeblock.com/makeblock-go/mysql/v2"
	"github.com/go-resty/resty/v2"
)

// 注册到事件总线
func init() {
	eventbus.Register(ConfigPluginNeedApprovalEventHandler)
	eventbus.Register(ConfigPluginApprovalDoneEventHandler)
	eventbus.Register(ProjectAppMemberUpdateEventHandler)
	eventbus.Register(ProjectAppCreateBeforeEventHandler)
	eventbus.Register(ProjectAppCreatedAfterEventHandler)
	eventbus.Register(HttpAppCreatedResolveEventHandler)
	eventbus.Register(StaticWebAppCreatedResolveEventHandler)
	eventbus.Register(AutoAddUserToProjectEventHandler)
	eventbus.Register(TryReviewKubernetesPluginConfigEventHandler)
}

// ConfigPluginNeedApprovalEventHandler 待审批事件处理
func ConfigPluginNeedApprovalEventHandler(ctx context.Context, event *events.AppConfigPluginNeedApprovalEvent) error {
	// 查询应用名称
	appInfo, err := NewAppRepo(mysql.GetDB()).GetByUUID(event.AppUUID)
	if err != nil {
		return err
	}
	// 发送通知到群
	data := types.NotifyData{
		Type:         types.FeishuGroup,
		Receivers:    []string{config.Items().AppConfigApproval.ChatID},
		TemplateType: types.AppConfigRequestApprove,
		DataSource: map[string]any{
			"app":    appInfo.Name,
			"user":   event.User,
			"plugin": event.ConfigPlugin,
			"time":   util.NowDateTime(),
			"color":  models.ColorMap[models.ApproveStatePending],
			"url": config.Items().FrontEndEndpoint +
				fmt.Sprintf(config.Items().AppConfigApproval.InfoBaseURL,
					appInfo.UUID, event.ConfigPlugin),
		},
	}
	// 如果是k8s配置尝试进行自动审批
	if event.ConfigPlugin == string(plugins.PluginTypeKubernetes) &&
		config.Items().AppConfigApproval.AutoApproval {
		eventbus.PublishAsync(ctx, &events.TryReviewKubernetesPluginConfigEvent{
			App: appInfo, // NotifyData: &data,
		})
	}
	// 发送通知
	return notify.SendNotification(ctx, data)
}

// ConfigPluginApprovalDoneEventHandler 审批完成事件处理
func ConfigPluginApprovalDoneEventHandler(ctx context.Context, event *events.AppConfigPluginApprovalDoneEvent) error {
	// 获取appid
	configPluginInfo, err := plugins.NewConfigRepo(mysql.GetDB()).
		GetConfigPluginInfo(event.ConfigUUID)
	if err != nil {
		return err
	}
	// 查询应用名称
	appInfo, err := NewAppRepo(mysql.GetDB()).
		GetByUUID(configPluginInfo.AppUUID)
	if err != nil {
		return err
	}
	color := models.ColorMap[event.State]
	// 发送通知给个人
	data := types.NotifyData{
		Type:         types.FeishuPerson,
		Receivers:    []string{event.Receiver},
		TemplateType: types.AppConfigApproveDone,
		DataSource: map[string]any{
			"color":  color,
			"app":    appInfo.Name,
			"user":   event.Receiver,
			"plugin": configPluginInfo.Key,
			"config": event.Name, // 审批的配置项
			"state":  event.State,
			"reason": event.Reason,
			"time":   util.NowDateTime(),
			"url": config.Items().FrontEndEndpoint +
				fmt.Sprintf(config.Items().AppConfigApproval.InfoBaseURL,
					appInfo.UUID, configPluginInfo.Key),
		},
	}
	return notify.SendNotification(ctx, data)
}

// ProjectAppMemberUpdateEventHandler 应用关联用户关系变更事件处理器
func ProjectAppMemberUpdateEventHandler(ctx context.Context, event *events.ProjectAppMemberUpdateEvent) error {
	// 查询当前应用的用户
	engine := mysql.GetDB()
	list, err := NewAppRepo(engine).GetProjectRelationShipList(event.App.UUID)
	if err != nil {
		return err
	}
	// 推送给监控平台
	type EventContent struct {
		Name    string                    `json:"name"`
		AppId   string                    `json:"appId"`
		AppUUID string                    `json:"appUuid"`
		Members []models.ProjectAppOfUser `json:"members"`
	}
	body := EventContent{
		Name:    event.App.Name,
		AppId:   event.App.Identity,
		AppUUID: event.App.UUID,
		Members: list,
	}
	response, err := resty.New().R().SetBody(body).Post(config.Items().Subscribes.Monitor)
	if err != nil {
		return err
	}
	if !response.IsSuccess() {
		return fmt.Errorf("%v", response)
	}
	return nil
}

// ProjectAppCreateBeforeEventHandler 应用创建前事件处理器
func ProjectAppCreateBeforeEventHandler(_ context.Context, event *events.ProjectAppCreateBeforeEvent) error {
	// 必要参数
	event.Request.Params["Name"] = event.App.Identity
	event.Request.Params["Description"] = event.App.Description
	// 是否需要适配网关渲染参数
	if v, ok := event.Request.Params["Export"]; ok && v == true {
		// {env}-gateway : gateway/{gateway}
		for _, env := range []string{"Dev", "Test", "Prod"} {
			// when app id last word is '-api', we don't need to add '-api' to endpoint
			dnsRecord := event.App.Identity
			if !strings.HasSuffix(dnsRecord, "-api") {
				dnsRecord = fmt.Sprintf("%s-api", dnsRecord)
			}
			if env != "Prod" {
				dnsRecord = strings.ToLower(fmt.Sprintf("%s-%s", dnsRecord, env))
			}
			// makeblock.com/makeblock-gateway 通过'/'进行分隔域名和网关
			key := fmt.Sprintf("%sDomainGateway", env)
			// 类型断言确保 event.Request.Params[key] 是 string 类型
			value, ok := event.Request.Params[key].(string)
			if !ok || !strings.Contains(value, "/") {
				log.ErrorE("domain format error",
					fmt.Errorf("domain %s format error, must contain '/'", value))
				continue
			}
			// 通过'/'进行分隔域名和网关
			parts := strings.Split(value, "/")
			if len(parts) != 2 {
				log.ErrorE("domain format error",
					fmt.Errorf("domain %s format error, must contain exactly one '/'", value))
				continue
			}
			domain, gateway := parts[0], parts[1]
			// for auto create dns record
			event.Request.Params[fmt.Sprintf("%sEndpoint", env)] = fmt.Sprintf("%s.%s", dnsRecord, domain)
			// for render gateway template
			event.Request.Params[fmt.Sprintf("%sDomain", env)] = domain
			event.Request.Params[fmt.Sprintf("%sGateway", env)] = gateway
		}
	}

	// 创建 sentry 项目
	if v, ok := event.Request.Params["Sentry"]; ok && v == true {
		projectID, dsn, err := sentry.SetupSentryForApp(
			event.App.Name,
			event.App.Identity,
			event.Request.Template,
		)
		if err != nil {
			log.Error("sentry setup failed", log.Any("appUUID", event.App.UUID), log.Any("err", err))
		}
		// 设置项目ID和DSN
		event.App.SentryProjectId = &projectID
		event.Request.Params["SentryDSN"] = dsn
		log.Info("sentry project created successfully", log.Any("appUUID", event.App.UUID), log.Any("projectID", projectID))
	}

	return nil
}

// ProjectAppCreatedAfterEventHandler 应用创建后置事件处理器
func ProjectAppCreatedAfterEventHandler(ctx context.Context, event *events.ProjectAppCreatedAfterEvent) error {
	// 如果是http-开头的模板，则自动创建dns记录
	if strings.HasPrefix(event.Request.Template, "http-") {
		return eventbus.Publish(ctx, &events.HttpAppCreatedResolveEvent{
			ProjectAppCreatedAfterEvent: event,
		})
	}
	// 处理自动创建oss存储桶
	if event.Request.Params["CreateOSSBucket"] == true {
		return eventbus.Publish(ctx, &events.StaticWebAppCreatedResolveEvent{
			ProjectAppCreatedAfterEvent: event,
		})
	}
	return nil
}

// HttpAppCreatedResolveEventHandler http 应用自动创建dns记录（http应用、oss创建+域名解析等）后续希望将其通过cmdb工单的方式来实现
func HttpAppCreatedResolveEventHandler(ctx context.Context, event *events.HttpAppCreatedResolveEvent) error {
	// 判断参数是否包含Export=true且有完整的dns域名endpoint
	if v, ok := event.Request.Params["Export"]; ok && v == true {
		repo := NewAppRepo(mysql.GetDB())
		for _, env := range []string{"Dev", "Test", "Prod"} {
			// 获取key
			domainKey := fmt.Sprintf("%sDomain", env)
			gatewayKey := fmt.Sprintf("%sGateway", env)
			endpointKey := fmt.Sprintf("%sEndpoint", env)
			// 获取值
			domain, domainOk := event.Request.Params[domainKey].(string)
			gateway, gatewayOk := event.Request.Params[gatewayKey].(string)
			endpoint, endpointOk := event.Request.Params[endpointKey].(string)
			// 验证是否有效
			if !domainOk || !gatewayOk || !endpointOk {
				log.ErrorE("endpoint format error",
					fmt.Errorf("endpoint %s format error, must contain '.'", endpoint))
				continue
			}
			dnsRecord := strings.Replace(endpoint, "."+domain, "", 1)
			// 创建dns记录
			log.Printf("create dns record for %s, gateway is %s\n", dnsRecord, gateway)
			// load gateway specific ip address from database
			gatewayInfo, err := repo.GetAppGatewayInfo(gateway, env)
			if err != nil {
				log.ErrorE("get app gateway info error", err)
				continue
			}
			provider, err := cloud.NewProvider(gatewayInfo.CloudProvider)
			if err != nil {
				log.ErrorE("new cloud provider error", err)
				continue
			}
			if err = provider.AddDNSRecord(ctx, &cloud.DNSRecordOptions{
				Domain: domain,
				Name:   dnsRecord,
				Value:  gatewayInfo.Ip,
			}); err != nil {
				log.ErrorE("add dns record error", err)
			}
		}
	}
	return nil
}

func StaticWebAppCreatedResolveEventHandler(ctx context.Context, event *events.StaticWebAppCreatedResolveEvent) error {
	// 获取必要的参数信息来进行创建资源
	ossProvider := event.Request.Params["OSSProvider"].(string)
	resourceGroup := event.Request.Params["ResourceGroup"].(string)
	region := event.Request.Params[regionKey(ossProvider)].(string)
	provider, err := cloud.NewProvider(ossProvider)
	if err != nil {
		return err
	}
	// 小写字母，去掉-, 且结尾最好是web后缀
	bucketName := strings.ToLower(event.App.Identity)
	if !strings.HasSuffix(bucketName, "web") {
		bucketName = fmt.Sprintf("%sweb", bucketName)
	}
	// do create bucket
	response, err := provider.CreateBucket(ctx, &cloud.BucketOptions{
		Name:          bucketName,
		ACL:           "public-read", // only for ali cloud
		Region:        region,
		ResourceGroup: resourceGroup, // only for azure cloud
	})
	if err != nil {
		return err
	}
	log.Info("create bucket success", log.Any("bucket", bucketName))

	// 创建成功后，添加dns记录
	bindingDomain := event.Request.Params["BindingDomain"].(bool)
	if bindingDomain {
		domainWithProvider := event.Request.Params["DomainWithProvider"].(string)
		// 通过'/'切割出域名和域名的provider
		parts := strings.Split(domainWithProvider, "/")
		if len(parts) != 2 {
			log.Error("domain with provider format error", log.Any("domainWithProvider", domainWithProvider))
			return nil
		}
		domain, domainProviderName := parts[0], parts[1]
		var domainProvider cloud.CloudProvider
		domainProvider, err = cloud.NewProvider(domainProviderName)
		if err != nil {
			return err
		}
		if err = domainProvider.AddDNSRecord(ctx, &cloud.DNSRecordOptions{
			Domain:     domain,
			Name:       response.WebEndpoint,
			RecordType: cloud.DNSRecordTypeCNAME,
		}); err != nil {
			return err
		}
		log.Info("add dns record success", log.Any("domain", domain))
	}

	return nil
}

func regionKey(provider string) string {
	switch provider {
	case "aliyun":
		return "AliyunRegion"
	case "azure":
		return "AzureRegion"
	case "cloudflare":
		return "CloudflareRegion"
	default:
		return ""
	}
}

// AutoAddUserToProjectEventHandler 处理将用户添加到项目的事件
func AutoAddUserToProjectEventHandler(ctx context.Context, event *events.AutoAddUserToProjectEvent) error {
	// 过滤
	userIdListMap := make(map[string]struct{})
	for _, userId := range event.Principals {
		userIdListMap[userId] = struct{}{}
	}
	for _, userId := range event.Members {
		userIdListMap[userId] = struct{}{}
	}
	if len(userIdListMap) == 0 {
		return nil
	}
	// 转换为列表
	userIdList := make([]string, 0, len(userIdListMap))
	for userId := range userIdListMap {
		userIdList = append(userIdList, userId)
	}
	// 调用项目服务添加用户
	return project.NewInfoRepo(mysql.GetDB()).
		AddUserToProjectIfNotExists(userIdList,
			event.ProjectUUID, event.Operator)
}

// TryReviewKubernetesPluginConfigEventHandler 尝试自动审批k8s配置
func TryReviewKubernetesPluginConfigEventHandler(ctx context.Context, event *events.TryReviewKubernetesPluginConfigEvent) error {

	if event.App == nil {
		return fmt.Errorf("event app is nil, event: %+v", event)
	}

	engine := mysql.GetDB()

	// load pending kubernetes config entry
	pluginInfo, err := plugins.NewConfigRepo(engine).
		GetConfigPluginInfoByAppUUIDAndKey(event.App.UUID,
			string(plugins.PluginTypeKubernetes))
	if err != nil {
		return err
	}

	// parse config entry
	plugin := plugins.NewAppConfigPlugin(plugins.PluginTypeKubernetes, pluginInfo)
	configValue := plugin.Load()
	if configValue == nil {
		return fmt.Errorf("kubernetes config entry is empty")
	}

	// cast
	cfg, ok := configValue.([]models.AppConfigEntry)
	if !ok {
		return fmt.Errorf("kubernetes config entry is not a valid type, got %T", configValue)
	}

	// map uuid:entry
	entries := make(map[string]models.AppConfigEntry)
	for _, configEntry := range cfg {
		if configEntry.State == models.ApproveStatePending {
			entries[configEntry.UUID] = configEntry
		}
	}
	// load edit version
	uuidList := make([]string, 0)
	for _, entry := range entries {
		uuidList = append(uuidList, entry.UUID)
	}
	var editEntries []models.AppConfigEntryEdit
	if err = engine.Where("entry_uuid in (?)", uuidList).
		Find(&editEntries).Error; err != nil {
		return err
	}
	// edit version map
	editVersionMap := make(map[string]models.AppConfigEntryEdit)
	for _, editEntry := range editEntries {
		editVersionMap[editEntry.EntryUUID] = editEntry
	}
	// review
	pendingPassList := make([]models.AppConfigEntry, 0)
	selfHostedConfig := llm.LoadSelfHostedConfig()
	client := review.NewKubernetesManifestReviewer(llm.NewClient(selfHostedConfig))
	for _, entry := range entries {
		oldValue := entry.Content
		newValue := entry.Content
		if editEntry, exist := editVersionMap[entry.UUID]; exist {
			newValue = editEntry.Content
		}
		reviewResponse, reviewErr := client.Review(ctx, oldValue, newValue)
		if reviewErr != nil {
			log.ErrorE("auto review error", reviewErr)
			continue
		}
		if reviewResponse.State == models.ApproveStatePassed {
			pendingPassList = append(pendingPassList, entry)
		}
	}
	if len(pendingPassList) == 0 {
		return nil
	}
	// do approve
	util.RunWithRecover("auto review kubernetes config", func() {
		service := NewAppService(nil, engine, redis.GetClient())
		for _, entry := range pendingPassList {
			if err = service.Approval(plugins.ConfigApprovalRequest{
				UUID:  entry.UUID,
				State: models.ApproveStatePassed,
				Reason: fmt.Sprintf("本次改动不涉及资源变更, 自动审批通过 (powered by %s)",
					selfHostedConfig.Model),
			}, true, "auto"); err != nil {
				log.ErrorE("auto review error", err)
				continue
			}
		}
	})
	return nil
}
