package app

import (
	"pipeline/internal/server/app/plugins"
	"pipeline/pkg/common/model"
	"time"
)

type InfoResponse struct {
	Id           int64           `json:"id"`          //  主键
	Uuid         string          `json:"uuid"`        //  uuid
	Name         string          `json:"name"`        //  应用名称
	Identity     string          `json:"identity"`    //  标识
	ProjectUuid  string          `json:"projectUuid"` //  所属项目
	Project      ProjectResponse `json:"project" gorm:"-"`
	Source       string          `json:"source"`                                          //  代码源
	Category     string          `json:"category"`                                        //  应用分类
	Level        string          `json:"level"`                                           //  应用等级
	Description  string          `json:"description"`                                     //  描述
	RepoId       int64           `json:"repoId"`                                          //  代码仓库id(一般api交互时用)
	Creator      string          `json:"creator"`                                         //  创建人
	Modifier     string          `json:"modifier"`                                        //  修改人
	GmtCreate    model.Time      `json:"gmtCreate"`                                       //  创建时间
	GmtModified  time.Time       `json:"gmtModified"`                                     //  修改时间
	Deleted      string          `json:"deleted"`                                         //  逻辑删除
	RelationShip string          `json:"relationship" gorm:"column:type_of" db:"type_of"` //项目逻辑关系
	// 仓库地址
	HTTPURLToRepo string `gorm:"column:http_url_to_repo" db:"http_url_to_repo" json:"httpUrlToRepo"`
	//绑定关系
	Members    []RelationShipResponse `json:"members" gorm:"-"`
	Principals []RelationShipResponse `json:"principals" gorm:"-"`
	// 配置插件
	Configs         []plugins.ApplicationConfigPlugin `json:"configs" gorm:"-"`
	Admin           bool                              `json:"admin,omitempty" gorm:"-"`
	Reviewer        bool                              `json:"reviewer,omitempty" gorm:"-"`
	SentryProjectId *int64                            `json:"sentryProjectId"` // sentry项目id
	UptimeKumaId    *int64                            `json:"uptimeKumaId"`    // uptime kuma 监控项id
}

type RelationShipResponse struct {
	Name   string `json:"label"`
	TypeOf string `json:"typeOf"`
	Uuid   string `json:"value"`
}

type BranchesResponse struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type BranchesOrTagsEntry struct {
	Label string `json:"label"`
	Value string `json:"value"`
	Type  string `json:"type"`
	Key   string `json:"key"`
}

type BranchesOrTagsOptions struct {
	Label   string                `json:"label"`
	Options []BranchesOrTagsEntry `json:"options"`
}

type ProjectResponse struct {
	Name  string `json:"label"`
	Value string `json:"value"`
}
