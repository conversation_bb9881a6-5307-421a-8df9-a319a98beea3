package app

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"pipeline/internal/server/app/plugins"
	"pipeline/pkg/middleware"
	"strings"

	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
)

// @BasePath /api/v1/project/app

// Info godoc
// @Summary 获取应用信息
// @Description 根据指定的 UUID 获取应用的详细信息
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body request.InfoRequest true "请求体参数，包含应用 UUID"
// @Success 200 {object} response.APIModel{data=app.InfoResponse} "成功返回应用详细信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/info [post]
func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID, middleware.GetUserEmail(c),
		middleware.IsAdmin(c), middleware.IsAppReviewer(c))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// Add 新增应用
// @Summary 新增应用
// @Description 新增一个应用，支持选择模板并可选下载代码包
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body request.AppAddRequest true "请求体参数，包含应用信息"
// @Success 200 {object} response.APIModel "成功返回，若Download为true则返回文件流"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/add [post]
func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.AppAddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	zipPath, err := svc.Add(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	//下载代码
	if req.Download {
		c.Writer.Header().Add("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(*zipPath))) //设置下载文件的文件名
		c.Writer.Header().Add("Content-Type", "application/octet-stream")                                             // 设置返回的数据类型为二进制
		// 使用http包的ServeFile函数下载文件
		http.ServeFile(c.Writer, c.Request, *zipPath)
		// remove file
		defer func() {
			dir := strings.ReplaceAll(*zipPath, common.ZipCompressionExtension, "")
			_ = util.DeleteFolder(dir)
			_ = os.Remove(*zipPath)
		}()
	}
	response.OK(c, nil)
}

// Update
// @Summary 更新应用
// @Description 更新应用的基本信息和配置
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body UpdateRequest true "请求体参数，包含应用更新信息"
// @Success 200 {object} response.APIModel{data=app.InfoResponse} "成功返回更新后的应用信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/update [put]
func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Update(req, c.GetString(common.UserEmail))
	if err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, info)
}

// Page 分页查询应用
// @Summary 分页查询应用
// @Description 分页获取应用列表，支持多条件筛选
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body PageRequest true "请求体参数，包含分页及筛选条件"
// @Success 200 {object} response.APIModel{data=response.PageModel} "成功返回分页应用列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/page [post]
func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req, middleware.GetUserEmail(c), middleware.IsAdmin(c), middleware.IsAppReviewer(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

// List 获取应用列表
// @Summary 获取应用列表
// @Description 获取所有应用列表（不分页）
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body ListRequest true "请求体参数，包含筛选条件"
// @Success 200 {object} response.APIModel{data=response.PageModel} "成功返回应用列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/list [post]
func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req, middleware.GetUserEmail(c), middleware.IsAdmin(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, response.PageModel{
		List: list,
	})
}

// Delete 删除应用
// @Summary 删除应用
// @Description 根据UUID批量删除应用
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body request.DeleteRequest true "请求体参数，包含待删除应用UUID列表"
// @Success 200 {object} response.APIModel "成功删除"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/delete [delete]
func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// Templates 获取应用模板列表
// @Summary 获取应用模板列表
// @Description 获取所有可用的应用模板
// @Tags 应用管理
// @Accept json
// @Produce json
// @Success 200 {object} response.APIModel "成功返回模板列表"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/templates [get]
func Templates(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	pm, err := svc.Templates()
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

// TemplatesRefresh 刷新应用模板
// @Summary 刷新应用模板
// @Description 刷新远程应用模板到本地
// @Tags 应用管理
// @Accept json
// @Produce json
// @Success 200 {object} response.APIModel "刷新成功"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/templates/refresh [get]
func TemplatesRefresh(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.TemplatesRefresh()
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}

// Parameters 获取应用参数
// @Summary 获取应用参数
// @Description 获取指定应用的参数信息
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body request.ParametersRequest true "请求体参数，包含应用UUID等"
// @Success 200 {object} response.APIModel "成功返回参数信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/parameters [post]
func Parameters(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req request.ParametersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	ret, err := svc.Parameters(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, ret)
}

// Branches 获取应用分支列表
// @Summary 获取应用分支列表
// @Description 获取指定应用的分支列表
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body request.BranchesRequest true "请求体参数，包含应用UUID等"
// @Success 200 {object} response.APIModel "成功返回分支列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/branches [post]
func Branches(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req request.BranchesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	ret, err := svc.Branches(req)
	if err != nil {
		response.Err(c, response.BranchQueryError(err.Error()))
		return
	}
	response.OK(c, ret)
}

// BranchesOrTags 获取分支或标签
// @Summary 获取分支或标签
// @Description 获取指定应用的分支或标签列表
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body request.BranchesRequest true "请求体参数，包含应用UUID等"
// @Success 200 {object} response.APIModel "成功返回分支或标签列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/branchesOrTags [post]
func BranchesOrTags(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req request.BranchesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	ret, err := svc.BranchesOrTags(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, ret)
}

// Import 导入应用
// @Summary 导入应用
// @Description 导入已有应用信息
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body ImportRequest true "请求体参数，包含导入应用信息"
// @Success 200 {object} response.APIModel "导入成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/import [post]
func Import(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req ImportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.Import(req, middleware.GetUserName(c))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, nil)
}

// InfoByLoggerUUID 通过日志UUID获取应用信息
// @Summary 通过日志UUID获取应用信息
// @Description 根据日志UUID获取对应的应用详细信息
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param uuid path string true "日志UUID"
// @Success 200 {object} response.APIModel{data=app.InfoResponse} "成功返回应用详细信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/info/{uuid} [get]
func InfoByLoggerUUID(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	loggerUUID := c.Param("uuid")
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.GetByLoggerUUID(loggerUUID, middleware.GetUserEmail(c), middleware.IsAdmin(c))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

// History 获取配置历史
// @Summary 获取配置历史
// @Description 获取应用配置的历史版本列表
// @Tags 应用配置
// @Accept json
// @Produce json
// @Param data body plugins.ConfigHistoryRequest true "请求体参数，包含配置UUID等"
// @Success 200 {object} response.APIModel "成功返回历史版本列表"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/config/history [post]
func History(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req plugins.ConfigHistoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	page, err := svc.History(req)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, page)
}

// Version 获取配置版本详情
// @Summary 获取配置版本详情
// @Description 获取指定配置的版本详情
// @Tags 应用配置
// @Accept json
// @Produce json
// @Param data body plugins.ConfigVersionRequest true "请求体参数，包含配置UUID和版本号"
// @Success 200 {object} response.APIModel "成功返回版本详情"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/config/version [post]
func Version(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req plugins.ConfigVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	page, err := svc.Version(req)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, page)
}

// Approval 审批配置
// @Summary 审批配置
// @Description 审批应用的配置项
// @Tags 应用配置
// @Accept json
// @Produce json
// @Param data body plugins.ConfigApprovalRequest true "请求体参数，包含审批信息"
// @Success 200 {object} response.APIModel "审批成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/config/approval [post]
func Approval(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req plugins.ConfigApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	if err := svc.Approval(req,
		middleware.HasRole(c, middleware.CICDAdmin, middleware.CICDAppReviewer),
		middleware.GetUserEmail(c)); err != nil {
		response.ErrorResponse(c, err)
		return
	}
	response.OK(c, nil)
}

// Select 获取下拉选项
// @Summary 获取下拉选项
// @Description 获取应用配置的下拉选项数据
// @Tags 应用配置
// @Accept json
// @Produce json
// @Param data body plugins.ConfigSelectRequest true "请求体参数，包含应用UUID和配置Key"
// @Success 200 {object} response.APIModel "成功返回下拉选项"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/config/select [post]
func Select(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req plugins.ConfigSelectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	page, err := svc.Select(req)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, page)
}

// Review 催审配置
// @Summary 催审配置
// @Description 催促管理员审批配置项
// @Tags 应用配置
// @Accept json
// @Produce json
// @Param data body ReviewRequest true "请求体参数，包含配置UUID"
// @Success 200 {object} response.APIModel "催审成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/config/review [post]
func Review(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req ReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Review(req, middleware.GetUserEmail(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// Entry 获取配置项详情
// @Summary 获取配置项详情
// @Description 获取配置项的详细信息
// @Tags 应用配置
// @Accept json
// @Produce json
// @Param data body ReviewRequest true "请求体参数，包含配置UUID"
// @Success 200 {object} response.APIModel "成功返回配置项详情"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/config/entry [post]
func Entry(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req ReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	data, err := svc.Entry(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, data)
}

// Edit
// @Summary 获取配置项编辑版本
// @Description 获取配置项的编辑版本信息
// @Tags 应用配置
// @Accept json
// @Produce json
// @Param data body ReviewRequest true "请求体参数，包含配置UUID"
// @Success 200 {object} response.APIModel "成功返回编辑版本信息"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/config/edit [post]
func Edit(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req ReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	data, err := svc.Edit(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, data)
}

// Request
// @Summary 申请加入应用
// @Description 用户申请加入指定应用
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param data body RequestJoinRequest true "请求体参数，包含申请信息"
// @Success 200 {object} response.APIModel "申请成功"
// @Failure 400 {object} response.APIModel "请求参数错误"
// @Failure 500 {object} response.APIModel "服务器内部错误"
// @Router /api/v1/project/app/request [post]
func Request(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewAppService(ts, mysql.GetDB(), redis.GetClient())
	var req RequestJoinRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.RequestJoin(req, middleware.GetUserEmail(c))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}
