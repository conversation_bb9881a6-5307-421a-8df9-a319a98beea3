package app

import (
	"pipeline/internal/server/app/plugins"
	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
	Name string `json:"name"` //  应用名称
}

type PageRequest struct {
	request.PageRequest
	Name        string `json:"name"`        //  应用名称
	Identity    string `json:"identity"`    //  标识
	Project     string `json:"project"`     //  所属项目
	Source      string `json:"source"`      //  代码源
	Description string `json:"description"` //  描述
	TypeOf      string `json:"typeOf"`      //  类型
}

type UpdateRequest struct {
	request.UpdateRequest
	Name        string `json:"name"`        //  应用名称
	ProjectUuid string `json:"projectUuid"` //  所属项目
	Description string `json:"description"` //  描述
	Category    string `json:"category"`    //  应用分类
	Level       string `json:"level"`       //  应用等级
	// 关联用户
	Principals []string //负责人uuid
	Members    []string //参与人uuid
	// 应用配置
	Configs []plugins.ApplicationConfigPlugin `json:"configs"`
}

type ImportRequest struct {
	Name        string `json:"name"`        //  应用名称
	Identity    string `json:"identity"`    //  标识
	NamespaceId int    `json:"namespaceId"` //  所属项目
	ProjectUuid string `json:"projectUuid"` //  所属项目
	Source      string `json:"source"`      //  代码源
	RepoId      int    `json:"repoId"`
	Description string `json:"description"` //  描述
	Category    string `json:"category"`    //  应用分类
	Level       string `json:"level"`       //  应用等级
	//关联用户
	Principals []string //负责人uuid
	Members    []string //参与人uuid
}

type ReviewRequest struct {
	UUID string `json:"uuid" form:"uuid"`
}

type RequestJoinRequest struct {
	AppUUID string `json:"appUUID" form:"appUUID"`
	Role    string `json:"role" form:"role"`
	Reason  string `json:"reason" form:"reason"`
}

type ApprovalRequestRequest struct {
	UUID   string `json:"uuid" form:"uuid"`
	Status string `json:"status" form:"status"`
	Reason string `json:"reason" form:"reason"`
}
