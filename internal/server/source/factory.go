package source

import (
	"time"

	"pipeline/pkg/common/request"
)

type (
	// CreateRepoOptions 请求参数
	CreateRepoOptions struct {
		Name        string
		Desc        string
		NamespaceId int
	}
	AddProjectMemberOptions struct {
		ProjectId int
		UserIds   []string
	}
	CreateWebhookOptions struct {
		WebhookURL string
		ProjectId  int
	}
	UploadOptions struct {
		ProjectId     int
		Branch        string
		Path          string
		DistBasePath  string
		CommitMessage string
	}
	BranchesOptions struct {
		Name      string
		ProjectId int
	}
	// CreateRepoResponse 响应
	CreateRepoResponse struct {
		ProjectId     int
		SSHURLToRepo  string
		HTTPURLToRepo string
	}
	AddProjectMemberResponse struct {
	}
	CreateWebhookResponse struct {
		ID int `json:"id"`
	}
	UploadResponse struct {
		CommitId string
	}
	BranchesResponse struct {
		List []string
	}
	Group struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}
	NamespacesResponse struct {
		list []Group
	}
	// ProjectsOptions 查询仓库
	ProjectsOptions struct {
		Name      string
		ProjectId int
	}
	ProjectsResponse struct {
		ProjectId     int `json:"projectId"`
		Name          string
		FullName      string `json:"fullName"`
		SSHURLToRepo  string
		HTTPURLToRepo string
	}
	// InfoRepoOptions 获取仓库详细详细
	InfoRepoOptions struct {
		Name      string
		ProjectId int
	}
	InfoRepoResponse struct {
		ProjectId     int `json:"projectId"`
		Name          string
		FullName      string `json:"fullName"`
		SSHURLToRepo  string
		HTTPURLToRepo string
	}
	// UsersOptions 获取用户数据
	UsersOptions struct {
		request.PageRequest
		CreatedAfter *time.Time
	}
	CodeSourceUserEntry struct {
		//info
		UserId   string
		Name     string
		Email    string
		Username string
	}
	UsersResponse struct {
		TotalItems   int
		TotalPages   int
		ItemsPerPage int
		CurrentPage  int
		NextPage     int
		PreviousPage int
		List         []CodeSourceUserEntry
	}
	// BranchOptions 获取分支数据
	BranchOptions struct {
		ProjectId  int `json:"projectId"`
		BranchName string
	}

	BranchResponse struct {
		CommitId      string
		CommitMessage string
		CommitURL     string
	}

	// TagsOptions 获取tag数据
	TagsOptions struct {
		request.PageRequest
		Name      string `json:"name"`
		ProjectId int    `json:"projectId"`
	}
	TagInfo struct {
		Name      string `json:"name"`
		CommitSHA string `json:"commitSHA"`
	}
	TagsResponse struct {
		List []TagInfo `json:"list"`
	}

	// TagOptions 获取标签数据
	TagOptions struct {
		ProjectId int    `json:"projectId"`
		TagName   string `json:"tagName"`
	}
	TagResponse struct {
		CommitId      string `json:"commitId"`
		CommitMessage string `json:"commitMessage"`
		Message       string `json:"message"`
		CommitURL     string `json:"commitURL"`
	}
)

type CodeSource interface {
	// GetRepository 获取仓库详细信息
	GetRepository(opt InfoRepoOptions) (*InfoRepoResponse, error)
	// CreateRepository 创建仓库
	CreateRepository(opt CreateRepoOptions) (*CreateRepoResponse, error)
	// ListRepositories 查询仓库列表
	ListRepositories(opt ProjectsOptions) ([]ProjectsResponse, error)
	// CreateWebhook 创建webhook
	CreateWebhook(opt CreateWebhookOptions) (*CreateWebhookResponse, error)
	// AddRepositoryMember 新增项目成员
	AddRepositoryMember(opt AddProjectMemberOptions) (*AddProjectMemberResponse, error)
	// UploadFiles 上传代码
	UploadFiles(opt UploadOptions) (*UploadResponse, error)
	// GetBranch 获取仓库指定分支信息
	GetBranch(opt BranchOptions) (*BranchResponse, error)
	// ListBranches 获取仓库所有的分支
	ListBranches(opt BranchesOptions) (*BranchesResponse, error)
	// GetTag 获取标签详情信息
	GetTag(opt TagOptions) (*TagResponse, error)
	// ListTags 获取仓库所有的标签
	ListTags(opt TagsOptions) (*TagsResponse, error)
	// ListUsers 获取所有的用户数据
	ListUsers(opt UsersOptions) (*UsersResponse, error)
	// ListNamespaces 获取所有的命名空间
	ListNamespaces(name string) (*NamespacesResponse, error)
}

func NewCodeSourceService(s *ProjectAppCodeSource) CodeSource {
	switch s.Platform {
	case "gitlab":
		return NewGitlabCodeSourceService(s)
	case "gitea":
		return NewGiteaCodeSourceService(s)
	case "github":
		return NewGitHubCodeSourceService(s)
	}
	return nil
}
