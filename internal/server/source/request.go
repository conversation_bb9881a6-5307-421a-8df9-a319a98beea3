package source

import (
	"time"

	"pipeline/pkg/common/request"
)

type ListRequest struct {
	request.PageRequest
}

type PageRequest struct {
	request.PageRequest
	Name        string `json:"name"`         //  名称
	Platform    string `json:"platform"`     //  平台
	Endpoint    string `json:"endpoint"`     //  访问地址
	AccessToken string `json:"access_token"` //  访问秘钥
	Enable      bool   `json:"enable"`       //  是否启用
	Context     string `json:"context"`      //  配置
}

type AddRequest struct {
	Name        string `json:"name"`        //  名称
	Platform    string `json:"platform"`    //  平台
	Endpoint    string `json:"endpoint"`    //  访问地址
	AccessName  string `json:"accessName"`  //  访问用户名
	AccessToken string `json:"accessToken"` //  访问秘钥
	Enable      bool   `json:"enable"`      //  是否启用
	Context     string `json:"context"`     //  配置
}

type UpdateRequest struct {
	UUID        string `json:"uuid" form:"uuid"`
	Name        string `json:"name"`        //  名称
	Platform    string `json:"platform"`    //  平台
	Endpoint    string `json:"endpoint"`    //  访问地址
	AccessName  string `json:"accessName"`  //  访问用户名
	AccessToken string `json:"accessToken"` //  访问秘钥
	Enable      *bool  `json:"enable"`      //  是否启用
	Context     string `json:"context"`     //  配置
}

type NamespaceRequest struct {
	UUID string `json:"uuid" form:"uuid"`
	Name string `json:"name" form:"name"`
}

type ProjectsRequest struct {
	UUID string `json:"uuid" form:"uuid"`
	Name string `json:"name" form:"name"`
}

type ListCodeSourceUserRequest struct {
	Source string   `json:"sourceUUID"`
	Emails []string `json:"emails"`
}

type CodeSourceWebhookEvent struct {
	EventName string    `json:"event_name"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	Email     string    `json:"email"`
	UpdatedAt time.Time `json:"updated_at"`
	UserId    int       `json:"user_id"`
	Username  string    `json:"username"`
}
