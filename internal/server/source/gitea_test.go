package source

import (
	"testing"

	"pipeline/pkg/common/request"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockGiteaCodeSource 用于测试的mock结构
type MockGiteaCodeSource struct {
	mock.Mock
}

func (m *MockGiteaCodeSource) GetRepository(opt InfoRepoOptions) (*InfoRepoResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*InfoRepoResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) CreateRepository(opt CreateRepoOptions) (*CreateRepoResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*CreateRepoResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) ListRepositories(opt ProjectsOptions) ([]ProjectsResponse, error) {
	args := m.Called(opt)
	return args.Get(0).([]ProjectsResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) CreateWebhook(opt CreateWebhookOptions) (*CreateWebhookResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*CreateWebhookResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) AddRepositoryMember(opt AddProjectMemberOptions) (*AddProjectMemberResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*AddProjectMemberResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) UploadFiles(opt UploadOptions) (*UploadResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*UploadResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) GetBranch(opt BranchOptions) (*BranchResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*BranchResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) ListBranches(opt BranchesOptions) (*BranchesResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*BranchesResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) GetTag(opt TagOptions) (*TagResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*TagResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) ListTags(opt TagsOptions) (*TagsResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*TagsResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) ListUsers(opt UsersOptions) (*UsersResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*UsersResponse), args.Error(1)
}

func (m *MockGiteaCodeSource) ListNamespaces(name string) (*NamespacesResponse, error) {
	args := m.Called(name)
	return args.Get(0).(*NamespacesResponse), args.Error(1)
}

// 测试Gitea服务创建 - 只测试类型检查，避免SDK初始化
func TestNewGiteaCodeSourceService_Type(t *testing.T) {
	// 测试确保NewGiteaCodeSourceService函数存在且返回正确类型
	// 注意：这里不实际调用函数，只是验证函数签名
	var service func(*ProjectAppCodeSource) CodeSource = NewGiteaCodeSourceService
	assert.NotNil(t, service, "NewGiteaCodeSourceService function should exist")
}

// 测试GetRepository方法
func TestMockGiteaCodeSource_GetRepository(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &InfoRepoResponse{
		ProjectId:     123,
		Name:          "test-repo",
		FullName:      "user/test-repo",
		SSHURLToRepo:  "*********************:user/test-repo.git",
		HTTPURLToRepo: "https://gitea.example.com/user/test-repo.git",
	}

	mockService.On("GetRepository", InfoRepoOptions{ProjectId: 123}).Return(expectedResponse, nil)

	result, err := mockService.GetRepository(InfoRepoOptions{ProjectId: 123})

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试CreateRepository方法
func TestMockGiteaCodeSource_CreateRepository(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &CreateRepoResponse{
		ProjectId:     124,
		SSHURLToRepo:  "*********************:user/new-repo.git",
		HTTPURLToRepo: "https://gitea.example.com/user/new-repo.git",
	}

	opts := CreateRepoOptions{
		Name: "new-repo",
		Desc: "Test repository",
	}

	mockService.On("CreateRepository", opts).Return(expectedResponse, nil)

	result, err := mockService.CreateRepository(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试ListRepositories方法
func TestMockGiteaCodeSource_ListRepositories(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := []ProjectsResponse{
		{
			ProjectId:     125,
			Name:          "search-repo",
			FullName:      "user/search-repo",
			SSHURLToRepo:  "*********************:user/search-repo.git",
			HTTPURLToRepo: "https://gitea.example.com/user/search-repo.git",
		},
	}

	opts := ProjectsOptions{Name: "search"}

	mockService.On("ListRepositories", opts).Return(expectedResponse, nil)

	result, err := mockService.ListRepositories(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试CreateWebhook方法
func TestMockGiteaCodeSource_CreateWebhook(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &CreateWebhookResponse{ID: 1}

	opts := CreateWebhookOptions{
		ProjectId:  126,
		WebhookURL: "https://example.com/webhook",
	}

	mockService.On("CreateWebhook", opts).Return(expectedResponse, nil)

	result, err := mockService.CreateWebhook(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试AddRepositoryMember方法
func TestMockGiteaCodeSource_AddRepositoryMember(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &AddProjectMemberResponse{}

	opts := AddProjectMemberOptions{
		ProjectId: 127,
		UserIds:   []string{"newuser"},
	}

	mockService.On("AddRepositoryMember", opts).Return(expectedResponse, nil)

	result, err := mockService.AddRepositoryMember(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试ListBranches方法
func TestMockGiteaCodeSource_ListBranches(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &BranchesResponse{
		List: []string{"main", "feature-branch", "dev-branch"},
	}

	opts := BranchesOptions{ProjectId: 128}

	mockService.On("ListBranches", opts).Return(expectedResponse, nil)

	result, err := mockService.ListBranches(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Len(t, result.List, 3)
	assert.Contains(t, result.List, "main")
	mockService.AssertExpectations(t)
}

// 测试GetBranch方法
func TestMockGiteaCodeSource_GetBranch(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &BranchResponse{
		CommitId:      "abc123",
		CommitMessage: "Initial commit",
		CommitURL:     "https://gitea.example.com/testuser/repo/commit/abc123",
	}

	opts := BranchOptions{
		ProjectId:  129,
		BranchName: "main",
	}

	mockService.On("GetBranch", opts).Return(expectedResponse, nil)

	result, err := mockService.GetBranch(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试ListTags方法
func TestMockGiteaCodeSource_ListTags(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &TagsResponse{
		List: []TagInfo{
			{Name: "v1.0.0", CommitSHA: "def456"},
			{Name: "v1.1.0", CommitSHA: "ghi789"},
		},
	}

	opts := TagsOptions{
		ProjectId:   130,
		PageRequest: request.PageRequest{Page: 1, PageSize: 10},
	}

	mockService.On("ListTags", opts).Return(expectedResponse, nil)

	result, err := mockService.ListTags(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Len(t, result.List, 2)
	mockService.AssertExpectations(t)
}

// 测试ListUsers方法
func TestMockGiteaCodeSource_ListUsers(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &UsersResponse{
		CurrentPage: 1,
		List: []CodeSourceUserEntry{
			{UserId: "1", Username: "testuser1", Name: "Test User 1", Email: "<EMAIL>"},
			{UserId: "2", Username: "testuser2", Name: "Test User 2", Email: "<EMAIL>"},
		},
	}

	opts := UsersOptions{
		PageRequest: request.PageRequest{Page: 1, PageSize: 10},
	}

	mockService.On("ListUsers", opts).Return(expectedResponse, nil)

	result, err := mockService.ListUsers(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Len(t, result.List, 2)
	mockService.AssertExpectations(t)
}

// 测试ListNamespaces方法
func TestMockGiteaCodeSource_ListNamespaces(t *testing.T) {
	mockService := new(MockGiteaCodeSource)

	expectedResponse := &NamespacesResponse{
		list: []Group{
			{ID: 1, Name: "Organization 1"},
			{ID: 2, Name: "Organization 2"},
		},
	}

	mockService.On("ListNamespaces", "").Return(expectedResponse, nil)

	result, err := mockService.ListNamespaces("")

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}
