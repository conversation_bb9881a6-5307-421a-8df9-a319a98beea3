package source

import "time"

type InfoResponse struct {
	Id          int64     `json:"id"`           //  主键
	Uuid        string    `json:"uuid"`         //  uuid
	Name        string    `json:"name"`         //  名称
	Platform    string    `json:"platform"`     //  平台
	Endpoint    string    `json:"endpoint"`     //  访问地址
	AccessName  string    `json:"accessName"`   //  访问地址
	Enable      bool      `json:"enable"`       //  是否启用
	Context     string    `json:"context"`      //  配置
	Creator     string    `json:"creator"`      //  创建人
	Modifier    string    `json:"modifier"`     //  修改人
	GmtCreate   time.Time `json:"gmt_create"`   //  创建时间
	GmtModified time.Time `json:"gmt_modified"` //  修改时间
}

type InfoSourceResponse struct {
	Id          int64     `json:"id"`           //  主键
	Uuid        string    `json:"uuid"`         //  uuid
	Name        string    `json:"name"`         //  名称
	Platform    string    `json:"platform"`     //  平台
	Endpoint    string    `json:"endpoint"`     //  访问地址
	AccessName  string    `json:"accessName"`   //  访问地址
	AccessToken string    `json:"accessToken"`  //  访问秘钥
	Enable      bool      `json:"enable"`       //  是否启用
	Context     string    `json:"context"`      //  配置
	Creator     string    `json:"creator"`      //  创建人
	Modifier    string    `json:"modifier"`     //  修改人
	GmtCreate   time.Time `json:"gmt_create"`   //  创建时间
	GmtModified time.Time `json:"gmt_modified"` //  修改时间
}
