package source

import (
	"fmt"
	"strconv"
	"strings"

	"pipeline/pkg/common/response"
	"pipeline/pkg/util"

	"code.gitea.io/sdk/gitea"
	"git.makeblock.com/makeblock-go/log"
)

var _ CodeSource = &GiteaCodeSource{}

func NewGiteaCodeSourceService(s *ProjectAppCodeSource) CodeSource {
	// ensure endpoint has no trailing slash
	s.Endpoint = strings.TrimSuffix(s.Endpoint, "/")

	client, err := gitea.NewClient(s.Endpoint, gitea.SetToken(s.AccessToken))
	if err != nil {
		log.ErrorE("Failed to create gitea client", err)
	}

	return GiteaCodeSource{
		client: client,
	}
}

type GiteaCodeSource struct {
	client *gitea.Client
}

func (g GiteaCodeSource) GetRepository(opt InfoRepoOptions) (*InfoRepoResponse, error) {
	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}
	return &InfoRepoResponse{
		ProjectId:     int(repo.ID),
		Name:          repo.Name,
		FullName:      repo.FullName,
		SSHURLToRepo:  repo.SSHURL,
		HTTPURLToRepo: repo.CloneURL,
	}, nil
}

func (g GiteaCodeSource) CreateRepository(opt CreateRepoOptions) (*CreateRepoResponse, error) {
	createOpt := gitea.CreateRepoOption{
		Name:        opt.Name,
		Description: opt.Desc,
	}

	var repo *gitea.Repository
	var err error

	if opt.NamespaceId > 0 {
		// Create repository in organization
		var org *gitea.Organization
		org, _, err = g.client.GetOrg(strconv.Itoa(opt.NamespaceId))
		if err != nil {
			return nil, err
		}
		repo, _, err = g.client.CreateOrgRepo(org.UserName, createOpt)
	} else {
		// Create repository for current user
		repo, _, err = g.client.CreateRepo(createOpt)
	}

	if err != nil {
		// check if repository already exists
		if strings.Contains(err.Error(), "already exists") {
			return nil, response.RepositoryAlreadyExistError(opt.Name)
		}
		return nil, err
	}

	return &CreateRepoResponse{
		ProjectId:     int(repo.ID),
		SSHURLToRepo:  repo.SSHURL,
		HTTPURLToRepo: repo.CloneURL,
	}, nil
}

func (g GiteaCodeSource) AddRepositoryMember(opt AddProjectMemberOptions) (*AddProjectMemberResponse, error) {
	if len(opt.UserIds) == 0 {
		return nil, fmt.Errorf("userIds can not empty")
	}

	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	for _, userIdStr := range opt.UserIds {
		permission := gitea.AccessModeWrite
		addOpt := gitea.AddCollaboratorOption{
			Permission: &permission, // 类似 GitLab 的 Maintainer 权限
		}
		_, err := g.client.AddCollaborator(repo.Owner.UserName, repo.Name, userIdStr, addOpt)
		if err != nil {
			log.ErrorE("Failed to add collaborator", err)
			// 继续处理其他用户，不因为单个用户失败而中断
		}
	}

	return &AddProjectMemberResponse{}, nil
}

func (g GiteaCodeSource) CreateWebhook(opt CreateWebhookOptions) (*CreateWebhookResponse, error) {
	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	hookOpt := gitea.CreateHookOption{
		Type: gitea.HookTypeGitea,
		Config: map[string]string{
			"url":          opt.WebhookURL,
			"content_type": "json",
		},
		Events: []string{"push", "create"}, // 对应 push_events 和 tag_push_events
		Active: true,
	}

	hook, _, err := g.client.CreateRepoHook(repo.Owner.UserName, repo.Name, hookOpt)
	if err != nil {
		return nil, err
	}

	return &CreateWebhookResponse{
		ID: int(hook.ID),
	}, nil
}

func (g GiteaCodeSource) UploadFiles(opt UploadOptions) (*UploadResponse, error) {
	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	// 加载opt.Path下的所有文件
	files, err := util.ReadFiles(opt.Path)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		// 仓库文件路径
		filePath := file.Path
		if opt.DistBasePath != "" {
			filePath = fmt.Sprintf("%s/%s", opt.DistBasePath, file.Path)
		}

		fileContent := file.Content
		// 如果是图片类似的文件，需要base64编码
		if util.IsImageFile(filePath) {
			fileContent = util.Base64Encode(file.Content)
		}

		createFileOpt := gitea.CreateFileOptions{
			Content: fileContent,
			// TODO: 需要查看正确的字段名称
		}

		_, _, err = g.client.CreateFile(repo.Owner.UserName, repo.Name, filePath, createFileOpt)
		if err != nil {
			log.ErrorE("Failed to upload file", err)
			return nil, err
		}
	}

	// Gitea API 在创建文件时不直接返回 commit ID，我们需要获取最新的 commit
	commits, _, err := g.client.ListRepoCommits(repo.Owner.UserName, repo.Name, gitea.ListCommitOptions{
		ListOptions: gitea.ListOptions{Page: 1, PageSize: 1},
		SHA:         opt.Branch,
	})
	if err != nil || len(commits) == 0 {
		return &UploadResponse{CommitId: ""}, nil
	}

	return &UploadResponse{
		CommitId: commits[0].SHA,
	}, nil
}

func (g GiteaCodeSource) ListBranches(opt BranchesOptions) (*BranchesResponse, error) {
	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	listOpt := gitea.ListRepoBranchesOptions{}
	branches, _, err := g.client.ListRepoBranches(repo.Owner.UserName, repo.Name, listOpt)
	if err != nil {
		return nil, err
	}

	ret := &BranchesResponse{}
	for _, branch := range branches {
		if opt.Name == "" || strings.Contains(branch.Name, opt.Name) {
			ret.List = append(ret.List, branch.Name)
		}
	}

	return ret, nil
}

func (g GiteaCodeSource) ListNamespaces(name string) (*NamespacesResponse, error) {
	var orgs []*gitea.Organization

	if name != "" {
		// 搜索指定名称的组织
		searchOpt := gitea.SearchUsersOption{
			KeyWord: name,
		}
		searchResult, _, err := g.client.SearchUsers(searchOpt)
		if err != nil {
			return nil, err
		}

		for _, user := range searchResult {
			if user.UserName == name {
				org, _, err := g.client.GetOrg(user.UserName)
				if err == nil {
					orgs = append(orgs, org)
				}
			}
		}
	} else {
		// 列出当前用户的所有组织
		userOrgs, _, err := g.client.ListMyOrgs(gitea.ListOrgsOptions{})
		if err != nil {
			return nil, err
		}
		orgs = userOrgs
	}

	ret := &NamespacesResponse{}
	for _, org := range orgs {
		ret.list = append(ret.list, Group{
			ID:   int(org.ID),
			Name: org.FullName,
		})
	}

	return ret, nil
}

func (g GiteaCodeSource) ListRepositories(opt ProjectsOptions) ([]ProjectsResponse, error) {
	searchOpt := gitea.SearchRepoOptions{
		Keyword: opt.Name,
	}

	repos, _, err := g.client.SearchRepos(searchOpt)
	if err != nil {
		return nil, err
	}

	ret := make([]ProjectsResponse, 0)
	for _, repo := range repos {
		ret = append(ret, ProjectsResponse{
			ProjectId:     int(repo.ID),
			Name:          repo.Name,
			FullName:      repo.FullName,
			SSHURLToRepo:  repo.SSHURL,
			HTTPURLToRepo: repo.CloneURL,
		})
	}

	return ret, nil
}

func (g GiteaCodeSource) ListUsers(opt UsersOptions) (*UsersResponse, error) {
	listOpt := gitea.AdminListUsersOptions{
		ListOptions: gitea.ListOptions{
			Page:     opt.Page,
			PageSize: opt.PageSize,
		},
	}

	users, _, err := g.client.AdminListUsers(listOpt)
	if err != nil {
		return nil, err
	}

	ret := &UsersResponse{
		CurrentPage: opt.Page,
		List:        make([]CodeSourceUserEntry, 0),
	}

	for _, user := range users {
		ret.List = append(ret.List, CodeSourceUserEntry{
			UserId:   strconv.FormatInt(user.ID, 10),
			Name:     user.FullName,
			Email:    user.Email,
			Username: user.UserName,
		})
	}

	return ret, nil
}

func (g GiteaCodeSource) GetBranch(opt BranchOptions) (*BranchResponse, error) {
	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	branch, resp, err := g.client.GetRepoBranch(repo.Owner.UserName, repo.Name, opt.BranchName)
	if err != nil {
		if resp != nil && resp.StatusCode == 404 {
			return nil, response.BranchNotFoundError(opt.BranchName)
		}
		return nil, err
	}

	ret := &BranchResponse{
		CommitId:      branch.Commit.ID,
		CommitMessage: branch.Commit.Message,
		CommitURL:     branch.Commit.URL,
	}

	return ret, nil
}

func (g GiteaCodeSource) ListTags(opt TagsOptions) (*TagsResponse, error) {
	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	listOpt := gitea.ListRepoTagsOptions{
		ListOptions: gitea.ListOptions{
			Page:     opt.Page,
			PageSize: opt.PageSize,
		},
	}

	tags, _, err := g.client.ListRepoTags(repo.Owner.UserName, repo.Name, listOpt)
	if err != nil {
		return nil, err
	}

	ret := &TagsResponse{
		List: make([]TagInfo, 0),
	}

	for _, tag := range tags {
		if opt.Name == "" || strings.Contains(tag.Name, opt.Name) {
			ret.List = append(ret.List, TagInfo{
				Name:      tag.Name,
				CommitSHA: tag.Commit.SHA,
			})
		}
	}

	return ret, nil
}

func (g GiteaCodeSource) GetTag(opt TagOptions) (*TagResponse, error) {
	repo, _, err := g.client.GetRepoByID(int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	tag, resp, err := g.client.GetTag(repo.Owner.UserName, repo.Name, opt.TagName)
	if err != nil {
		if resp != nil && resp.StatusCode == 404 {
			return nil, response.TagNotFoundError(opt.TagName)
		}
		return nil, err
	}

	return &TagResponse{
		Message:       tag.Message,
		CommitId:      tag.Commit.SHA,
		CommitMessage: "", // Tag 中没有 commit message 字段
		CommitURL:     tag.Commit.URL,
	}, nil
}
