package source

import (
	"testing"

	"pipeline/pkg/common/request"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockGitHubCodeSource 用于测试的mock结构
type MockGitHubCodeSource struct {
	mock.Mock
}

func (m *MockGitHubCodeSource) GetRepository(opt InfoRepoOptions) (*InfoRepoResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*InfoRepoResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) CreateRepository(opt CreateRepoOptions) (*CreateRepoResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*CreateRepoResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) ListRepositories(opt ProjectsOptions) ([]ProjectsResponse, error) {
	args := m.Called(opt)
	return args.Get(0).([]ProjectsResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) CreateWebhook(opt CreateWebhookOptions) (*CreateWebhookResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*CreateWebhookResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) AddRepositoryMember(opt AddProjectMemberOptions) (*AddProjectMemberResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*AddProjectMemberResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) UploadFiles(opt UploadOptions) (*UploadResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*UploadResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) GetBranch(opt BranchOptions) (*BranchResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*BranchResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) ListBranches(opt BranchesOptions) (*BranchesResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*BranchesResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) GetTag(opt TagOptions) (*TagResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*TagResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) ListTags(opt TagsOptions) (*TagsResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*TagsResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) ListUsers(opt UsersOptions) (*UsersResponse, error) {
	args := m.Called(opt)
	return args.Get(0).(*UsersResponse), args.Error(1)
}

func (m *MockGitHubCodeSource) ListNamespaces(name string) (*NamespacesResponse, error) {
	args := m.Called(name)
	return args.Get(0).(*NamespacesResponse), args.Error(1)
}

// 测试GitHub服务创建 - 只测试类型检查，避免SDK初始化
func TestNewGitHubCodeSourceService_Type(t *testing.T) {
	// 测试确保NewGitHubCodeSourceService函数存在且返回正确类型
	// 注意：这里不实际调用函数，只是验证函数签名
	var service func(*ProjectAppCodeSource) CodeSource = NewGitHubCodeSourceService
	assert.NotNil(t, service, "NewGitHubCodeSourceService function should exist")
}

// 测试GitHub Enterprise支持
func TestGitHubCodeSource_Enterprise_Support(t *testing.T) {
	// 这个测试验证GitHub实现支持企业版配置
	config := &ProjectAppCodeSource{
		Platform:    "github",
		Endpoint:    "https://github.enterprise.com",
		AccessToken: "test-token",
	}

	// 只验证不会panic，不实际初始化
	assert.NotNil(t, config.Endpoint, "Enterprise endpoint should be supported")
	assert.Equal(t, "github", config.Platform)
}

// 测试GetRepository方法
func TestMockGitHubCodeSource_GetRepository(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &InfoRepoResponse{
		ProjectId:     123,
		Name:          "test-repo",
		FullName:      "user/test-repo",
		SSHURLToRepo:  "**************:user/test-repo.git",
		HTTPURLToRepo: "https://github.com/user/test-repo.git",
	}

	mockService.On("GetRepository", InfoRepoOptions{ProjectId: 123}).Return(expectedResponse, nil)

	result, err := mockService.GetRepository(InfoRepoOptions{ProjectId: 123})

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试CreateRepository方法
func TestMockGitHubCodeSource_CreateRepository(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &CreateRepoResponse{
		ProjectId:     124,
		SSHURLToRepo:  "**************:user/new-repo.git",
		HTTPURLToRepo: "https://github.com/user/new-repo.git",
	}

	opts := CreateRepoOptions{
		Name: "new-repo",
		Desc: "Test repository",
	}

	mockService.On("CreateRepository", opts).Return(expectedResponse, nil)

	result, err := mockService.CreateRepository(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试CreateRepository在组织中创建
func TestMockGitHubCodeSource_CreateRepository_InOrganization(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &CreateRepoResponse{
		ProjectId:     125,
		SSHURLToRepo:  "**************:myorg/org-repo.git",
		HTTPURLToRepo: "https://github.com/myorg/org-repo.git",
	}

	opts := CreateRepoOptions{
		Name:        "org-repo",
		Desc:        "Organization repository",
		NamespaceId: 123, // 组织ID
	}

	mockService.On("CreateRepository", opts).Return(expectedResponse, nil)

	result, err := mockService.CreateRepository(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试ListRepositories方法
func TestMockGitHubCodeSource_ListRepositories(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := []ProjectsResponse{
		{
			ProjectId:     125,
			Name:          "search-repo",
			FullName:      "user/search-repo",
			SSHURLToRepo:  "**************:user/search-repo.git",
			HTTPURLToRepo: "https://github.com/user/search-repo.git",
		},
	}

	opts := ProjectsOptions{Name: "search"}

	mockService.On("ListRepositories", opts).Return(expectedResponse, nil)

	result, err := mockService.ListRepositories(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试CreateWebhook方法
func TestMockGitHubCodeSource_CreateWebhook(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &CreateWebhookResponse{ID: 1}

	opts := CreateWebhookOptions{
		ProjectId:  126,
		WebhookURL: "https://example.com/webhook",
	}

	mockService.On("CreateWebhook", opts).Return(expectedResponse, nil)

	result, err := mockService.CreateWebhook(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试AddRepositoryMember方法
func TestMockGitHubCodeSource_AddRepositoryMember(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &AddProjectMemberResponse{}

	opts := AddProjectMemberOptions{
		ProjectId: 127,
		UserIds:   []string{"newuser"}, // GitHub使用用户名而不是ID
	}

	mockService.On("AddRepositoryMember", opts).Return(expectedResponse, nil)

	result, err := mockService.AddRepositoryMember(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试AddRepositoryMember边界条件
func TestMockGitHubCodeSource_AddRepositoryMember_EmptyUsers(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	opts := AddProjectMemberOptions{
		ProjectId: 127,
		UserIds:   []string{}, // 空用户列表
	}

	// Mock返回错误
	mockService.On("AddRepositoryMember", opts).Return((*AddProjectMemberResponse)(nil), assert.AnError)

	result, err := mockService.AddRepositoryMember(opts)

	assert.Error(t, err)
	assert.Nil(t, result)
	mockService.AssertExpectations(t)
}

// 测试ListBranches方法
func TestMockGitHubCodeSource_ListBranches(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &BranchesResponse{
		List: []string{"main", "feature-branch", "dev-branch"},
	}

	opts := BranchesOptions{ProjectId: 128}

	mockService.On("ListBranches", opts).Return(expectedResponse, nil)

	result, err := mockService.ListBranches(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Len(t, result.List, 3)
	assert.Contains(t, result.List, "main")
	mockService.AssertExpectations(t)
}

// 测试ListBranches搜索过滤
func TestMockGitHubCodeSource_ListBranches_WithSearch(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &BranchesResponse{
		List: []string{"feature-branch"},
	}

	opts := BranchesOptions{
		ProjectId: 128,
		Name:      "feature", // 搜索包含"feature"的分支
	}

	mockService.On("ListBranches", opts).Return(expectedResponse, nil)

	result, err := mockService.ListBranches(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Len(t, result.List, 1)
	assert.Contains(t, result.List, "feature-branch")
	mockService.AssertExpectations(t)
}

// 测试GetBranch方法
func TestMockGitHubCodeSource_GetBranch(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &BranchResponse{
		CommitId:      "abc123",
		CommitMessage: "Initial commit",
		CommitURL:     "https://github.com/testuser/repo/commit/abc123",
	}

	opts := BranchOptions{
		ProjectId:  129,
		BranchName: "main",
	}

	mockService.On("GetBranch", opts).Return(expectedResponse, nil)

	result, err := mockService.GetBranch(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试ListTags方法
func TestMockGitHubCodeSource_ListTags(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &TagsResponse{
		List: []TagInfo{
			{Name: "v1.0.0", CommitSHA: "def456"},
			{Name: "v1.1.0", CommitSHA: "ghi789"},
		},
	}

	opts := TagsOptions{
		ProjectId:   130,
		PageRequest: request.PageRequest{Page: 1, PageSize: 10},
	}

	mockService.On("ListTags", opts).Return(expectedResponse, nil)

	result, err := mockService.ListTags(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Len(t, result.List, 2)
	mockService.AssertExpectations(t)
}

// 测试GetTag方法
func TestMockGitHubCodeSource_GetTag(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &TagResponse{
		CommitId:      "def456",
		CommitMessage: "",
		Message:       "Release version 1.0.0",
		CommitURL:     "https://api.github.com/repos/testuser/repo/commits/def456",
	}

	opts := TagOptions{
		ProjectId: 131,
		TagName:   "v1.0.0",
	}

	mockService.On("GetTag", opts).Return(expectedResponse, nil)

	result, err := mockService.GetTag(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Equal(t, "Release version 1.0.0", result.Message)
	mockService.AssertExpectations(t)
}

// 测试ListUsers方法
func TestMockGitHubCodeSource_ListUsers(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &UsersResponse{
		TotalItems:  2,
		CurrentPage: 1,
		List: []CodeSourceUserEntry{
			{UserId: "1", Username: "testuser1", Name: "Test User 1", Email: "<EMAIL>"},
			{UserId: "2", Username: "testuser2", Name: "Test User 2", Email: "<EMAIL>"},
		},
	}

	opts := UsersOptions{
		PageRequest: request.PageRequest{Page: 1, PageSize: 10},
	}

	mockService.On("ListUsers", opts).Return(expectedResponse, nil)

	result, err := mockService.ListUsers(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Equal(t, 2, result.TotalItems)
	assert.Len(t, result.List, 2)
	mockService.AssertExpectations(t)
}

// 测试ListUsers带时间过滤
func TestMockGitHubCodeSource_ListUsers_WithDateFilter(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &UsersResponse{
		TotalItems:  1,
		CurrentPage: 1,
		List: []CodeSourceUserEntry{
			{UserId: "3", Username: "newuser", Name: "New User", Email: "<EMAIL>"},
		},
	}

	// 使用时间过滤
	opts := UsersOptions{
		PageRequest: request.PageRequest{Page: 1, PageSize: 10},
		// CreatedAfter: &someTime, // 在实际使用中可以设置
	}

	mockService.On("ListUsers", opts).Return(expectedResponse, nil)

	result, err := mockService.ListUsers(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Equal(t, 1, result.TotalItems)
	mockService.AssertExpectations(t)
}

// 测试ListNamespaces方法
func TestMockGitHubCodeSource_ListNamespaces(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &NamespacesResponse{
		list: []Group{
			{ID: 1, Name: "Organization 1"},
			{ID: 2, Name: "Organization 2"},
		},
	}

	mockService.On("ListNamespaces", "").Return(expectedResponse, nil)

	result, err := mockService.ListNamespaces("")

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	mockService.AssertExpectations(t)
}

// 测试ListNamespaces搜索特定组织
func TestMockGitHubCodeSource_ListNamespaces_WithSearch(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &NamespacesResponse{
		list: []Group{
			{ID: 1, Name: "MyOrg"},
		},
	}

	mockService.On("ListNamespaces", "MyOrg").Return(expectedResponse, nil)

	result, err := mockService.ListNamespaces("MyOrg")

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Len(t, result.list, 1)
	assert.Equal(t, "MyOrg", result.list[0].Name)
	mockService.AssertExpectations(t)
}

// 测试UploadFiles方法
func TestMockGitHubCodeSource_UploadFiles(t *testing.T) {
	mockService := new(MockGitHubCodeSource)

	expectedResponse := &UploadResponse{
		CommitId: "file123",
	}

	opts := UploadOptions{
		ProjectId:     132,
		Branch:        "main",
		Path:          "/test/path",
		DistBasePath:  "dist",
		CommitMessage: "Add test files",
	}

	mockService.On("UploadFiles", opts).Return(expectedResponse, nil)

	result, err := mockService.UploadFiles(opts)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, result)
	assert.Equal(t, "file123", result.CommitId)
	mockService.AssertExpectations(t)
}
