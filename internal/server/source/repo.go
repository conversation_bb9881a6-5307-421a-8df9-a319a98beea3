package source

import (
	"gorm.io/gorm"
	"pipeline/pkg/common"
	"pipeline/pkg/common/response"
	"pipeline/pkg/util"
)

type sourceRepo struct {
	db *gorm.DB
}

func NewSourceRepo(db *gorm.DB) *sourceRepo {
	return &sourceRepo{db: db}
}

func (repo *sourceRepo) Info(id int64) (*ProjectAppCodeSource, error) {
	var result ProjectAppCodeSource
	if err := repo.db.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (repo *sourceRepo) Add(m *ProjectAppCodeSource) (id int64, err error) {
	// encrypt
	m.AccessToken, err = common.DefaultEncrypt(m.AccessToken)
	if err != nil {
		return 0, err
	}
	db := repo.db.Create(m)
	if err = db.Error; err != nil {
		return -1, err
	}
	return m.ID, nil
}

func (repo *sourceRepo) Delete(ids []string) error {
	return repo.db.Where("uuid in (?)", ids).Delete(&ProjectAppCodeSource{}).Error
}

func (repo *sourceRepo) Update(m *ProjectAppCodeSource) (rowsAffected int64, err error) {
	db := repo.db.Model(ProjectAppCodeSource{}).Where("id = ?", m.ID).Updates(&m)
	return db.RowsAffected, db.Error
}

func (repo *sourceRepo) Page(r PageRequest) (pm response.PageModel, err error) {
	query := repo.db.Model(ProjectAppCodeSource{})
	if r.Name != "" {
		query.Where("name like ?", util.Like(r.Name))
	}
	if err = query.Count(&pm.Total).Error; err != nil {
		return pm, err
	}
	offset := (r.Page - 1) * r.PageSize
	limit := r.PageSize
	var list []*InfoResponse
	if err := query.Order("gmt_create desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return pm, err
	}
	pm.List = list
	pm.Page = r.Page
	pm.PageSize = r.PageSize
	return pm, nil
}

func (repo *sourceRepo) List(r ListRequest) (list []*ProjectAppCodeSource, err error) {
	if err := repo.db.Order("gmt_create desc").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *sourceRepo) GetByUUID(uuid string) (*ProjectAppCodeSource, error) {
	var result ProjectAppCodeSource
	if err := repo.db.Where("uuid = ?", uuid).First(&result).Error; err != nil {
		return nil, err
	}
	// decrypt
	accessToken, err := common.DefaultDecrypt(result.AccessToken)
	if err != nil {
		return nil, err
	}
	result.AccessToken = accessToken
	return &result, nil
}

// ListCodeSourceUser 查询代码源用户
func (repo *sourceRepo) ListCodeSourceUser(r ListCodeSourceUserRequest) (list []*CodeSourceUser, err error) {
	query := repo.db.Model(CodeSourceUser{})
	if r.Source != "" {
		query.Where("code_source_uuid = ?", r.Source)
	}
	if len(r.Emails) > 0 {
		query.Where("email in (?)", r.Emails)
	}
	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *sourceRepo) AddCodeSourceUser(r []CodeSourceUser) error {
	return repo.db.Save(r).Error
}
