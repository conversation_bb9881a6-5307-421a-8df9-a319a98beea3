package source

//const accessToken = "**************************"
//
//func TestName(t *testing.T) {
//	git, err := gitlab.NewClient(accessToken, gitlab.WithBaseURL("http://172.16.50.132:8088/api/v4"))
//	if err != nil {
//		log.Fatalf("Failed to create client: %v", err)
//	}
//	users, _, err := git.Groups.ListGroups(&gitlab.ListGroupsOptions{})
//	for _, user := range users {
//		fmt.Printf("%#v \n", user)
//	}
//}
//
//func TestCreateRepo(t *testing.T) {
//	git, err := gitlab.NewClient(accessToken, gitlab.WithBaseURL("http://172.16.50.132:8088/api/v4"))
//	if err != nil {
//		log.Fatalf("Failed to create client: %v", err)
//	}
//	p := &gitlab.CreateCommitOptions{
//		Branch:        gitlab.String("main"),
//		CommitMessage: gitlab.String("init commit"),
//		Actions: []*gitlab.CommitActionOptions{
//			{
//				Action:   gitlab.FileAction(gitlab.FileCreate),
//				FilePath: gitlab.String("a.txt"),
//				Content:  gitlab.String("aaaaaa"),
//			}, {
//				Action:   gitlab.FileAction(gitlab.FileCreate),
//				FilePath: gitlab.String("b.txt"),
//				Content:  gitlab.String("bbbbbb"),
//			},
//		},
//	}
//	project, _, err := git.Commits.CreateCommit(1, p)
//	if err != nil {
//		t.Error(err)
//	}
//	fmt.Printf("%v\n", project)
//}
//
//func TestListProjects(t *testing.T) {
//	git, err := gitlab.NewClient(accessToken, gitlab.WithBaseURL("http://172.16.50.132:8088/api/v4"))
//	if err != nil {
//		log.Fatalf("Failed to create client: %v", err)
//	}
//	users, _, err := git.Projects.ListProjects(&gitlab.ListProjectsOptions{
//		Search: gitlab.String("Apache"),
//	})
//	for _, user := range users {
//		marshal, err := json.Marshal(user)
//		if err != nil {
//			return
//		}
//		fmt.Printf("%#v \n", string(marshal))
//	}
//}
//
//func TestListUserList(t *testing.T) {
//	git, err := gitlab.NewClient(accessToken, gitlab.WithBaseURL("http://172.16.50.132:8088/api/v4"))
//	if err != nil {
//		log.Fatalf("Failed to create client: %v", err)
//	}
//	users, response, err := git.Users.ListUsers(&gitlab.ListUsersOptions{
//		ListOptions: gitlab.ListOptions{
//			Page:    1,
//			PerPage: 100, // gitlab max 100
//		},
//	})
//	t.Logf("response: %#v \n", response)
//	for _, user := range users {
//		t.Logf("%#v \n", user.Name)
//	}
//}
