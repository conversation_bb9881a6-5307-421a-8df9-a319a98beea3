package source

import (
	"github.com/gin-gonic/gin"
	"pipeline/pkg/middleware"
)

// RegisterRouter RegisterRouter
func RegisterRouter(router *gin.Engine) {
	cAPI := router.Group("/api/v1/project/source")
	{
		//外部触发无需鉴权
		//cAPI.GET("/sync", SyncCodeSourceUser)
		cAPI.POST("/webhook", CodeSourceWebhook) // https://pipeline.makeblock.com/api/v1/project/source/webhook (系统级别的webhook用于监听用户创建事件)
		//业务操作
		cAPI.Use(middleware.AuthUToken())
		cAPI.POST("/info", Info)
		cAPI.POST("/page", Page)
		cAPI.POST("/list", List)
		cAPI.POST("/add", Add)
		cAPI.PUT("/update", Update)
		cAPI.DELETE("/delete", Delete)
		cAPI.POST("/namespace", Namespace)
		cAPI.POST("/projects", Projects)
	}
}
