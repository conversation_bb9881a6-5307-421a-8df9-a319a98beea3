package source

import (
	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/redis"
	"git.makeblock.com/makeblock-go/utils/v2/errors"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"pipeline/pkg/common"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
)

func Info(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req request.InfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Info(req.UUID)
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	var ret InfoResponse
	err = copier.Copy(&ret, &info)
	if err != nil {
		errors.ResponseError(c, err)
	}
	response.OK(c, ret)
}

func Add(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req AddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Add(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Update(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	info, err := svc.Update(req, c.GetString(common.UserName))
	if err != nil {
		errors.ResponseError(c, err)
		return
	}
	response.OK(c, info)
}

func Page(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	var req PageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	pm, err := svc.Page(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, pm)
}

func List(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	var req ListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	list, err := svc.List(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, list)
}

func Delete(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	var req request.DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	err := svc.Delete(req.UUIDs, c.GetString(common.UserName))
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

func Namespace(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	var req NamespaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	ns, err := svc.Namespace(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, ns)
}

func Projects(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	var req ProjectsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	ns, err := svc.Projects(req)
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, ns)
}

// 同步代码源用户列表数据

func SyncCodeSourceUser(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	err := svc.SyncCodeSourceUser()
	if err != nil {
		response.Err(c, response.DBOperationError(err.Error()))
		return
	}
	response.OK(c, nil)
}

// 接收用户创建webhook事件

func CodeSourceWebhook(c *gin.Context) {
	ts := trace.NewTraceServiceFromGin(c)
	svc := NewSourceService(ts, mysql.GetDB(), redis.GetClient())
	var req CodeSourceWebhookEvent
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Err(c, response.RequestParamError(err.Error()))
		return
	}
	// 只处理用户创建事件
	if req.EventName == "user_create" {
		if err := svc.CreateCodeSourceUser(req); err != nil {
			response.Err(c, response.UnknownError("create code source user error"))
		}
		return
	}
	response.Err(c, response.RequestParamError("event not supported, skip"))
}
