package source

import (
	"git.makeblock.com/makeblock-go/mysql/v2"
	"gorm.io/gorm/logger"
	"pipeline/config"
	"pipeline/pkg/common"
	"testing"
)

func TestGitlabCodeSource_AddProjectMember(t *testing.T) {
	t.Skip()
	config.Load()
	// 缺省数据库
	cnf := mysql.NewConfig(
		config.Items().Mysql.User,
		config.Items().Mysql.Pwd,
		config.Items().Mysql.Host,
		config.Items().Mysql.Port,
		config.Items().Mysql.DBName,
		config.Items().Mysql.Charset,
		logger.Info)
	mysql.Register(cnf)
	var projectAppCodeSource ProjectAppCodeSource
	if err := mysql.GetDB().Model(&ProjectAppCodeSource{}).Where("id = ?", 3).
		First(&projectAppCodeSource).Error; err != nil {
		t.Errorf("Failed to get project app code source: %v", err)
		return
	}
	projectAppCodeSource.AccessToken, _ = common.DefaultDecrypt(projectAppCodeSource.AccessToken)
	codeSource := NewCodeSourceService(&projectAppCodeSource)
	_, err := codeSource.AddRepositoryMember(AddProjectMemberOptions{
		ProjectId: 963,
		UserIds:   []string{"59", "107", "204", "7", "2"},
	})
	if err != nil {
		t.Errorf("Failed to add project member: %v", err)
		return
	}
}
