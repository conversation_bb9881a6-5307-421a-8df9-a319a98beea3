package source

import (
	"time"

	"pipeline/pkg/common/model"
)

// ProjectAppCodeSource  project_app_code_source
type ProjectAppCodeSource struct {
	model.BaseModel
	Name        string `gorm:"column:name" db:"name" json:"name"`                        //  名称
	Platform    string `gorm:"column:platform" db:"platform" json:"platform"`            //  平台
	Endpoint    string `gorm:"column:endpoint" db:"endpoint" json:"endpoint"`            //  访问地址
	AccessName  string `gorm:"column:access_name" db:"access_name" json:"access_name"`   //  访问名称
	AccessToken string `gorm:"column:access_token" db:"access_token" json:"accessToken"` //  访问秘钥
	Enable      *bool  `gorm:"column:enable" db:"enable" json:"enable"`                  //  是否启用
	Context     string `gorm:"column:context" db:"context" json:"context"`               //  配置
}

func (ProjectAppCodeSource) TableName() string {
	return "code_source"
}

type CodeSourceUser struct {
	ID             int64      `gorm:"column:id" db:"id" json:"id"`                                                  //  主键
	CodeSourceUUID string     `gorm:"column:code_source_uuid" db:"code_source_uuid" json:"codeSourceUUID"`          //  代码源
	Email          string     `gorm:"column:email" db:"email" json:"email"`                                         //  邮箱
	UserID         string     `gorm:"column:user_id" db:"user_id" json:"userID"`                                    //  用户id
	Remark         string     `gorm:"column:remark" db:"remark" json:"remark"`                                      //  备注
	GmtCreate      *time.Time `gorm:"column:gmt_create" db:"gmt_create" json:"gmtCreate" form:"gmt_create"`         //  创建时间
	GmtModified    *time.Time `gorm:"column:gmt_modified" db:"gmt_modified" json:"gmtModified" form:"gmt_modified"` //  修改时间
}

func (CodeSourceUser) TableName() string {
	return "code_source_user"
}
