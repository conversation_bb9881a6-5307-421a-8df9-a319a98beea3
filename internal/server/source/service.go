package source

import (
	"context"
	"pipeline/pkg/common"
	"pipeline/pkg/util"
	"strconv"
	"time"

	"git.makeblock.com/makeblock-go/mysql/v2"
	"git.makeblock.com/makeblock-go/utils/v2/trace"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"pipeline/pkg/common/request"
	"pipeline/pkg/common/response"
)

type sourceService struct {
	*trace.Service
	db          *gorm.DB
	redisClient *redis.Client
}

func NewSourceService(ts *trace.Service, dbEngine *gorm.DB, redisClient *redis.Client) *sourceService {
	svc := &sourceService{
		Service: trace.IfNil(ts),
	}
	ctx := svc.AttachTrace(context.Background())
	svc.db = dbEngine.WithContext(ctx)
	svc.redisClient = redisClient.WithContext(ctx)
	return svc
}

func (s *sourceService) Info(uuid string) (*InfoSourceResponse, error) {
	repo := NewSourceRepo(s.db)
	m, err := repo.GetByUUID(uuid)
	if err != nil {
		return nil, err
	}
	var info InfoSourceResponse
	err = copier.Copy(&info, m)
	if err != nil {
		return nil, err
	}
	info.AccessToken = util.HideValue
	return &info, nil
}

func (s *sourceService) Add(r AddRequest, ops string) (id int64, err error) {
	repo := NewSourceRepo(s.db)
	var m ProjectAppCodeSource
	err = copier.Copy(&m, &r)
	m.Create(ops)
	if err != nil {
		return 0, err
	}
	return repo.Add(&m)
}

func (s *sourceService) Delete(ids []string, ops string) error {
	repo := NewSourceRepo(s.db)
	return repo.Delete(ids)
}

func (s *sourceService) Update(r UpdateRequest, ops string) (rowsAffected int64, err error) {
	repo := NewSourceRepo(s.db)
	m, err := repo.GetByUUID(r.UUID)
	if err != nil {
		return 0, err
	}
	// update the access token
	if r.AccessToken == util.HideValue {
		r.AccessToken = ""
	}
	// only encrypt when the access token is updated
	if r.AccessToken != "" {
		r.AccessToken, err = common.DefaultEncrypt(m.AccessToken)
		if err != nil {
			return 0, err
		}
	}
	err = copier.Copy(&m, &r)
	if err != nil {
		return 0, err
	}
	m.Update(ops)
	return repo.Update(m)
}

func (s *sourceService) Page(r PageRequest) (response.PageModel, error) {
	repo := NewSourceRepo(s.db)
	return repo.Page(r)
}

func (s *sourceService) List(r ListRequest) (results []*InfoResponse, err error) {
	repo := NewSourceRepo(s.db)
	list, err := repo.List(r)
	if err != nil {
		return nil, err
	}
	ret := make([]*InfoResponse, 0)
	for _, source := range list {
		var info InfoResponse
		sc := source
		err = copier.Copy(&info, &sc)
		if err != nil {
			return nil, err
		}
		ret = append(ret, &info)
	}
	return ret, nil
}

func (s *sourceService) Namespace(req NamespaceRequest) ([]Group, error) {
	source, err := NewSourceRepo(s.db).GetByUUID(req.UUID)
	if err != nil {
		return nil, err
	}
	//获取代码源实例
	resp, err := NewCodeSourceService(source).ListNamespaces(req.Name)
	if err != nil {
		return nil, err
	}
	return resp.list, nil
}

func (s *sourceService) Projects(req ProjectsRequest) ([]ProjectsResponse, error) {
	source, err := NewSourceRepo(s.db).GetByUUID(req.UUID)
	if err != nil {
		return nil, err
	}
	//获取代码源实例
	resp, err := NewCodeSourceService(source).ListRepositories(ProjectsOptions{
		Name: req.Name,
	})
	//
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *sourceService) SyncCodeSourceUser() error {
	repo := NewSourceRepo(mysql.GetDB())
	list, err := repo.List(ListRequest{})
	if err != nil {
		return err
	}
	now := time.Now()
	arr := make([]CodeSourceUser, 0)
	for _, source := range list {
		if source.Enable != nil && *source.Enable {
			cs := NewCodeSourceService(source)
			pg := request.PageRequest{
				Page:     1,
				PageSize: 100,
			}
			users := make([]CodeSourceUserEntry, 0)
			usersResponse, err := cs.ListUsers(UsersOptions{
				PageRequest: pg,
			})
			if err != nil {
				return err
			}
			users = append(users, usersResponse.List...)
			// 分页获取
			nextPage := usersResponse.NextPage
			for nextPage > 0 {
				pg.Page = usersResponse.NextPage
				ur, err := cs.ListUsers(UsersOptions{
					PageRequest: pg,
				})
				if err != nil {
					return err
				}
				users = append(users, ur.List...)
				nextPage = ur.NextPage
			}
			// 保存
			for _, user := range users {
				arr = append(arr, CodeSourceUser{
					CodeSourceUUID: source.UUID,
					UserID:         user.UserId,
					Email:          user.Email,
					GmtModified:    &now,
				})
			}
		}
	}
	return repo.AddCodeSourceUser(arr)
}

// 创建代码源用户(用于创建应用时将对应的用户保存到gitlab代码仓库成员中)

func (s *sourceService) CreateCodeSourceUser(req CodeSourceWebhookEvent) error {
	// 保存用户
	now := time.Now()
	user := CodeSourceUser{
		CodeSourceUUID: "52f6285c83ed46a58847a6876f7eaf74", //目前只有一个代码源，所以写死
		Email:          req.Email,
		UserID:         strconv.Itoa(req.UserId),
		GmtModified:    &now,
	}
	return s.db.Save(&user).Error
}
