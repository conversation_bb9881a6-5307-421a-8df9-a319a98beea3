package source

import (
	"fmt"
	"strconv"
	"strings"

	"pipeline/pkg/common/response"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"github.com/xanzy/go-gitlab"
)

var _ CodeSource = &GitlabCodeSource{}

func NewGitlabCodeSourceService(s *ProjectAppCodeSource) CodeSource {
	// ensure endpoint has no trailing slash
	s.Endpoint = strings.TrimSuffix(s.Endpoint, "/")
	git, err := gitlab.NewClient(
		s.AccessToken,
		gitlab.WithBaseURL(fmt.Sprintf("%s/api/v4", s.Endpoint)))
	if err != nil {
		log.ErrorE("Failed to create client: %v", err)
	}
	return GitlabCodeSource{
		client: git,
	}
}

type GitlabCodeSource struct {
	client *gitlab.Client
}

func (g GitlabCodeSource) GetRepository(opt InfoRepoOptions) (*InfoRepoResponse, error) {
	project, _, err := g.client.Projects.GetProject(opt.ProjectId, nil)
	if err != nil {
		return nil, err
	}
	return &InfoRepoResponse{
		ProjectId:     project.ID,
		SSHURLToRepo:  project.SSHURLToRepo,
		HTTPURLToRepo: project.HTTPURLToRepo,
	}, nil
}

func (g GitlabCodeSource) CreateRepository(opt CreateRepoOptions) (*CreateRepoResponse, error) {
	p := &gitlab.CreateProjectOptions{
		Name:        &opt.Name,
		NamespaceID: &opt.NamespaceId,
		Description: &opt.Desc,
		Path:        &opt.Name,
	}
	project, _, err := g.client.Projects.CreateProject(p)
	if err != nil {
		// check if project already exists, return formatted error
		if strings.Contains(err.Error(), "has already been taken") {
			return nil, response.RepositoryAlreadyExistError(opt.Name)
		}
		return nil, err
	}
	return &CreateRepoResponse{
		ProjectId:     project.ID,
		SSHURLToRepo:  project.SSHURLToRepo,
		HTTPURLToRepo: project.HTTPURLToRepo,
	}, nil
}

func (g GitlabCodeSource) AddRepositoryMember(opt AddProjectMemberOptions) (*AddProjectMemberResponse, error) {
	if len(opt.UserIds) == 0 {
		return nil, fmt.Errorf("userIds can not empty")
	}
	// case: may be some user id already exists, so we need to remove the existing user id
	// otherwise, because our add role only maintainer when the user role is owner, gitlab will return error
	userId2Int := make([]int, 0)
	for _, v := range opt.UserIds {
		userId, _ := strconv.ParseInt(v, 10, 64)
		userId2Int = append(userId2Int, int(userId))
	}
	existUserIds := make(map[int]struct{})
	var nextPage = 1
	for nextPage > 0 {
		members, r, err := g.client.ProjectMembers.ListAllProjectMembers(opt.ProjectId,
			&gitlab.ListProjectMembersOptions{
				UserIDs: &userId2Int,
				ListOptions: gitlab.ListOptions{
					Page:    nextPage,
					PerPage: 1000,
				},
			})
		if err != nil {
			return nil, err
		}
		for _, member := range members {
			existUserIds[member.ID] = struct{}{}
		}
		nextPage = r.NextPage
	}
	// filter should add user id
	shouldAddUserIds := make([]string, 0)
	for _, v := range opt.UserIds {
		userId, _ := strconv.ParseInt(v, 10, 64)
		if _, ok := existUserIds[int(userId)]; !ok {
			shouldAddUserIds = append(shouldAddUserIds, v)
		}
	}
	if len(shouldAddUserIds) == 0 {
		log.Info("no need to add project member")
		return &AddProjectMemberResponse{}, nil
	}
	// commas split userid
	sb := strings.Builder{}
	for i, v := range shouldAddUserIds {
		sb.WriteString(v)
		if i < len(shouldAddUserIds)-1 {
			sb.WriteString(",")
		}
	}
	p := &gitlab.AddProjectMemberOptions{
		UserID:      gitlab.String(sb.String()),
		AccessLevel: gitlab.AccessLevel(gitlab.MaintainerPermissions),
	}
	log.Info("add project member", log.Any("param", p))
	_, _, err := g.client.ProjectMembers.AddProjectMember(opt.ProjectId, p)
	if err != nil {
		return nil, err
	}
	return &AddProjectMemberResponse{}, nil
}

func (g GitlabCodeSource) CreateWebhook(opt CreateWebhookOptions) (*CreateWebhookResponse, error) {
	p := &gitlab.AddProjectHookOptions{
		URL:           gitlab.String(opt.WebhookURL),
		PushEvents:    gitlab.Bool(true),
		TagPushEvents: gitlab.Bool(true),
		//IssuesEvents:          gitlab.Bool(true),
		//ConfidentialIssuesEvents: gitlab.Bool(true),
		//NoteEvents:            gitlab.Bool(true),
		//ConfidentialNoteEvents: gitlab.Bool(true),
		//MergeRequestsEvents:   gitlab.Bool(true),
		//JobEvents:             gitlab.Bool(true),
		//PipelineEvents:        gitlab.Bool(true),
		//WikiPageEvents:        gitlab.Bool(true),
	}
	hook, _, err := g.client.Projects.AddProjectHook(opt.ProjectId, p)
	if err != nil {
		return nil, err
	}
	return &CreateWebhookResponse{
		ID: hook.ID,
	}, nil
}

func (g GitlabCodeSource) UploadFiles(opt UploadOptions) (*UploadResponse, error) {
	p := &gitlab.CreateCommitOptions{
		Branch:        gitlab.String(opt.Branch),
		CommitMessage: gitlab.String(opt.CommitMessage),
	}
	//加载opt.Path下的所有文件
	files, err := util.ReadFiles(opt.Path)
	if err != nil {
		return nil, err
	}
	for _, file := range files {
		// 仓库文件路径
		filePath := file.Path
		if opt.DistBasePath != "" {
			filePath = fmt.Sprintf("%s/%s", opt.DistBasePath, file.Path)
		}
		gitlabFile := gitlab.CommitActionOptions{
			Action:   gitlab.FileAction(gitlab.FileCreate),
			FilePath: gitlab.String(filePath),
			Content:  gitlab.String(file.Content), //文件内容
		}
		// 如果是图片类似的文件，需要设置为base64
		if util.IsImageFile(filePath) {
			gitlabFile.Encoding = util.String2Ptr("base64")
			gitlabFile.Content = gitlab.String(util.Base64Encode(file.Content))
		}
		p.Actions = append(p.Actions, &gitlabFile)
	}
	cr, _, err := g.client.Commits.CreateCommit(opt.ProjectId, p)
	if err != nil {
		return nil, err
	}
	return &UploadResponse{
		CommitId: cr.ID,
	}, nil
}

func (g GitlabCodeSource) ListBranches(opt BranchesOptions) (*BranchesResponse, error) {
	p := &gitlab.ListBranchesOptions{}
	if opt.Name != "" {
		p.Search = gitlab.String(opt.Name)
	}
	branches, _, err := g.client.Branches.ListBranches(opt.ProjectId, p)
	if err != nil {
		return nil, err
	}
	ret := &BranchesResponse{}
	for _, branch := range branches {
		ret.List = append(ret.List, branch.Name)
	}
	return ret, nil
}

func (g GitlabCodeSource) ListNamespaces(name string) (*NamespacesResponse, error) {
	var groups []*gitlab.Group
	var err error
	// search group by name
	if name != "" {
		groups, _, err = g.client.Groups.SearchGroup(name)
		if err != nil {
			log.ErrorE("Failed to search group", err)
		}
	} else {
		// list all groups(max 20 per page)
		groups, _, err = g.client.Groups.ListGroups(&gitlab.ListGroupsOptions{})
		if err != nil {
			return nil, err
		}
	}
	ret := &NamespacesResponse{}
	for _, group := range groups {
		ret.list = append(ret.list, Group{
			ID:   group.ID,
			Name: group.FullName,
		})
	}
	return ret, nil
}

func (g GitlabCodeSource) ListRepositories(opt ProjectsOptions) ([]ProjectsResponse, error) {
	projects, _, err := g.client.Projects.ListProjects(&gitlab.ListProjectsOptions{
		Search: gitlab.String(opt.Name),
	})
	if err != nil {
		return nil, err
	}
	ret := make([]ProjectsResponse, 0)
	for _, project := range projects {
		ret = append(ret, ProjectsResponse{
			ProjectId:     project.ID,
			Name:          project.Name,
			FullName:      project.PathWithNamespace,
			SSHURLToRepo:  project.SSHURLToRepo,
			HTTPURLToRepo: project.HTTPURLToRepo,
		})
	}
	return ret, nil
}

func (g GitlabCodeSource) ListUsers(opt UsersOptions) (*UsersResponse, error) {
	gOpt := &gitlab.ListUsersOptions{
		ListOptions: gitlab.ListOptions{
			Page:    opt.Page,
			PerPage: opt.PageSize,
		},
	}
	if opt.CreatedAfter != nil {
		gOpt.CreatedAfter = opt.CreatedAfter
	}
	users, resp, err := g.client.Users.ListUsers(gOpt)
	if err != nil {
		return nil, err
	}
	ret := &UsersResponse{
		TotalItems:   resp.TotalItems,
		TotalPages:   resp.TotalPages,
		ItemsPerPage: resp.ItemsPerPage,
		CurrentPage:  resp.CurrentPage,
		NextPage:     resp.NextPage,
		PreviousPage: resp.PreviousPage,
		List:         make([]CodeSourceUserEntry, 0),
	}
	for _, user := range users {
		ret.List = append(ret.List, CodeSourceUserEntry{
			UserId:   strconv.Itoa(user.ID),
			Name:     user.Name,
			Email:    user.Email,
			Username: user.Username,
		})
	}
	return ret, nil
}

func (g GitlabCodeSource) GetBranch(opt BranchOptions) (*BranchResponse, error) {
	branch, resp, err := g.client.Branches.GetBranch(opt.ProjectId, opt.BranchName)
	if err != nil {
		// match error
		if resp != nil && resp.StatusCode == 404 {
			return nil, response.BranchNotFoundError(opt.BranchName)
		}
		return nil, err
	}
	ret := &BranchResponse{
		CommitId:      branch.Commit.ID,
		CommitMessage: branch.Commit.Message,
		CommitURL:     branch.Commit.WebURL,
	}
	return ret, nil
}

func (g GitlabCodeSource) ListTags(opt TagsOptions) (*TagsResponse, error) {
	tags, _, err := g.client.Tags.ListTags(opt.ProjectId, &gitlab.ListTagsOptions{
		Search: gitlab.String(opt.Name),
		ListOptions: gitlab.ListOptions{
			Page:    opt.Page,
			PerPage: opt.PageSize,
		},
	})
	if err != nil {
		return nil, err
	}
	ret := &TagsResponse{
		List: make([]TagInfo, 0),
	}
	// range tags
	for _, tag := range tags {
		ret.List = append(ret.List, TagInfo{
			Name:      tag.Name,
			CommitSHA: tag.Target,
		})
	}
	return ret, nil
}

func (g GitlabCodeSource) GetTag(opt TagOptions) (*TagResponse, error) {
	tag, resp, err := g.client.Tags.GetTag(opt.ProjectId, opt.TagName)
	if err != nil {
		if resp != nil && resp.StatusCode == 404 {
			return nil, response.TagNotFoundError(opt.TagName)
		}
		return nil, err
	}
	return &TagResponse{
		Message:       tag.Message,
		CommitId:      tag.Commit.ID,
		CommitMessage: tag.Commit.Message,
		CommitURL:     tag.Commit.WebURL,
	}, nil
}
