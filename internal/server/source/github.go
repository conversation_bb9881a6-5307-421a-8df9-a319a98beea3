package source

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"pipeline/pkg/common/response"
	"pipeline/pkg/util"

	"git.makeblock.com/makeblock-go/log"
	"github.com/google/go-github/v72/github"
	"golang.org/x/oauth2"
)

var _ CodeSource = &GitHubCodeSource{}

func NewGitHubCodeSourceService(s *ProjectAppCodeSource) CodeSource {
	// ensure endpoint has no trailing slash
	s.Endpoint = strings.TrimSuffix(s.Endpoint, "/")

	ctx := context.Background()
	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: s.AccessToken},
	)
	tc := oauth2.NewClient(ctx, ts)

	client := github.NewClient(tc)

	// Set custom endpoint if not using github.com
	if s.Endpoint != "" && s.Endpoint != "https://github.com" {
		var err error
		client, err = github.NewClient(tc).WithEnterpriseURLs(s.Endpoint+"/api/v3", s.Endpoint+"/api/uploads")
		if err != nil {
			log.ErrorE("Failed to create github enterprise client", err)
		}
	}

	return GitHubCodeSource{
		client: client,
		ctx:    ctx,
	}
}

type GitHubCodeSource struct {
	client *github.Client
	ctx    context.Context
}

func (g GitHubCodeSource) GetRepository(opt InfoRepoOptions) (*InfoRepoResponse, error) {
	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}
	return &InfoRepoResponse{
		ProjectId:     int(repo.GetID()),
		Name:          repo.GetName(),
		FullName:      repo.GetFullName(),
		SSHURLToRepo:  repo.GetSSHURL(),
		HTTPURLToRepo: repo.GetCloneURL(),
	}, nil
}

func (g GitHubCodeSource) CreateRepository(opt CreateRepoOptions) (*CreateRepoResponse, error) {
	repoOptions := &github.Repository{
		Name:        github.Ptr(opt.Name),
		Description: github.Ptr(opt.Desc),
	}

	var repo *github.Repository
	var err error

	if opt.NamespaceId > 0 {
		// Create repository in organization
		// Note: GitHub uses organization name (string), not ID
		// For this implementation, we'll assume NamespaceId maps to an org name
		// In practice, you might need to map the ID to the org name first
		orgName := strconv.Itoa(opt.NamespaceId) // This might need adjustment based on your use case
		repo, _, err = g.client.Repositories.Create(g.ctx, orgName, repoOptions)
	} else {
		// Create repository for current user
		repo, _, err = g.client.Repositories.Create(g.ctx, "", repoOptions)
	}

	if err != nil {
		// check if repository already exists
		if strings.Contains(err.Error(), "already exists") {
			return nil, response.RepositoryAlreadyExistError(opt.Name)
		}
		return nil, err
	}

	return &CreateRepoResponse{
		ProjectId:     int(repo.GetID()),
		SSHURLToRepo:  repo.GetSSHURL(),
		HTTPURLToRepo: repo.GetCloneURL(),
	}, nil
}

func (g GitHubCodeSource) AddRepositoryMember(opt AddProjectMemberOptions) (*AddProjectMemberResponse, error) {
	if len(opt.UserIds) == 0 {
		return nil, fmt.Errorf("userIds can not empty")
	}

	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	owner := repo.GetOwner().GetLogin()
	repoName := repo.GetName()

	for _, userIdStr := range opt.UserIds {
		// GitHub uses usernames, not IDs for collaboration
		// You might need to convert userIdStr to username first
		_, _, err := g.client.Repositories.AddCollaborator(g.ctx, owner, repoName, userIdStr, &github.RepositoryAddCollaboratorOptions{
			Permission: "write", // Equivalent to maintainer permissions
		})
		if err != nil {
			log.ErrorE("Failed to add collaborator", err)
			// Continue processing other users
		}
	}

	return &AddProjectMemberResponse{}, nil
}

func (g GitHubCodeSource) CreateWebhook(opt CreateWebhookOptions) (*CreateWebhookResponse, error) {
	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	owner := repo.GetOwner().GetLogin()
	repoName := repo.GetName()

	hookConfig := &github.HookConfig{
		URL:         github.Ptr(opt.WebhookURL),
		ContentType: github.Ptr("json"),
	}

	hookOptions := &github.Hook{
		Name:   github.Ptr("web"),
		Config: hookConfig,
		Events: []string{"push", "create"}, // Equivalent to push_events and tag_push_events
		Active: github.Ptr(true),
	}

	hook, _, err := g.client.Repositories.CreateHook(g.ctx, owner, repoName, hookOptions)
	if err != nil {
		return nil, err
	}

	return &CreateWebhookResponse{
		ID: int(hook.GetID()),
	}, nil
}

func (g GitHubCodeSource) UploadFiles(opt UploadOptions) (*UploadResponse, error) {
	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	owner := repo.GetOwner().GetLogin()
	repoName := repo.GetName()

	// Load all files from opt.Path
	files, err := util.ReadFiles(opt.Path)
	if err != nil {
		return nil, err
	}

	var lastCommitSHA string

	for _, file := range files {
		// Repository file path
		filePath := file.Path
		if opt.DistBasePath != "" {
			filePath = fmt.Sprintf("%s/%s", opt.DistBasePath, file.Path)
		}

		fileContent := file.Content
		// If it's an image file, need base64 encoding
		if util.IsImageFile(filePath) {
			fileContent = util.Base64Encode(file.Content)
		}

		// Get current file (if exists) to handle updates
		var currentFile *github.RepositoryContent
		currentFile, _, _, _ = g.client.Repositories.GetContents(g.ctx, owner, repoName, filePath, &github.RepositoryContentGetOptions{
			Ref: opt.Branch,
		})

		fileOptions := &github.RepositoryContentFileOptions{
			Message: github.Ptr(opt.CommitMessage),
			Content: []byte(fileContent),
			Branch:  github.Ptr(opt.Branch),
		}

		// If file exists, add SHA for update
		if currentFile != nil {
			fileOptions.SHA = currentFile.SHA
		}

		commitResponse, _, err := g.client.Repositories.CreateFile(g.ctx, owner, repoName, filePath, fileOptions)
		if err != nil {
			log.ErrorE("Failed to upload file", err)
			return nil, err
		}

		if commitResponse.Commit.SHA != nil {
			lastCommitSHA = *commitResponse.Commit.SHA
		}
	}

	return &UploadResponse{
		CommitId: lastCommitSHA,
	}, nil
}

func (g GitHubCodeSource) ListBranches(opt BranchesOptions) (*BranchesResponse, error) {
	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	owner := repo.GetOwner().GetLogin()
	repoName := repo.GetName()

	branches, _, err := g.client.Repositories.ListBranches(g.ctx, owner, repoName, &github.BranchListOptions{})
	if err != nil {
		return nil, err
	}

	ret := &BranchesResponse{}
	for _, branch := range branches {
		branchName := branch.GetName()
		if opt.Name == "" || strings.Contains(branchName, opt.Name) {
			ret.List = append(ret.List, branchName)
		}
	}

	return ret, nil
}

func (g GitHubCodeSource) ListNamespaces(name string) (*NamespacesResponse, error) {
	var orgs []*github.Organization

	if name != "" {
		// Search for specific organization
		searchOpts := &github.SearchOptions{
			TextMatch: true,
		}
		result, _, err := g.client.Search.Users(g.ctx, fmt.Sprintf("%s type:org", name), searchOpts)
		if err != nil {
			return nil, err
		}

		for _, user := range result.Users {
			if user.GetLogin() == name {
				org, _, err := g.client.Organizations.Get(g.ctx, user.GetLogin())
				if err == nil {
					orgs = append(orgs, org)
				}
			}
		}
	} else {
		// List current user's organizations
		userOrgs, _, err := g.client.Organizations.List(g.ctx, "", &github.ListOptions{})
		if err != nil {
			return nil, err
		}
		orgs = userOrgs
	}

	ret := &NamespacesResponse{}
	for _, org := range orgs {
		ret.list = append(ret.list, Group{
			ID:   int(org.GetID()),
			Name: org.GetName(),
		})
	}

	return ret, nil
}

func (g GitHubCodeSource) ListRepositories(opt ProjectsOptions) ([]ProjectsResponse, error) {
	searchQuery := ""
	if opt.Name != "" {
		searchQuery = opt.Name
	}

	searchOpts := &github.SearchOptions{}
	repos, _, err := g.client.Search.Repositories(g.ctx, searchQuery, searchOpts)
	if err != nil {
		return nil, err
	}

	ret := make([]ProjectsResponse, 0)
	for _, repo := range repos.Repositories {
		ret = append(ret, ProjectsResponse{
			ProjectId:     int(repo.GetID()),
			Name:          repo.GetName(),
			FullName:      repo.GetFullName(),
			SSHURLToRepo:  repo.GetSSHURL(),
			HTTPURLToRepo: repo.GetCloneURL(),
		})
	}

	return ret, nil
}

func (g GitHubCodeSource) ListUsers(opt UsersOptions) (*UsersResponse, error) {
	searchOpts := &github.SearchOptions{
		ListOptions: github.ListOptions{
			Page:    opt.Page,
			PerPage: opt.PageSize,
		},
	}

	query := "type:user"
	if opt.CreatedAfter != nil {
		query += fmt.Sprintf(" created:>%s", opt.CreatedAfter.Format("2006-01-02"))
	}

	result, _, err := g.client.Search.Users(g.ctx, query, searchOpts)
	if err != nil {
		return nil, err
	}

	ret := &UsersResponse{
		TotalItems:  result.GetTotal(),
		CurrentPage: opt.Page,
		List:        make([]CodeSourceUserEntry, 0),
	}

	for _, user := range result.Users {
		ret.List = append(ret.List, CodeSourceUserEntry{
			UserId:   strconv.FormatInt(user.GetID(), 10),
			Name:     user.GetName(),
			Email:    user.GetEmail(),
			Username: user.GetLogin(),
		})
	}

	return ret, nil
}

func (g GitHubCodeSource) GetBranch(opt BranchOptions) (*BranchResponse, error) {
	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	owner := repo.GetOwner().GetLogin()
	repoName := repo.GetName()

	branch, resp, err := g.client.Repositories.GetBranch(g.ctx, owner, repoName, opt.BranchName, 3)
	if err != nil {
		if resp != nil && resp.StatusCode == 404 {
			return nil, response.BranchNotFoundError(opt.BranchName)
		}
		return nil, err
	}

	commit := branch.GetCommit()
	ret := &BranchResponse{
		CommitId:      commit.GetSHA(),
		CommitMessage: commit.Commit.GetMessage(),
		CommitURL:     commit.GetHTMLURL(),
	}

	return ret, nil
}

func (g GitHubCodeSource) ListTags(opt TagsOptions) (*TagsResponse, error) {
	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	owner := repo.GetOwner().GetLogin()
	repoName := repo.GetName()

	listOpts := &github.ListOptions{
		Page:    opt.Page,
		PerPage: opt.PageSize,
	}

	tags, _, err := g.client.Repositories.ListTags(g.ctx, owner, repoName, listOpts)
	if err != nil {
		return nil, err
	}

	ret := &TagsResponse{
		List: make([]TagInfo, 0),
	}

	for _, tag := range tags {
		tagName := tag.GetName()
		if opt.Name == "" || strings.Contains(tagName, opt.Name) {
			ret.List = append(ret.List, TagInfo{
				Name:      tagName,
				CommitSHA: tag.Commit.GetSHA(),
			})
		}
	}

	return ret, nil
}

func (g GitHubCodeSource) GetTag(opt TagOptions) (*TagResponse, error) {
	repo, _, err := g.client.Repositories.GetByID(g.ctx, int64(opt.ProjectId))
	if err != nil {
		return nil, err
	}

	owner := repo.GetOwner().GetLogin()
	repoName := repo.GetName()

	// GitHub doesn't have a direct "get tag" API, so we list tags and find the specific one
	tags, _, err := g.client.Repositories.ListTags(g.ctx, owner, repoName, &github.ListOptions{PerPage: 100})
	if err != nil {
		return nil, err
	}

	var targetTag *github.RepositoryTag
	for _, tag := range tags {
		if tag.GetName() == opt.TagName {
			targetTag = tag
			break
		}
	}

	if targetTag == nil {
		return nil, response.TagNotFoundError(opt.TagName)
	}

	// Get tag details if it's an annotated tag
	var tagMessage string
	if targetTag.GetCommit().GetSHA() != "" {
		gitTag, _, err := g.client.Git.GetTag(g.ctx, owner, repoName, targetTag.GetCommit().GetSHA())
		if err == nil {
			tagMessage = gitTag.GetMessage()
		}
	}

	return &TagResponse{
		Message:       tagMessage,
		CommitId:      targetTag.Commit.GetSHA(),
		CommitMessage: "", // Tag commits don't have direct message access
		CommitURL:     targetTag.GetCommit().GetURL(),
	}, nil
}
