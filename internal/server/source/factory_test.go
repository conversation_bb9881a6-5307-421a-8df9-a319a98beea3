package source

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewCodeSourceService_GitLab(t *testing.T) {
	config := &ProjectAppCodeSource{
		Platform:    "gitlab",
		Endpoint:    "https://gitlab.example.com",
		AccessToken: "test-token",
	}

	service := NewCodeSourceService(config)
	assert.NotNil(t, service)

	// 验证返回的是GitLab类型
	_, ok := service.(GitlabCodeSource)
	assert.True(t, ok, "Should return GitlabCodeSource for gitlab platform")
}

func TestNewCodeSourceService_Gitea(t *testing.T) {
	config := &ProjectAppCodeSource{
		Platform:    "gitea",
		Endpoint:    "https://gitea.example.com",
		AccessToken: "test-token",
	}

	service := NewCodeSourceService(config)
	assert.NotNil(t, service)

	// 验证返回的是Gitea类型
	_, ok := service.(GiteaCodeSource)
	assert.True(t, ok, "Should return GiteaCodeSource for gitea platform")
}

func TestNewCodeSourceService_GitHub(t *testing.T) {
	config := &ProjectAppCodeSource{
		Platform:    "github",
		Endpoint:    "https://github.com",
		AccessToken: "test-token",
	}

	service := NewCodeSourceService(config)
	assert.NotNil(t, service)

	// 验证返回的是GitHub类型
	_, ok := service.(GitHubCodeSource)
	assert.True(t, ok, "Should return GitHubCodeSource for github platform")
}

func TestNewCodeSourceService_UnsupportedPlatform(t *testing.T) {
	config := &ProjectAppCodeSource{
		Platform:    "unsupported",
		Endpoint:    "https://example.com",
		AccessToken: "test-token",
	}

	service := NewCodeSourceService(config)
	assert.Nil(t, service, "Should return nil for unsupported platform")
}

func TestNewCodeSourceService_EmptyPlatform(t *testing.T) {
	config := &ProjectAppCodeSource{
		Platform:    "",
		Endpoint:    "https://example.com",
		AccessToken: "test-token",
	}

	service := NewCodeSourceService(config)
	assert.Nil(t, service, "Should return nil for empty platform")
}

// 测试所有平台的接口实现
func TestCodeSourceInterface_Implementation(t *testing.T) {
	platforms := []struct {
		name     string
		platform string
		endpoint string
	}{
		{"GitLab", "gitlab", "https://gitlab.example.com"},
		{"Gitea", "gitea", "https://gitea.example.com"},
		{"GitHub", "github", "https://github.com"},
	}

	for _, tc := range platforms {
		t.Run(tc.name, func(t *testing.T) {
			config := &ProjectAppCodeSource{
				Platform:    tc.platform,
				Endpoint:    tc.endpoint,
				AccessToken: "test-token",
			}

			service := NewCodeSourceService(config)
			assert.NotNil(t, service, "%s service should not be nil", tc.name)

			// 验证实现了CodeSource接口
			_, ok := service.(CodeSource)
			assert.True(t, ok, "%s should implement CodeSource interface", tc.name)
		})
	}
}
