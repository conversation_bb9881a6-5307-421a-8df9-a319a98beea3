package config

import (
	"time"

	"github.com/jinzhu/configor"
)

var config configuration

// Items Items
var Items = func() configuration {
	return config
}

type configuration struct {
	// 基础信息
	AppName          string `env:"APP_NAME" default:"pipeline"`
	ProjectEnv       string `env:"PROJECT_ENV" default:"dev"`
	APIVersion       string `env:"API_VERSION" default:"Commit ID"`
	Workspace        string `env:"WORKSPACE" default:"~/.pipeline"`
	FrontEndEndpoint string `env:"FRONT_END_ENDPOINT" default:"http://local.makeblock.com:3001"`

	// 缺省数据库
	Mysql struct {
		Host    string `default:"mysql-sz.makeblock.com"`
		Port    string `default:"3306"`
		DBName  string `default:"pipeline-dev"`
		User    string `default:"root"`
		Pwd     string `default:"tEKIZtC0CXjg"`
		Charset string `default:"utf8mb4"`
	}

	// 缺省redis
	Redis struct {
		Host   string `default:"127.0.0.1"`
		Port   string `default:"6379"`
		Pwd    string `default:""`
		Prefix string `default:"'pipeline:'"`
	}

	// OSS配置
	OSS struct {
		// https://min.io/docs/minio/linux/administration/object-management/create-lifecycle-management-expiration-rule.html
		CachesBucketName    string `default:"pipeline-cache"`    // 用于存储缓存
		LoggerBucketName    string `default:"pipeline-logger"`   // 用于存储日志
		ArtifactsBucketName string `default:"pipeline-artifact"` // 用于存储构建物
		// minio配置
		Minio struct {
			Endpoint  string `default:"minio-dev.makeblock.com:9000"`
			AccessKey string `default:"YIZmLTaucpkX7raXIwMr"`
			SecretKey string `default:"oTxO2Aa75KhErRVm3N9LRZ4gK9rENA5gpESODvYc"`
		}
	}

	// 流水线配置
	Pipeline struct {
		// 默认流水线step执行超时时间
		Timeout time.Duration `yaml:"timeout" env:"TIMEOUT" default:"60m"`
		// 默认存留最大流水线记录数
		MaxKeepItem uint `yaml:"max_keep_item" env:"MAX_KEEP_ITEM" default:"30"`
		// 检查流水线step执行超时时间间隔
		StepTimeoutCheckInterval time.Duration `yaml:"step_timeout_check_interval" env:"STEP_TIMEOUT_CHECK_INTERVAL" default:"10s"`
		// 用于接收git仓库回调地址进行触发流水线执行
		WebHookEndpoint string `env:"WEBHOOK_ENDPOINT" default:"https://pipeline-dev.makeblock.com"`
		// 任务详情URL（feishu通知必须携带http或者https协议）
		PipelineURL string `env:"PIPELINE_URL" default:"/pipeline/#/cicd/pipeline?execute=%s"`
		// 最大重试次数
		ActionMaxRetries uint `yaml:"action_max_retries" env:"ACTION_MAX_RETRIES" default:"5"`
		// 单个制品最大大小byte（默认：2GB=2*1024*1024*1024）
		MaxArtifactSize int64 `yaml:"max_artifact_size" env:"MAX_ARTIFACT_SIZE" default:"2147483648"`
		// action日志最大大小byte,超出丢弃（默认：100MB=1024*1024*100）
		MaxActionLogSize int64 `yaml:"max_artifact_size" env:"MAX_ARTIFACT_SIZE" default:"104857600"`
		// 是否开启cron任务
		EnableCron bool `yaml:"enable_cron" env:"ENABLE_CRON" default:"true"`
		// 解密秘钥(凭证、流水线秘钥、代码源)
		SecretKey string `yaml:"secret_key" env:"SECRET_KEY" default:"auenzeunehjwadfe"` // AES only supports key sizes of 16, 24 or 32 bytes
		// 缓存类型
		CacheType string `yaml:"cache_type" env:"CACHE_TYPE" default:"cache-local"`
		// runner 快捷搜索地址
		RunnerSearchUrl string `env:"RUNNER_SEARCH_URL" default:"/pipeline/#/cicd/analytics?runnerName=%s"`
		// 执行补偿日志transfers到oss的定时任务触发时间, 默认每天凌晨5点
		TransferActionLoggerCron string `env:"TRANSFER_ACTION_LOGGER_CRON" default:"0 5 * * *"`
	}

	// influxdb配置(存储runner指标数据)
	InfluxDB struct {
		Url             string `env:"INFLUXDB_URL" default:"http://localhost:8086"`
		Token           string `env:"INFLUXDB_TOKEN" default:"F6969B3F-471B-48F9-A878-10AE404942BE"`
		Organization    string `env:"INFLUXDB_RUNNER_ORG" default:"makeblock-devops"`
		Bucket          string `env:"INFLUXDB_RUNNER_BUCKET" default:"pipeline"`
		RetentionPeriod string `env:"INFLUXDB_RUNNER_RETENTION_PERIOD" default:"604800"` // 数据保留时间7天
	}

	// sonarqube配置(runner执行sonarqube扫描时需要)
	SonarQube struct {
		Endpoint string `env:"SONARQUBE_ENDPOINT" default:"http://sonarqube.makeblock.com"`
		Token    string `env:"SONARQUBE_TOKEN" default:"squ_46289eaa209d35b8934445064788419ca576d8ca"`
	}

	// feishu app配置(runner通知资源占用)
	NotifyFeishuApp struct {
		AppID     string `env:"LARK_APP_ID" default:"********************"`
		AppSecret string `env:"LARK_APP_SECRET" default:"w4tDQ2pwkbCRe7WOOR7x8cVvXZll4FFg"`
	}

	// runner指标通知配置
	RunnerNotify struct {
		// 是否开启资源监控
		Enable bool `env:"RUNNER_NOTIFY_ENABLE" default:"false"`
		// CPU阈值百分比，要是超过这个值就会触发通知
		CPUThresholdRate int `env:"CPU_THRESHOLD_RATE" default:"90"`
		// 内存阈值百分比，要是超过这个值就会触发通知
		MemThresholdRate int `env:"MEM_THRESHOLD_RATE" default:"90"`
		// 磁盘阈值百分比，要是超过这个值就会触发通知
		DiskThresholdRate int `env:"DISK_THRESHOLD_RATE" default:"90"`
		// 通知接收人
		Receiver string `env:"RUNNER_NOTIFY_RECEIVER" default:"oc_7880791cc905b441763e64faeb71994e"`
		// 预警通知频率
		NotifyInterval time.Duration `env:"RUNNER_NOTIFY_INTERVAL" default:"1m"`
		// 超出阈值持续时间（满足才发送通知）
		ThresholdDuration time.Duration `env:"THRESHOLD_DURATION" default:"5m"`
		// 是否开启离线通知
		EnableOfflineNotify bool `env:"RUNNER_OFFLINE_NOTIFY_ENABLE" default:"false"`
		// 离线通知重复的时间间隔
		OfflineNotifyInterval time.Duration `env:"OFFLINE_NOTIFY_INTERVAL" default:"30m"`
		// ticker 周期
		TickerInterval time.Duration `env:"RUNNER_TICKER_INTERVAL" default:"5m"`
	}

	Runner struct {
		// 自定义缓存过期时间
		CacheExpire time.Duration `env:"CACHE_EXPIRE" default:"720h"`
		// debug模式下清理任务延迟
		DebugPurgeDelay time.Duration `env:"RUNNER_DEBUG_PURGE_DELAY" default:"30m"`
	}

	// 常量token用于外部调用时做鉴权
	ApiToken string `yaml:"api_token" env:"API_TOKEN" default:"119ebe93dd0d"`

	// 应用配置审批通知
	AppConfigApproval struct {
		// 审批通知发送的群
		ChatID string `env:"APP_CONFIG_APPROVAL_NOTICE_CHAT_ID" default:"oc_69a6056f60df974f728f89798f8c4b91"`
		// 审批详情基础链接
		InfoBaseURL string `env:"APP_CONFIG_APPROVAL_INFO_BASEURL" default:"/pipeline/#/cicd/application?action=config&uuid=%s&plugin=%s"`
		// 是否开启自动审批
		AutoApproval bool `env:"APP_CONFIG_AUTO_APPROVAL" default:"false"`
	}

	EfficacyApi struct {
		Endpoint string `env:"EFFICACY_API_ENDPOINT" default:"https://api-dev.makeblock.com"`
		Token    string `env:"EFFICACY_API_TOKEN" default:"8F5A3AB5-0D41-473A-947D-7CC3362F14B5"`
	}

	// 事件订阅
	Subscribes struct {
		// 同步应用关联用户到监控平台用于预警到指定的人
		Monitor string `env:"SUBSCRIBES_MONITOR" default:"https://api-dev.makeblock.com"`
	}

	// 应用配置插件
	AppConfigPlugins struct {
		// 保留的历史版本数
		MaxHistoryVersion int `env:"APP_CONFIG_PLUGINS_MAX_HISTORY_VERSION" default:"30"`
		// 应用模板+流水线模板
		Kubernetes string `env:"APP_CONFIG_PLUGINS_KUBERNETES" default:"http-golang,grpc-golang,http-python,http-nodejs,nuxt3,98ca01fc23f144888559a54bb0e12013,1e99b087ad064d10a5f9189e62ab792f"`
		// 针对 AI 的模板
		AIKubernetes string `env:"AI_APP_CONFIG_PLUGINS_KUBERNETES" default:"http-python-ai,189379d4df784435b9229c84c390f593"`
	}

	// LLM 配置
	LLMConfig struct {
		// 外部部署的模型
		External struct {
			Model         string        `yaml:"model" env:"LLM_CONFIG_MODEL" default:"ep-20250328103048-gxgk5"`                    // ds
			APIKey        string        `yaml:"api_key" env:"LLM_CONFIG_API_KEY" default:"258364127242308292_linz12_arkdeeps_dev"` // ds
			BaseURL       string        `yaml:"base_url" env:"LLM_CONFIG_BASE_URL" default:"https://api-dev.makeblock.com/store-api/v1/compatible-mode/v1"`
			Timeout       time.Duration `yaml:"timeout" env:"LLM_CONFIG_TIMEOUT" default:"2m"`
			MaxTokens     int           `yaml:"max_tokens" env:"LLM_CONFIG_MAX_TOKENS" default:"2000"`
			Temperature   float32       `yaml:"temperature" env:"LLM_CONFIG_TEMPERATURE" default:"0.7"`
			NotifyGroupId string        `yaml:"notify_group_id" env:"LLM_CONFIG_NOTIFY_GROUP_ID" default:"oc_fae819cafcbae2b63813324a01f5f8a9"`
		}
		// 内部部署的模型
		SelfHosted struct {
			Model       string        `yaml:"model" env:"LLM_CONFIG_SELF_HOSTED_MODEL" default:"qwen2.5:72b-instruct-q8_0"`
			APIKey      string        `yaml:"api_key" env:"LLM_CONFIG_SELF_HOSTED_API_KEY" default:""`
			BaseURL     string        `yaml:"base_url" env:"LLM_CONFIG_SELF_HOSTED_BASE_URL" default:"http://ollama.makeblock.com:11434/v1"`
			Timeout     time.Duration `yaml:"timeout" env:"LLM_CONFIG_SELF_HOSTED_TIMEOUT" default:"3m"`
			MaxTokens   int           `yaml:"max_tokens" env:"LLM_CONFIG_SELF_HOSTED_MAX_TOKENS" default:"10000"`
			Temperature float32       `yaml:"temperature" env:"LLM_CONFIG_TEMPERATURE" default:"0.7"`
		}
	}

	RabbitMqConfig struct {
		Url          string `yaml:"url" env:"RABBITMQ_URL" default:"amqp://pipeline:EZ7WbC1Y6xAg@************:5680//pipeline-dev"`
		Exchange     string `yaml:"exchange" env:"RABBITMQ_EXCHANGE" default:"devops"`
		Queue        string `yaml:"queue" env:"RABBITMQ_QUEUE" default:"pipeline-local"` // 相同实例下不要声明相同的队列，需要按环境添加后缀：local、dev、test、prod
		CardRouteKey string `yaml:"routeKey" env:"RABBITMQ_CARD_ROUTE_KEY" default:"pipeline_card"`
	}

	// sentry
	Sentry struct {
		DSN      string `env:"SENTRY_DSN" default:"https://<EMAIL>/13"`
		Endpoint string `env:"SENTRY_ENDPOINT" default:"https://sentry.xtool.com"`
		OrgSlug  string `env:"SENTRY_ORG_SLUG" default:"sentry"`
		ApiToken string `env:"SENTRY_API_TOKEN" default:"****************************************************************"`
	}
}

// Load load configurations
func Load() {
	conf := &configor.Config{ENVPrefix: "-"}
	if err := configor.New(conf).Load(&config, "/pipeline/etc/config.yaml", "config.yaml", "../config.yaml"); err != nil {
		panic(err)
	}
}
