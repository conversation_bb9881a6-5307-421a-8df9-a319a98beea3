// nolint:lll
package config

import (
	"encoding/json"
	"fmt"
	"git.makeblock.com/makeblock-go/log"
	api "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"os"
	"path/filepath"
	"pipeline/pkg/util"
	"time"

	"github.com/jinzhu/configor"
)

const WARNING = "This file is automatically generated by runner. Do not edit it manually unless you know what you are doing. Removing this file will cause runner to re-register as a new runner."

var runnerConfig RunnerConfig

var GetRunnerConfig = func() RunnerConfig {
	return runnerConfig
}

type RunnerConfig struct {
	// sentry
	SentryDSN string `yaml:"sentry_dsn" env:"SENTRY_DSN" default:"https://<EMAIL>/14"`
	// for runner runtime
	Workspace string `yaml:"workspace" env:"WORKSPACE" default:"~/.runner"` // Workspace is the directory where the runner stores data.
	// for log
	Runner Runner `yaml:"runner"` // Runner represents the configuration for the runner.
	// for update
	Update Update `yaml:"update"` // runner self update.
	// for kubernetes runtime
	Kubernetes Kubernetes `yaml:"kubernetes"` // Kubernetes represents the configuration for the kubernetes runtime.
	// for runner cache
	Cache Cache `yaml:"cache"` // Cache represents the configuration for the cache.
}

type Cache struct {
	RunnerLocalCachePath   string `yaml:"runner_local_cache_path" env:"RUNNER_LOCAL_CACHE_PATH"`
	EnableAutomaticCleanup bool   `yaml:"enable_automatic_cleanup" env:"ENABLE_AUTOMATIC_CLEANUP" default:"false"`
	AutomaticCleanupCron   string `yaml:"automatic_cleanup_cron" env:"AUTOMATIC_CLEANUP_CRON" default:"00 00 02 * * *"` // 每天 02:00:00 执行
}

// Runner represents the configuration for the runner.
type Runner struct {
	File                  string        `yaml:"file" env:"FILE" default:".runner"`
	Capacity              int           `yaml:"capacity" env:"CAPACITY" default:"3"`
	Timeout               time.Duration `yaml:"timeout" env:"TIMEOUT" default:"2h"`
	ShutdownTimeout       time.Duration `yaml:"shutdown_timeout" env:"SHUTDOWN_TIMEOUT" default:"3s"`
	FetchTimeout          time.Duration `yaml:"fetch_timeout" env:"FETCH_TIMEOUT" default:"5s"`
	FetchInterval         time.Duration `yaml:"fetch_interval" env:"FETCH_INTERVAL" default:"1s"`
	Insecure              bool          `yaml:"insecure" env:"INSECURE" default:"false"`
	ReportMetric          bool          `yaml:"report_metric" env:"REPORT_METRIC" default:"false"`
	HealthUpdateFrequency int           `yaml:"health_update_frequency" env:"HEALTH_UPDATE_FREQUENCY" default:"5"`
	MetricUpdateFrequency int           `yaml:"metric_update_frequency" env:"METRIC_UPDATE_FREQUENCY" default:"10"`
	PurgeRepoLockInterval time.Duration `yaml:"purge_repo_lock_interval" env:"PURGE_REPO_LOCK_INTERVAL" default:"1h"`
}

type NFSCache struct {
	Server   string `yaml:"server" env:"NFS_SERVER" default:"nas.makeblock.com"`
	Port     int    `yaml:"port" env:"NFS_PORT" default:"2049"`
	Path     string `yaml:"path" env:"NFS_PATH" default:"/volume1/pipeline"`
	ReadOnly bool   `yaml:"read_only" env:"READ_ONLY" default:"false"`
}

type CacheConfig struct {
	Type     string   `yaml:"type" env:"TYPE" default:"nfs"`
	NFSCache NFSCache `yaml:"nfs"`
}

// Kubernetes represents the configuration for the kubernetes runtime.
type Kubernetes struct {
	KubeConfig                string `yaml:"kube_config" env:"KUBE_CONFIG" default:"~/.runner/.kube/config"`
	Namespace                 string `yaml:"namespace" env:"NAMESPACE" default:"pipeline"` // Namespace is the namespace where the runner runs.
	WaitForPodRunningAttempts int    `yaml:"wait_for_pod_running_attempts" env:"WAIT_FOR_POD_RUNNING_ATTEMPTS" default:"180"`
	WaitForPodRunningInterval int    `yaml:"wait_for_pod_running_interval" env:"WAIT_FOR_POD_RUNNING_INTERVAL" default:"3"`
	// image
	HelperImage string `yaml:"helper_image" env:"HELPER_IMAGE" default:"mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/runner-helper:latest"`
	DinDImage   string `yaml:"dind_image" env:"DIND_IMAGE" default:"mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/docker:27-dind"`
	// requests, limits
	HelperLimits   string `yaml:"helper_limits" env:"HELPER_LIMITS" default:"cpu:100m,memory:512Mi"`
	HelperRequests string `yaml:"helper_requests" env:"HELPER_REQUESTS" default:"cpu:100m,memory:128Mi"`
	BuildRequests  string `yaml:"build_requests" env:"BUILD_REQUESTS" default:"cpu:100m,memory:256Mi"`
	BuildLimits    string `yaml:"build_limits" env:"BUILD_LIMITS" default:"cpu:1,memory:2Gi"`
	// cache
	Cache CacheConfig `yaml:"cache"`
	// affinity
	Affinity KubernetesAffinity `yaml:"affinity"`
	// job pod labels
	PodLabels map[string]string `yaml:"pod_labels" env:"POD_LABELS"`
	// A yaml table/json object of key:value. Value is expected to be a string. When set this will create pods on k8s nodes that match all the key:value pairs. Only one selector is supported through environment variable configuration.
	NodeSelector map[string]string `yaml:"node_selector" env:"KUBERNETES_NODE_SELECTOR"`
	// A yaml table/json object of key=value:effect. Value and effect are expected to be strings. When set, pods will tolerate the given taints. Only one toleration is supported through environment variable configuration.
	NodeToleration map[string]string `yaml:"node_toleration" env:"KUBERNETES_NODE_TOLERATION"`
}

// LoadRunnerConfig load configurations
func LoadRunnerConfig(path *string) {
	if path == nil {
		return
	}
	conf := &configor.Config{ENVPrefix: "-"}
	if err := configor.New(conf).Load(&runnerConfig, *path); err != nil {
		panic(err)
	}
}

// Registration is the registration information for a runner
type Registration struct {
	Warning string   `json:"WARNING"` // Warning message to display, it's always the registrationWarning constant
	ID      int64    `json:"id"`
	UUID    string   `json:"uuid"`
	Name    string   `json:"name"`
	Token   string   `json:"token"`
	Address string   `json:"address"`
	Labels  []string `json:"labels"`
}

func RegistrationPath(cfg RunnerConfig) (string, error) {
	return util.RemoveHomeDir(filepath.Join(cfg.Workspace, cfg.Runner.File))
}

func LoadRegistration(cfg RunnerConfig) (*Registration, error) {
	regFilePath, err := RegistrationPath(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to get runner config file path: %w", err)
	}
	f, err := os.Open(regFilePath)
	// 如果文件不存在，尝试在当前目录下查找
	if os.IsNotExist(err) {
		log.Info("runner registration file not found, try to find it in current directory")
		f, err = os.Open(cfg.Runner.File)
	}
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var reg Registration
	if err = json.NewDecoder(f).Decode(&reg); err != nil {
		return nil, err
	}

	reg.Warning = ""

	return &reg, nil
}

func SaveRegistration(cfg RunnerConfig, reg *Registration) error {
	regFilePath, err := RegistrationPath(cfg)
	if err != nil {
		return fmt.Errorf("failed to get runner config file path: %w", err)
	}
	// Ensure the directory exists
	dir := filepath.Dir(regFilePath)
	if _, err = os.Stat(dir); os.IsNotExist(err) {
		if err = os.MkdirAll(dir, 0o755); err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}
	}
	// Create the file
	f, err := os.Create(regFilePath)
	if err != nil {
		return err
	}
	defer f.Close()
	reg.Warning = WARNING
	enc := json.NewEncoder(f)
	enc.SetIndent("", "  ")
	return enc.Encode(reg)
}

type Update struct {
	ApiUrl    string        `yaml:"runner_version_api_url" env:"RUNNER_VERSION_API_URL" default:"https://pipeline.makeblock.com/api/v1/pipeline/runner/version"` // URL to check for updates
	Frequency time.Duration `yaml:"fetch_frequency" env:"FETCH_FREQUENCY" default:"1m"`                                                                          // check for updates every Frequency
	Disable   bool          `yaml:"disable_self_update" env:"DISABLE_SELF_UPDATE" default:"false"`                                                               // disable self update
}

type NodeSelectorRequirement struct {
	Key      string   `yaml:"key,omitempty" json:"key"`
	Operator string   `yaml:"operator,omitempty" json:"operator"`
	Values   []string `yaml:"values,omitempty" json:"values,omitempty"`
}

type NodeSelectorTerm struct {
	MatchExpressions []NodeSelectorRequirement `yaml:"match_expressions,omitempty" json:"match_expressions,omitempty"`
	MatchFields      []NodeSelectorRequirement `yaml:"match_fields,omitempty" json:"match_fields,omitempty"`
}

type NodeSelector struct {
	NodeSelectorTerms []NodeSelectorTerm `yaml:"node_selector_terms" json:"node_selector_terms,omitempty"`
}

type KubernetesAffinity struct {
	NodeAffinity    *KubernetesNodeAffinity    `yaml:"node_affinity,omitempty" json:"node_affinity,omitempty" long:"node-affinity" description:"Node affinity is conceptually similar to nodeSelector -- it allows you to constrain which nodes your pod is eligible to be scheduled on, based on labels on the node."`
	PodAffinity     *KubernetesPodAffinity     `yaml:"pod_affinity,omitempty" json:"pod_affinity,omitempty" description:"Pod affinity allows to constrain which nodes your pod is eligible to be scheduled on based on the labels on other pods."`
	PodAntiAffinity *KubernetesPodAntiAffinity `yaml:"pod_anti_affinity,omitempty" json:"pod_anti_affinity,omitempty" description:"Pod anti-affinity allows to constrain which nodes your pod is eligible to be scheduled on based on the labels on other pods."`
}

type PreferredSchedulingTerm struct {
	Weight     int32            `yaml:"weight" json:"weight"`
	Preference NodeSelectorTerm `yaml:"preference" json:"preference"`
}

type KubernetesNodeAffinity struct {
	RequiredDuringSchedulingIgnoredDuringExecution  *NodeSelector             `yaml:"required_during_scheduling_ignored_during_execution,omitempty" json:"required_during_scheduling_ignored_during_execution,omitempty"`
	PreferredDuringSchedulingIgnoredDuringExecution []PreferredSchedulingTerm `yaml:"preferred_during_scheduling_ignored_during_execution,omitempty" json:"preferred_during_scheduling_ignored_during_execution,omitempty"`
}

type PodAffinityTerm struct {
	LabelSelector     *LabelSelector `yaml:"label_selector,omitempty" json:"label_selector,omitempty"`
	Namespaces        []string       `yaml:"namespaces,omitempty" json:"namespaces,omitempty"`
	TopologyKey       string         `yaml:"topology_key,omitempty" json:"topology_key"`
	NamespaceSelector *LabelSelector `yaml:"namespace_selector,omitempty" json:"namespace_selector,omitempty"`
}

type LabelSelector struct {
	MatchLabels      map[string]string         `yaml:"match_labels,omitempty" json:"match_labels,omitempty"`
	MatchExpressions []NodeSelectorRequirement `yaml:"match_expressions,omitempty" json:"match_expressions,omitempty"`
}

type WeightedPodAffinityTerm struct {
	Weight          int32           `yaml:"weight" json:"weight"`
	PodAffinityTerm PodAffinityTerm `yaml:"pod_affinity_term" json:"pod_affinity_term"`
}

type KubernetesPodAffinity struct {
	RequiredDuringSchedulingIgnoredDuringExecution  []PodAffinityTerm         `yaml:"required_during_scheduling_ignored_during_execution,omitempty" json:"required_during_scheduling_ignored_during_execution,omitempty"`
	PreferredDuringSchedulingIgnoredDuringExecution []WeightedPodAffinityTerm `yaml:"preferred_during_scheduling_ignored_during_execution,omitempty" json:"preferred_during_scheduling_ignored_during_execution,omitempty"`
}

type KubernetesPodAntiAffinity struct {
	RequiredDuringSchedulingIgnoredDuringExecution  []PodAffinityTerm         `yaml:"required_during_scheduling_ignored_during_execution,omitempty" json:"required_during_scheduling_ignored_during_execution,omitempty"`
	PreferredDuringSchedulingIgnoredDuringExecution []WeightedPodAffinityTerm `yaml:"preferred_during_scheduling_ignored_during_execution,omitempty" json:"preferred_during_scheduling_ignored_during_execution,omitempty"`
}

func (c *NodeSelectorRequirement) GetNodeSelectorRequirement() api.NodeSelectorRequirement {
	return api.NodeSelectorRequirement{
		Key:      c.Key,
		Operator: api.NodeSelectorOperator(c.Operator),
		Values:   c.Values,
	}
}

func (c *NodeSelectorTerm) GetNodeSelectorTerm() api.NodeSelectorTerm {
	nodeSelectorTerm := api.NodeSelectorTerm{}
	for _, expression := range c.MatchExpressions {
		nodeSelectorTerm.MatchExpressions = append(
			nodeSelectorTerm.MatchExpressions,
			expression.GetNodeSelectorRequirement(),
		)
	}
	for _, fields := range c.MatchFields {
		nodeSelectorTerm.MatchFields = append(
			nodeSelectorTerm.MatchFields,
			fields.GetNodeSelectorRequirement(),
		)
	}

	return nodeSelectorTerm
}

func (c *NodeSelector) GetNodeSelector() *api.NodeSelector {
	var nodeSelector api.NodeSelector
	for _, selector := range c.NodeSelectorTerms {
		nodeSelector.NodeSelectorTerms = append(nodeSelector.NodeSelectorTerms, selector.GetNodeSelectorTerm())
	}
	return &nodeSelector
}

func (c *PreferredSchedulingTerm) GetPreferredSchedulingTerm() api.PreferredSchedulingTerm {
	return api.PreferredSchedulingTerm{
		Weight:     c.Weight,
		Preference: c.Preference.GetNodeSelectorTerm(),
	}
}

func (c *Kubernetes) GetNodeAffinity() *api.NodeAffinity {
	var nodeAffinity api.NodeAffinity
	if c.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution != nil {
		nodeSelector := c.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.GetNodeSelector()
		nodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution = nodeSelector
	}
	for _, preferred := range c.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution {
		nodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution = append(
			nodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution,
			preferred.GetPreferredSchedulingTerm(),
		)
	}
	return &nodeAffinity
}

func (c *LabelSelector) GetLabelSelectorMatchExpressions() []metav1.LabelSelectorRequirement {
	var labelSelectorRequirement []metav1.LabelSelectorRequirement
	for _, label := range c.MatchExpressions {
		expression := metav1.LabelSelectorRequirement{
			Key:      label.Key,
			Operator: metav1.LabelSelectorOperator(label.Operator),
			Values:   label.Values,
		}
		labelSelectorRequirement = append(labelSelectorRequirement, expression)
	}
	return labelSelectorRequirement
}

func (c *PodAffinityTerm) GetLabelSelector() *metav1.LabelSelector {
	if c.LabelSelector == nil {
		return nil
	}

	return &metav1.LabelSelector{
		MatchLabels:      c.LabelSelector.MatchLabels,
		MatchExpressions: c.LabelSelector.GetLabelSelectorMatchExpressions(),
	}
}

func (c *PodAffinityTerm) GetNamespaceSelector() *metav1.LabelSelector {
	if c.NamespaceSelector == nil {
		return nil
	}

	return &metav1.LabelSelector{
		MatchLabels:      c.NamespaceSelector.MatchLabels,
		MatchExpressions: c.NamespaceSelector.GetLabelSelectorMatchExpressions(),
	}
}

func (c *PodAffinityTerm) GetPodAffinityTerm() api.PodAffinityTerm {
	return api.PodAffinityTerm{
		LabelSelector:     c.GetLabelSelector(),
		Namespaces:        c.Namespaces,
		TopologyKey:       c.TopologyKey,
		NamespaceSelector: c.GetNamespaceSelector(),
	}
}

func (c *WeightedPodAffinityTerm) GetWeightedPodAffinityTerm() api.WeightedPodAffinityTerm {
	return api.WeightedPodAffinityTerm{
		Weight:          c.Weight,
		PodAffinityTerm: c.PodAffinityTerm.GetPodAffinityTerm(),
	}
}

func (c *Kubernetes) GetPodAffinity() *api.PodAffinity {
	var podAffinity api.PodAffinity
	for _, required := range c.Affinity.PodAffinity.RequiredDuringSchedulingIgnoredDuringExecution {
		podAffinity.RequiredDuringSchedulingIgnoredDuringExecution = append(
			podAffinity.RequiredDuringSchedulingIgnoredDuringExecution,
			required.GetPodAffinityTerm(),
		)
	}
	for _, preferred := range c.Affinity.PodAffinity.PreferredDuringSchedulingIgnoredDuringExecution {
		podAffinity.PreferredDuringSchedulingIgnoredDuringExecution = append(
			podAffinity.PreferredDuringSchedulingIgnoredDuringExecution,
			preferred.GetWeightedPodAffinityTerm(),
		)
	}
	return &podAffinity
}

func (c *Kubernetes) GetPodAntiAffinity() *api.PodAntiAffinity {
	var podAntiAffinity api.PodAntiAffinity
	for _, required := range c.Affinity.PodAntiAffinity.RequiredDuringSchedulingIgnoredDuringExecution {
		podAntiAffinity.RequiredDuringSchedulingIgnoredDuringExecution = append(
			podAntiAffinity.RequiredDuringSchedulingIgnoredDuringExecution,
			required.GetPodAffinityTerm(),
		)
	}
	for _, preferred := range c.Affinity.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution {
		podAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution = append(
			podAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution,
			preferred.GetWeightedPodAffinityTerm(),
		)
	}
	return &podAntiAffinity
}

func (c *Kubernetes) GetAffinity() *api.Affinity {
	var affinity api.Affinity
	if c.Affinity.NodeAffinity != nil {
		affinity.NodeAffinity = c.GetNodeAffinity()
	}
	if c.Affinity.PodAffinity != nil {
		affinity.PodAffinity = c.GetPodAffinity()
	}
	if c.Affinity.PodAntiAffinity != nil {
		affinity.PodAntiAffinity = c.GetPodAntiAffinity()
	}
	return &affinity
}
