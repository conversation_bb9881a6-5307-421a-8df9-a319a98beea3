# Windows 文件删除问题解决方案

## 问题描述

在 Windows 平台上，清理工作空间时经常遇到以下错误：

```
failed to delete folder: remove C:\Users\<USER>\.runner\run\atomm\1301\73\EXEC_SHELL\1-0-0\atomm\node_modules\.pnpm\@esbuild+win32-x64@0.25.5\node_modules\@esbuild\win32-x64\esbuild.exe: Access is denied.
```

## 问题根因

1. **文件被进程锁定**：`esbuild.exe` 等可执行文件可能仍被正在运行的进程使用
2. **Windows 文件句柄管理**：Windows 平台的文件句柄释放机制与 Unix 系统不同
3. **进程终止不完整**：子进程可能没有被正确终止，导致文件句柄仍然被占用

## 解决方案

### 1. 增强的 DeleteFolder 函数

修改了 `pkg/util/file.go` 中的 `DeleteFolder` 函数，添加了 Windows 特定的处理逻辑：

```go
func DeleteFolder(path string) error {
	err := os.RemoveAll(path)
	if err != nil {
		// On Windows, try enhanced deletion if normal deletion fails
		if runtime.GOOS == "windows" {
			return deleteWithWindowsEnhancement(path, err)
		}
		return fmt.Errorf("failed to delete folder: %v", err)
	}
	return nil
}
```

### 2. Windows 增强删除机制

实现了 `deleteWithWindowsEnhancement` 函数，包含以下策略：

1. **延迟重试**：等待 100ms 后重试正常删除
2. **命令行强制删除**：使用 Windows `rmdir` 命令强制删除
3. **进程终止**：识别并终止可能锁定文件的进程
4. **多重尝试**：组合使用多种方法确保删除成功

### 3. 强制删除命令

```go
func forceDeleteWithCmd(path string) error {
	// Use rmdir with /s (remove subdirectories) and /q (quiet mode) flags
	cmd := exec.Command("cmd", "/c", "rmdir", "/s", "/q", path)
	if err := cmd.Run(); err != nil {
		// Try with rd command as alternative
		cmd = exec.Command("cmd", "/c", "rd", "/s", "/q", path)
		return cmd.Run()
	}
	return nil
}
```

### 4. 进程终止机制

```go
func killProcessesLockingPath(path string) error {
	// Try to use handle.exe from Sysinternals if available
	if err := killWithHandle(path); err == nil {
		return nil
	}
	
	// Fallback: kill common processes that might lock files
	commonProcesses := []string{"esbuild.exe", "node.exe", "npm.exe", "pnpm.exe", "yarn.exe"}
	for _, process := range commonProcesses {
		killProcess(process)
	}
	
	return nil
}
```

## 使用场景

这个解决方案主要应用于以下场景：

1. **CI/CD 流水线清理**：`internal/runner/step/action/clean.go`
2. **工作空间清理**：`internal/runner/poll/poller.go` 中的 `purgeTask`
3. **任何需要删除可能被锁定文件的场景**

## 优势

1. **向后兼容**：对非 Windows 平台保持原有行为
2. **渐进式处理**：从简单到复杂的多层次尝试
3. **错误处理**：保留原始错误信息，便于调试
4. **进程安全**：避免强制终止重要系统进程

## 注意事项

1. **权限要求**：某些操作可能需要管理员权限
2. **进程识别**：当前实现针对常见的前端构建工具进程
3. **性能影响**：增强删除会增加一定的延迟
4. **测试建议**：建议在 Windows 环境下进行充分测试

## 测试

创建了专门的 Windows 测试文件 `pkg/util/file_windows_test.go`，包含：

- 基本删除功能测试
- 增强删除机制测试
- 强制删除命令测试

## 相关文件

- `pkg/util/file.go` - 主要实现
- `pkg/util/file_windows_test.go` - Windows 特定测试
- `internal/runner/step/action/clean.go` - 使用场景
- `internal/runner/poll/poller.go` - 使用场景
