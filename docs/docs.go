// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/packages/add": {
            "post": {
                "description": "新增一个制品",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "新增制品",
                "parameters": [
                    {
                        "description": "请求体参数，包含制品信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/artifact.AddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "新增成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/delete": {
            "delete": {
                "description": "根据UUID批量删除制品",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "删除制品",
                "parameters": [
                    {
                        "description": "请求体参数，包含待删除制品UUID列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/download/{uuid}": {
            "get": {
                "description": "根据UUID下载制品文件",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "下载制品",
                "parameters": [
                    {
                        "type": "string",
                        "description": "制品UUID",
                        "name": "uuid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "下载文件流",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/info": {
            "post": {
                "description": "根据UUID获取制品详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "获取制品信息",
                "parameters": [
                    {
                        "description": "请求体参数，包含制品UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.InfoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回制品信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/artifact.InfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/list": {
            "post": {
                "description": "获取所有制品列表（不分页）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "获取制品列表",
                "parameters": [
                    {
                        "description": "请求体参数，包含筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/artifact.ListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回制品列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/page": {
            "post": {
                "description": "分页获取制品列表，支持多条件筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "分页查询制品",
                "parameters": [
                    {
                        "description": "请求体参数，包含分页及筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/artifact.PageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回分页制品列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/update": {
            "put": {
                "description": "更新制品信息或版本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "更新制品",
                "parameters": [
                    {
                        "description": "请求体参数，包含更新信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/artifact.UpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/version": {
            "post": {
                "description": "获取指定UUID的制品版本详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "获取制品版本详情",
                "parameters": [
                    {
                        "description": "请求体参数，包含制品版本UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.InfoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回版本详情",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/version/delete": {
            "delete": {
                "description": "根据UUID批量删除制品版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "删除制品版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含待删除版本UUID列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/version/page": {
            "post": {
                "description": "分页获取指定制品的版本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "分页获取制品版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含分页及筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/artifact.VersionsPageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回版本分页信息",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/packages/versions": {
            "post": {
                "description": "获取指定制品的所有版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "获取制品版本列表",
                "parameters": [
                    {
                        "description": "请求体参数，包含制品UUID等",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/artifact.VersionsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回版本列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/add": {
            "post": {
                "description": "新增一个凭证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "添加凭证",
                "parameters": [
                    {
                        "description": "请求体参数，包含凭证信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/credential.AddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "新增成功，返回凭证ID",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/delete": {
            "delete": {
                "description": "根据UUID批量删除凭证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "删除凭证",
                "parameters": [
                    {
                        "description": "请求体参数，包含待删除凭证UUID列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/info": {
            "post": {
                "description": "根据UUID获取凭证详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "获取凭证详情",
                "parameters": [
                    {
                        "description": "请求体参数，包含凭证UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.InfoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回凭证信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/credential.InfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/levels": {
            "get": {
                "description": "获取凭证等级列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "获取凭证等级",
                "responses": {
                    "200": {
                        "description": "成功返回凭证等级",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/credential.CredentialLevelResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/page": {
            "post": {
                "description": "分页获取凭证列表，支持多条件筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "分页查询凭证",
                "parameters": [
                    {
                        "description": "请求体参数，包含分页及筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/credential.PageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回分页凭证列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageModel"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/select": {
            "post": {
                "description": "获取凭证类型，提供给前端动态下拉框使用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "获取凭证类型（下拉框）",
                "parameters": [
                    {
                        "description": "请求体参数，包含筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/credential.SelectRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回凭证下拉列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/credential.SelectResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/types": {
            "get": {
                "description": "获取所有凭证类型对象数组",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "获取凭证类型对象数组",
                "responses": {
                    "200": {
                        "description": "成功返回凭证类型数组",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/credential.TypeResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/credential/update": {
            "put": {
                "description": "更新凭证信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "凭证管理"
                ],
                "summary": "更新凭证",
                "parameters": [
                    {
                        "description": "请求体参数，包含更新信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/credential.UpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功，返回影响行数",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/delete": {
            "delete": {
                "description": "根据UUID批量删除Runner",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "删除Runner",
                "parameters": [
                    {
                        "description": "请求体参数，包含待删除Runner UUID列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/info": {
            "post": {
                "description": "根据UUID获取Runner详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "获取Runner详情",
                "parameters": [
                    {
                        "description": "请求体参数，包含Runner UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.InfoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回Runner信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/runner.InfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/list": {
            "post": {
                "description": "获取所有Runner列表（不分页）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "获取Runner列表",
                "parameters": [
                    {
                        "description": "请求体参数，包含筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.ListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回Runner列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/metrics": {
            "post": {
                "description": "查询Runner的资源监控数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "查询Runner监控指标",
                "parameters": [
                    {
                        "description": "请求体参数，包含监控参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.MetricRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回监控数据",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/online": {
            "post": {
                "description": "获取当前在线的Runner标签列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "获取在线Runner标签",
                "parameters": [
                    {
                        "description": "请求体参数，包含筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.ListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回在线Runner标签列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/page": {
            "post": {
                "description": "分页获取Runner列表，支持多条件筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "分页查询Runner",
                "parameters": [
                    {
                        "description": "请求体参数，包含分页及筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.PageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回分页Runner列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/tasks": {
            "post": {
                "description": "查询Runner正在运行的任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "查询Runner正在运行的任务",
                "parameters": [
                    {
                        "description": "请求体参数，包含筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.TasksRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回任务列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/token": {
            "post": {
                "description": "新增Runner注册Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "新增Runner注册Token",
                "parameters": [
                    {
                        "description": "请求体参数，包含Token信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.AddRunnerRegisterTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "新增成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/update": {
            "put": {
                "description": "更新Runner信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "更新Runner",
                "parameters": [
                    {
                        "description": "请求体参数，包含更新信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.UpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version": {
            "post": {
                "description": "获取Runner自升级的最新版本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "获取Runner最新版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含Runner UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/updater.RunnerVersionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回版本信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/updater.RunnerVersionResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version/add": {
            "post": {
                "description": "新增Runner版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "新增Runner版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含版本信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.AddVersionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "新增成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version/batch": {
            "post": {
                "description": "批量更新Runner的版本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "批量更新Runner版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含批量更新参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.BatchUpdateRunnerVersionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "批量更新成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version/delete": {
            "delete": {
                "description": "根据UUID批量删除Runner版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "删除Runner版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含待删除版本UUID列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version/info": {
            "post": {
                "description": "查询Runner版本详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "查询Runner版本信息",
                "parameters": [
                    {
                        "description": "请求体参数，包含版本UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.InfoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回版本详情",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version/list": {
            "post": {
                "description": "查询Runner可用版本列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "查询Runner可用版本列表",
                "parameters": [
                    {
                        "description": "请求体参数，包含筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.VersionListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回可用版本列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version/page": {
            "post": {
                "description": "分页获取Runner版本列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "分页查询Runner版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含分页及筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.VersionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回版本分页信息",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/pipeline/runner/version/update": {
            "put": {
                "description": "更新Runner的版本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Runner管理"
                ],
                "summary": "更新Runner版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含版本信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/runner.UpdateVersionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/add": {
            "post": {
                "description": "新增一个应用，支持选择模板并可选下载代码包",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "新增应用",
                "parameters": [
                    {
                        "description": "请求体参数，包含应用信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.AppAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回，若Download为true则返回文件流",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/branches": {
            "post": {
                "description": "获取指定应用的分支列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "获取应用分支列表",
                "parameters": [
                    {
                        "description": "请求体参数，包含应用UUID等",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.BranchesRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回分支列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/branchesOrTags": {
            "post": {
                "description": "获取指定应用的分支或标签列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "获取分支或标签",
                "parameters": [
                    {
                        "description": "请求体参数，包含应用UUID等",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.BranchesRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回分支或标签列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/config/approval": {
            "post": {
                "description": "审批应用的配置项",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用配置"
                ],
                "summary": "审批配置",
                "parameters": [
                    {
                        "description": "请求体参数，包含审批信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/plugins.ConfigApprovalRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "审批成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/config/edit": {
            "post": {
                "description": "获取配置项的编辑版本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用配置"
                ],
                "summary": "获取配置项编辑版本",
                "parameters": [
                    {
                        "description": "请求体参数，包含配置UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.ReviewRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回编辑版本信息",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/config/entry": {
            "post": {
                "description": "获取配置项的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用配置"
                ],
                "summary": "获取配置项详情",
                "parameters": [
                    {
                        "description": "请求体参数，包含配置UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.ReviewRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回配置项详情",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/config/history": {
            "post": {
                "description": "获取应用配置的历史版本列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用配置"
                ],
                "summary": "获取配置历史",
                "parameters": [
                    {
                        "description": "请求体参数，包含配置UUID等",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/plugins.ConfigHistoryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回历史版本列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/config/review": {
            "post": {
                "description": "催促管理员审批配置项",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用配置"
                ],
                "summary": "催审配置",
                "parameters": [
                    {
                        "description": "请求体参数，包含配置UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.ReviewRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "催审成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/config/select": {
            "post": {
                "description": "获取应用配置的下拉选项数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用配置"
                ],
                "summary": "获取下拉选项",
                "parameters": [
                    {
                        "description": "请求体参数，包含应用UUID和配置Key",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/plugins.ConfigSelectRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回下拉选项",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/config/version": {
            "post": {
                "description": "获取指定配置的版本详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用配置"
                ],
                "summary": "获取配置版本详情",
                "parameters": [
                    {
                        "description": "请求体参数，包含配置UUID和版本号",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/plugins.ConfigVersionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回版本详情",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/delete": {
            "delete": {
                "description": "根据UUID批量删除应用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "删除应用",
                "parameters": [
                    {
                        "description": "请求体参数，包含待删除应用UUID列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功删除",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/import": {
            "post": {
                "description": "导入已有应用信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "导入应用",
                "parameters": [
                    {
                        "description": "请求体参数，包含导入应用信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.ImportRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "导入成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/info": {
            "post": {
                "description": "根据指定的 UUID 获取应用的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "获取应用信息",
                "parameters": [
                    {
                        "description": "请求体参数，包含应用 UUID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.InfoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回应用详细信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/app.InfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/info/{uuid}": {
            "get": {
                "description": "根据日志UUID获取对应的应用详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "通过日志UUID获取应用信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "日志UUID",
                        "name": "uuid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回应用详细信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/app.InfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/list": {
            "post": {
                "description": "获取所有应用列表（不分页）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "获取应用列表",
                "parameters": [
                    {
                        "description": "请求体参数，包含筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.ListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回应用列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageModel"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/page": {
            "post": {
                "description": "分页获取应用列表，支持多条件筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "分页查询应用",
                "parameters": [
                    {
                        "description": "请求体参数，包含分页及筛选条件",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.PageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回分页应用列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageModel"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/parameters": {
            "post": {
                "description": "获取指定应用的参数信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "获取应用参数",
                "parameters": [
                    {
                        "description": "请求体参数，包含应用UUID等",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.ParametersRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回参数信息",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/request": {
            "post": {
                "description": "用户申请加入指定应用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "申请加入应用",
                "parameters": [
                    {
                        "description": "请求体参数，包含申请信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.RequestJoinRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "申请成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/templates": {
            "get": {
                "description": "获取所有可用的应用模板",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "获取应用模板列表",
                "responses": {
                    "200": {
                        "description": "成功返回模板列表",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/templates/refresh": {
            "get": {
                "description": "刷新远程应用模板到本地",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "刷新应用模板",
                "responses": {
                    "200": {
                        "description": "刷新成功",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/api/v1/project/app/update": {
            "put": {
                "description": "更新应用的基本信息和配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "应用管理"
                ],
                "summary": "更新应用",
                "parameters": [
                    {
                        "description": "请求体参数，包含应用更新信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/app.UpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回更新后的应用信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.APIModel"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/app.InfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/assets/{path}": {
            "get": {
                "description": "获取制品相关静态资源（如测试报告等）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/html"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "获取静态资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "资源路径",
                        "name": "path",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "资源文件流",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "404": {
                        "description": "资源不存在",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        },
        "/packages/download/{app}/{name}/{version}": {
            "get": {
                "description": "通过应用标识、制品名、版本号下载制品",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "制品管理"
                ],
                "summary": "OpenAPI下载制品",
                "parameters": [
                    {
                        "type": "string",
                        "description": "应用标识",
                        "name": "app",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "制品名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本号",
                        "name": "version",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "下载文件流",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.APIModel"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "app.ImportRequest": {
            "type": "object",
            "properties": {
                "category": {
                    "description": "应用分类",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "identity": {
                    "description": "标识",
                    "type": "string"
                },
                "level": {
                    "description": "应用等级",
                    "type": "string"
                },
                "members": {
                    "description": "参与人uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "namespaceId": {
                    "description": "所属项目",
                    "type": "integer"
                },
                "principals": {
                    "description": "关联用户",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "projectUuid": {
                    "description": "所属项目",
                    "type": "string"
                },
                "repoId": {
                    "type": "integer"
                },
                "source": {
                    "description": "代码源",
                    "type": "string"
                }
            }
        },
        "app.InfoResponse": {
            "type": "object",
            "properties": {
                "admin": {
                    "type": "boolean"
                },
                "category": {
                    "description": "应用分类",
                    "type": "string"
                },
                "configs": {
                    "description": "配置插件",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/plugins.ApplicationConfigPlugin"
                    }
                },
                "creator": {
                    "description": "创建人",
                    "type": "string"
                },
                "deleted": {
                    "description": "逻辑删除",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "gmtCreate": {
                    "description": "创建时间",
                    "type": "string"
                },
                "gmtModified": {
                    "description": "修改时间",
                    "type": "string"
                },
                "httpUrlToRepo": {
                    "description": "仓库地址",
                    "type": "string"
                },
                "id": {
                    "description": "主键",
                    "type": "integer"
                },
                "identity": {
                    "description": "标识",
                    "type": "string"
                },
                "level": {
                    "description": "应用等级",
                    "type": "string"
                },
                "members": {
                    "description": "绑定关系",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/app.RelationShipResponse"
                    }
                },
                "modifier": {
                    "description": "修改人",
                    "type": "string"
                },
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "principals": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/app.RelationShipResponse"
                    }
                },
                "project": {
                    "$ref": "#/definitions/app.ProjectResponse"
                },
                "projectUuid": {
                    "description": "所属项目",
                    "type": "string"
                },
                "relationship": {
                    "description": "项目逻辑关系",
                    "type": "string"
                },
                "repoId": {
                    "description": "代码仓库id(一般api交互时用)",
                    "type": "integer"
                },
                "reviewer": {
                    "type": "boolean"
                },
                "sentryProjectId": {
                    "description": "sentry项目id",
                    "type": "integer"
                },
                "source": {
                    "description": "代码源",
                    "type": "string"
                },
                "uptimeKumaId": {
                    "description": "uptime kuma 监控项id",
                    "type": "integer"
                },
                "uuid": {
                    "description": "uuid",
                    "type": "string"
                }
            }
        },
        "app.ListRequest": {
            "type": "object",
            "properties": {
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                }
            }
        },
        "app.PageRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "identity": {
                    "description": "标识",
                    "type": "string"
                },
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "project": {
                    "description": "所属项目",
                    "type": "string"
                },
                "sort": {
                    "type": "string"
                },
                "source": {
                    "description": "代码源",
                    "type": "string"
                },
                "typeOf": {
                    "description": "类型",
                    "type": "string"
                }
            }
        },
        "app.ProjectResponse": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "app.RelationShipResponse": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string"
                },
                "typeOf": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "app.RequestJoinRequest": {
            "type": "object",
            "properties": {
                "appUUID": {
                    "type": "string"
                },
                "reason": {
                    "type": "string"
                },
                "role": {
                    "type": "string"
                }
            }
        },
        "app.ReviewRequest": {
            "type": "object",
            "properties": {
                "uuid": {
                    "type": "string"
                }
            }
        },
        "app.UpdateRequest": {
            "type": "object",
            "required": [
                "uuid"
            ],
            "properties": {
                "category": {
                    "description": "应用分类",
                    "type": "string"
                },
                "configs": {
                    "description": "应用配置",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/plugins.ApplicationConfigPlugin"
                    }
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "gmtModified": {
                    "type": "string"
                },
                "level": {
                    "description": "应用等级",
                    "type": "string"
                },
                "members": {
                    "description": "参与人uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "principals": {
                    "description": "关联用户",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "projectUuid": {
                    "description": "所属项目",
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "artifact.AddRequest": {
            "type": "object",
            "properties": {
                "category": {
                    "description": "应用分类",
                    "type": "string"
                },
                "createPipeline": {
                    "type": "boolean"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "download": {
                    "description": "是否下载",
                    "type": "boolean"
                },
                "identity": {
                    "description": "标识",
                    "type": "string"
                },
                "level": {
                    "description": "应用等级",
                    "type": "string"
                },
                "members": {
                    "description": "参与人uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "namespaceId": {
                    "description": "所属项目",
                    "type": "integer"
                },
                "param": {
                    "description": "渲染参数",
                    "type": "object",
                    "additionalProperties": {}
                },
                "principals": {
                    "description": "关联用户",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "projectUuid": {
                    "description": "所属项目",
                    "type": "string"
                },
                "source": {
                    "description": "代码源",
                    "type": "string"
                },
                "template": {
                    "description": "模板",
                    "type": "string"
                },
                "useAppTemplate": {
                    "description": "是否使用模板",
                    "type": "boolean"
                }
            }
        },
        "artifact.InfoResponse": {
            "type": "object",
            "properties": {
                "appUuid": {
                    "description": "所属应用",
                    "type": "string"
                },
                "creator": {
                    "description": "创建人",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "gmtCreate": {
                    "description": "创建时间",
                    "type": "string"
                },
                "gmtModified": {
                    "description": "修改时间",
                    "type": "string"
                },
                "latestVersion": {
                    "description": "最新版本",
                    "type": "string"
                },
                "maxVersions": {
                    "description": "最大保留版本数",
                    "type": "integer"
                },
                "modifier": {
                    "description": "备注",
                    "type": "string"
                },
                "name": {
                    "description": "应用级别唯一标识                      //  应用名称",
                    "type": "string"
                },
                "remark": {
                    "description": "修改人",
                    "type": "string"
                },
                "uuid": {
                    "description": "uuid",
                    "type": "string"
                },
                "versions": {
                    "description": "版本数",
                    "type": "integer"
                }
            }
        },
        "artifact.ListRequest": {
            "type": "object",
            "properties": {
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                }
            }
        },
        "artifact.PageRequest": {
            "type": "object",
            "properties": {
                "appUuid": {
                    "description": "标识",
                    "type": "string"
                },
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                }
            }
        },
        "artifact.UpdateRequest": {
            "type": "object",
            "required": [
                "uuid"
            ],
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "labels": {
                    "description": "所属项目",
                    "type": "string"
                },
                "maxVersions": {
                    "description": "针对制品的更新",
                    "type": "integer"
                },
                "uuid": {
                    "description": "针对制品版本的更新",
                    "type": "string"
                }
            }
        },
        "artifact.VersionsPageRequest": {
            "type": "object",
            "required": [
                "uuid"
            ],
            "properties": {
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                },
                "uuid": {
                    "description": "制品uuid",
                    "type": "string"
                }
            }
        },
        "artifact.VersionsRequest": {
            "type": "object",
            "required": [
                "uuid"
            ],
            "properties": {
                "name": {
                    "type": "string"
                },
                "size": {
                    "type": "integer"
                },
                "uuid": {
                    "description": "制品uuid",
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "credential.AddRequest": {
            "type": "object",
            "properties": {
                "categoryUUID": {
                    "description": "类别",
                    "type": "string"
                },
                "members": {
                    "description": "credential 共享者 uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "环境变量名",
                    "type": "string"
                },
                "principals": {
                    "description": "credential 创建者（管理者）uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "remark": {
                    "description": "描述",
                    "type": "string"
                },
                "scope": {
                    "description": "凭证级别(0:system|1:simple)",
                    "type": "integer"
                },
                "show": {
                    "type": "boolean"
                },
                "typeOf": {
                    "description": "类型",
                    "type": "string"
                },
                "value": {
                    "description": "环境变量值",
                    "type": "string"
                }
            }
        },
        "credential.CredentialLevelResponse": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string"
                },
                "value": {
                    "description": "凭证级别(0:system|1:simple)",
                    "type": "integer"
                }
            }
        },
        "credential.InfoResponse": {
            "type": "object",
            "properties": {
                "categoryUUID": {
                    "description": "类别",
                    "type": "string"
                },
                "creator": {
                    "description": "创建人",
                    "type": "string"
                },
                "deleted": {
                    "description": "逻辑删除",
                    "type": "integer"
                },
                "example": {
                    "description": "示例",
                    "type": "string"
                },
                "gmtCreate": {
                    "description": "创建时间",
                    "type": "string"
                },
                "gmtModified": {
                    "description": "修改时间",
                    "type": "string"
                },
                "id": {
                    "description": "主键",
                    "type": "integer"
                },
                "members": {
                    "description": "credential 共享者 uuid",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/credential.RelationShipResponse"
                    }
                },
                "modifier": {
                    "description": "修改人",
                    "type": "string"
                },
                "name": {
                    "description": "环境变量名",
                    "type": "string"
                },
                "principals": {
                    "description": "credential 创建者（管理者）uuid",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/credential.RelationShipResponse"
                    }
                },
                "remark": {
                    "description": "备注",
                    "type": "string"
                },
                "scope": {
                    "description": "凭证级别(0:system|1:simple)",
                    "type": "integer"
                },
                "show": {
                    "type": "boolean"
                },
                "typeOf": {
                    "description": "类型",
                    "type": "string"
                },
                "uuid": {
                    "description": "UUID",
                    "type": "string"
                },
                "value": {
                    "description": "环境变量值",
                    "type": "string"
                }
            }
        },
        "credential.PageRequest": {
            "type": "object",
            "properties": {
                "categoryUUID": {
                    "description": "类别",
                    "type": "string"
                },
                "name": {
                    "description": "环境变量名",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "scope": {
                    "description": "凭证级别(0:system|1:simple)",
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                },
                "typeOf": {
                    "description": "typeOf 后续不用",
                    "type": "string"
                },
                "value": {
                    "description": "环境变量值",
                    "type": "string"
                }
            }
        },
        "credential.RelationShipResponse": {
            "type": "object",
            "properties": {
                "label": {
                    "description": "唯一标识名称",
                    "type": "string"
                },
                "typeOf": {
                    "description": "角色类型",
                    "type": "string"
                },
                "value": {
                    "description": "唯一标识",
                    "type": "string"
                }
            }
        },
        "credential.SelectRequest": {
            "type": "object",
            "properties": {
                "categoryUUID": {
                    "description": "类别ID",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                },
                "value": {
                    "description": "select的value类型：uuid或者直接是value（如果是value类型则必须是show状态的）",
                    "allOf": [
                        {
                            "$ref": "#/definitions/credential.ValueType"
                        }
                    ]
                }
            }
        },
        "credential.SelectResponse": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string"
                },
                "value": {
                    "description": "环境变量值",
                    "type": "string"
                }
            }
        },
        "credential.TypeResponse": {
            "type": "object",
            "properties": {
                "categoryName": {
                    "description": "类别名称",
                    "type": "string"
                },
                "example": {
                    "type": "string"
                },
                "scope": {
                    "description": "凭证级别(0:system|1:simple)",
                    "type": "integer"
                },
                "typeOf": {
                    "description": "角色类型",
                    "type": "string"
                },
                "uuid": {
                    "description": "UUID",
                    "type": "string"
                }
            }
        },
        "credential.UpdateRequest": {
            "type": "object",
            "required": [
                "uuid"
            ],
            "properties": {
                "categoryUUID": {
                    "description": "类别",
                    "type": "string"
                },
                "members": {
                    "description": "credential 共享者 uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "环境变量名",
                    "type": "string"
                },
                "principals": {
                    "description": "credential 创建者（管理者）uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "remark": {
                    "description": "描述",
                    "type": "string"
                },
                "show": {
                    "type": "boolean"
                },
                "typeOf": {
                    "description": "类型",
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                },
                "value": {
                    "description": "环境变量值",
                    "type": "string"
                }
            }
        },
        "credential.ValueType": {
            "type": "string",
            "enum": [
                "uuid",
                "value"
            ],
            "x-enum-varnames": [
                "UUID",
                "Value"
            ]
        },
        "models.ApproveState": {
            "type": "string",
            "enum": [
                "pending",
                "passed",
                "rejected"
            ],
            "x-enum-varnames": [
                "ApproveStatePending",
                "ApproveStatePassed",
                "ApproveStateRejected"
            ]
        },
        "plugins.ApplicationConfigPlugin": {
            "type": "object",
            "properties": {
                "key": {
                    "description": "配置标识",
                    "type": "string"
                },
                "name": {
                    "description": "配置名称",
                    "type": "string"
                },
                "value": {
                    "description": "配置内容"
                }
            }
        },
        "plugins.ConfigApprovalRequest": {
            "type": "object",
            "properties": {
                "reason": {
                    "description": "理由",
                    "type": "string"
                },
                "state": {
                    "description": "状态(部分配置需要审批通过才能使用)",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ApproveState"
                        }
                    ]
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "plugins.ConfigHistoryRequest": {
            "type": "object",
            "properties": {
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "plugins.ConfigSelectRequest": {
            "type": "object",
            "properties": {
                "appUUID": {
                    "type": "string"
                },
                "key": {
                    "type": "string"
                }
            }
        },
        "plugins.ConfigVersionRequest": {
            "type": "object",
            "properties": {
                "configEntryUUID": {
                    "type": "string"
                },
                "version": {
                    "type": "integer"
                }
            }
        },
        "request.AddRunnerRegisterTokenRequest": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "request.AppAddRequest": {
            "type": "object",
            "properties": {
                "category": {
                    "description": "应用分类",
                    "type": "string"
                },
                "createPipeline": {
                    "type": "boolean"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "download": {
                    "description": "是否下载",
                    "type": "boolean"
                },
                "identity": {
                    "description": "标识",
                    "type": "string"
                },
                "level": {
                    "description": "应用等级",
                    "type": "string"
                },
                "members": {
                    "description": "参与人uuid",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "应用名称",
                    "type": "string"
                },
                "namespaceId": {
                    "description": "所属项目",
                    "type": "integer"
                },
                "param": {
                    "description": "渲染参数",
                    "type": "object",
                    "additionalProperties": {}
                },
                "principals": {
                    "description": "关联用户",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "projectUuid": {
                    "description": "所属项目",
                    "type": "string"
                },
                "source": {
                    "description": "代码源",
                    "type": "string"
                },
                "template": {
                    "description": "模板",
                    "type": "string"
                },
                "useAppTemplate": {
                    "description": "是否使用模板",
                    "type": "boolean"
                }
            }
        },
        "request.BranchesRequest": {
            "type": "object",
            "required": [
                "appUuid"
            ],
            "properties": {
                "appUuid": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "request.DeleteRequest": {
            "type": "object",
            "required": [
                "uuids"
            ],
            "properties": {
                "uuids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "request.InfoRequest": {
            "type": "object",
            "properties": {
                "uuid": {
                    "description": "统一替换id成uuid方式,防止id暴露",
                    "type": "string"
                }
            }
        },
        "request.MetricRequest": {
            "type": "object",
            "required": [
                "runnerUUID",
                "startTime",
                "stopTime"
            ],
            "properties": {
                "runnerUUID": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                },
                "stopTime": {
                    "type": "integer"
                }
            }
        },
        "request.ParametersRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "name": {
                    "type": "string"
                }
            }
        },
        "request.TasksRequest": {
            "type": "object",
            "properties": {
                "runnerUUID": {
                    "type": "string"
                }
            }
        },
        "response.APIModel": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/response.Code"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "response.Code": {
            "type": "integer",
            "enum": [
                0,
                10000,
                10001,
                10002,
                10003,
                20101,
                20102,
                90534,
                90535,
                90536,
                90537,
                90538,
                90539,
                90540,
                90541
            ],
            "x-enum-comments": {
                "CodeVersionConflict": "版本冲突错误码"
            },
            "x-enum-varnames": [
                "CodeOK",
                "CodeUnknown",
                "CodeDBConnect",
                "CodeDBOperation",
                "CodeRequestParam",
                "CodeTokenInvalid",
                "CodePermissionDenied",
                "TagNotFound",
                "BranchNotFound",
                "PipelineInvalid",
                "ConfigStateInvalid",
                "OperatePermissionDenied",
                "BranchQuery",
                "RepositoryAlreadyExist",
                "CodeVersionConflict"
            ]
        },
        "response.PageModel": {
            "type": "object",
            "properties": {
                "list": {},
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "runner.AddVersionRequest": {
            "type": "object",
            "properties": {
                "arch": {
                    "description": "架构",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "downloadUrl": {
                    "description": "下载链接",
                    "type": "string"
                },
                "os": {
                    "description": "系统",
                    "type": "string"
                },
                "released": {
                    "description": "发布",
                    "type": "boolean"
                },
                "version": {
                    "description": "版本",
                    "type": "string"
                }
            }
        },
        "runner.BatchUpdateRunnerVersionRequest": {
            "type": "object",
            "required": [
                "runnerUUIDList",
                "version"
            ],
            "properties": {
                "runnerUUIDList": {
                    "description": "版本",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "version": {
                    "description": "版本",
                    "type": "string"
                }
            }
        },
        "runner.InfoResponse": {
            "type": "object",
            "properties": {
                "allowedUserIds": {
                    "description": "允许访问的用户",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "concurrency": {
                    "description": "最大任务并发量",
                    "type": "integer"
                },
                "creator": {
                    "description": "创建人",
                    "type": "string"
                },
                "deleted": {
                    "description": "逻辑删除",
                    "type": "boolean"
                },
                "disable": {
                    "description": "是否启用",
                    "type": "boolean"
                },
                "enableOfflineNotification": {
                    "description": "是否启用离线通知",
                    "type": "boolean"
                },
                "endpoint": {
                    "description": "服务地址",
                    "type": "string"
                },
                "gmtCreate": {
                    "description": "创建时间",
                    "type": "string"
                },
                "gmtModified": {
                    "description": "修改时间",
                    "type": "string"
                },
                "id": {
                    "description": "主键",
                    "type": "integer"
                },
                "ip": {
                    "description": "IP",
                    "type": "string"
                },
                "isPublic": {
                    "description": "是否公开",
                    "type": "boolean"
                },
                "labels": {
                    "description": "标签",
                    "type": "string"
                },
                "modifier": {
                    "description": "修改人",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "offlineNotifyUserEmails": {
                    "description": "离线通知用户邮箱",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "remark": {
                    "description": "描述",
                    "type": "string"
                },
                "status": {
                    "description": "状态",
                    "type": "string"
                },
                "uuid": {
                    "description": "UUID",
                    "type": "string"
                },
                "version": {
                    "description": "状态",
                    "type": "string"
                },
                "versionUUID": {
                    "description": "绑定的版本",
                    "type": "string"
                }
            }
        },
        "runner.ListRequest": {
            "type": "object",
            "properties": {
                "checkOfflineNotify": {
                    "type": "boolean"
                },
                "disable": {
                    "description": "是否启用",
                    "type": "boolean"
                },
                "uuid": {
                    "description": "UUID",
                    "type": "string"
                },
                "uuidList": {
                    "description": "UUID列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "runner.PageRequest": {
            "type": "object",
            "properties": {
                "concurrency": {
                    "description": "最大任务并发量",
                    "type": "integer"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "endpoint": {
                    "description": "服务地址",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                }
            }
        },
        "runner.UpdateRequest": {
            "type": "object",
            "properties": {
                "allowedUserIds": {
                    "description": "允许访问的用户",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "disable": {
                    "description": "是否启用",
                    "type": "boolean"
                },
                "enableOfflineNotification": {
                    "description": "是否启用离线通知",
                    "type": "boolean"
                },
                "isPublic": {
                    "description": "是否公开",
                    "type": "boolean"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "offlineNotifyUserEmails": {
                    "description": "离线通知用户邮箱",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "remark": {
                    "description": "描述",
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                },
                "versionUUID": {
                    "description": "绑定的版本",
                    "type": "string"
                }
            }
        },
        "runner.UpdateVersionRequest": {
            "type": "object",
            "required": [
                "uuid"
            ],
            "properties": {
                "arch": {
                    "description": "架构",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "downloadUrl": {
                    "description": "下载链接",
                    "type": "string"
                },
                "os": {
                    "description": "系统",
                    "type": "string"
                },
                "released": {
                    "description": "发布",
                    "type": "boolean"
                },
                "uuid": {
                    "type": "string"
                },
                "version": {
                    "description": "版本",
                    "type": "string"
                }
            }
        },
        "runner.VersionListRequest": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "runnerUUID": {
                    "type": "string"
                },
                "sort": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "runner.VersionRequest": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "sort": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "updater.RunnerVersionRequest": {
            "type": "object",
            "properties": {
                "runnerUUID": {
                    "type": "string"
                }
            }
        },
        "updater.RunnerVersionResponse": {
            "type": "object",
            "properties": {
                "downloadURL": {
                    "type": "string"
                },
                "secretKey": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
