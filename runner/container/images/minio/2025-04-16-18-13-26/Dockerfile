# syntax=docker/dockerfile:1
FROM mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-ubuntu:22.04 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装各类SKD
ENV SDK_PATH=/usr/local/sdk

# 安装 kubectl
ENV MC_SDK_PATH=${SDK_PATH}/mc
RUN curl https://dl.min.io/client/mc/release/linux-${TARGETARCH}/mc --create-dirs -o ${MC_SDK_PATH}/mc && ls -alh ${MC_SDK_PATH}/mc && \
    chmod +x ${MC_SDK_PATH}/mc && \
    ${MC_SDK_PATH}/mc --version

# 设置环境变量
ENV PATH=$PATH:${MC_SDK_PATH}