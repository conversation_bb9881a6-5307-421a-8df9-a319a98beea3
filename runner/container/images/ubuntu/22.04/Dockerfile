# syntax=docker/dockerfile:1
# 使用Ubuntu作为基础镜像
FROM ubuntu:22.04 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log
# 设置时区
ENV TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive
## 更新Ubuntu的软件源
RUN apt-get update && apt-get upgrade -y
# 安装git
RUN apt-get install -y git wget curl make tzdata xz-utils unzip
# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone && dpkg-reconfigure --frontend noninteractive tzdata

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]