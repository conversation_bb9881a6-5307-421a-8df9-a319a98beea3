# syntax=docker/dockerfile:1
FROM mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-ubuntu:22.04 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装各类SKD
ENV SDK_PATH=/usr/local/sdk

# 安装 helm
ENV HELM_SDK_PATH=${SDK_PATH}/helm
ENV HELM_VERSION=3.17.3
# https://get.helm.sh/helm-v3.17.3-linux-amd64.tar.gz
RUN wget https://get.helm.sh/helm-v${HELM_VERSION}-linux-${ARRCH}.tar.gz -P /tmp && \
    mkdir -p ${HELM_SDK_PATH} && \
    tar -C ${HELM_SDK_PATH} -xzf /tmp/helm-v${HELM_VERSION}-linux-${ARRCH}.tar.gz --strip-components=1 && \
    chmod +x ${HELM_SDK_PATH}/helm
# 设置环境变量
ENV PATH=$PATH:${HELM_SDK_PATH}

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]