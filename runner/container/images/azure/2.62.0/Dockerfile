# syntax=docker/dockerfile:1
FROM mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-ubuntu:22.04 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# azure-cli
# https://learn.microsoft.com/en-us/cli/azure/install-azure-cli-linux?pivots=script
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash &&  az -v

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]