# Runner 执行器 运行任务 镜像


## 登录仓库

```bash
docker login mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com --username=makeblockcom --password=xxxx
``` 

## 创建构建器

```shell
docker buildx create --name mybuilder --driver docker-container
docker buildx use mybuilder
```

## 构建镜像

```
pipeline-context-ubuntu:22.04（顶层）
            |
pipeline-context-docker:latest（需要docker打包）
            |
pipeline-context-node、java......
```

```bash
cd ubuntu
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-ubuntu:22.04 \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd docker
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-docker:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd node
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-node:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd java
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-java:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd azure
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-azure:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd golang
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-golang:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd kubernetes
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-kubernetes:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd headless
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-headless:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd helm
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-helm:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
cd minio
docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-minio:latest \
  --platform linux/arm64,linux/amd64 \
  --push .
```

```bash
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-ubuntu:latest
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-docker:latest
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-node:latest
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-java:latest
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-azure:latest
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-golang:latest
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-python:latest
docker pull mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-kubernetes:latest
```

## 相关文档
1. https://docs.docker.com/build/guide/multi-platform/