# syntax=docker/dockerfile:1
FROM  mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-docker:27.3.1 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
# amd64, arm64, arm/v7, arm/v6
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装各类SKD
ENV SDK_PATH=/usr/local/sdk

ENV JDK_VERSION=17.0.2
ENV JDK_SDK_PATH=${SDK_PATH}/jdk
# install java, from https://jdk.java.net/archive/
RUN arch=""; \
    if [ "$ARRCH" = "amd64" ]; then \
        arch="x64"; \
    fi && \
    if [ "$ARRCH" = "arm64" ]; then \
        arch="aarch64"; \
    fi && \
    wget https://download.java.net/java/GA/jdk${JDK_VERSION}/dfd4a8d0985749f896bed50d7138ee7f/8/GPL/openjdk-${JDK_VERSION}_linux-${arch}_bin.tar.gz -P /tmp && \
    mkdir -p ${JDK_SDK_PATH} && \
    tar -C ${JDK_SDK_PATH}/ -xzf /tmp/openjdk-${JDK_VERSION}_linux-${arch}_bin.tar.gz --strip-components=1 && \
    rm /tmp/openjdk-${JDK_VERSION}_linux-${arch}_bin.tar.gz  && \
    chmod +x ${JDK_SDK_PATH}/bin
# 设置环境变量
ENV JAVA_HOME=${JDK_SDK_PATH}
ENV PATH=${JDK_SDK_PATH}/bin:$PATH

# install sonar-scanner
ENV SONARQUBE_VERSION=6.2.1.4610
ENV SONARQUBE_SDK_PATH=${SDK_PATH}/sonar
RUN arch=""; \
    if [ "$ARRCH" = "amd64" ]; then \
        arch="x64"; \
    fi && \
    if [ "$ARRCH" = "arm64" ]; then \
        arch="aarch64"; \
    fi && \
    wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${SONARQUBE_VERSION}-linux-${arch}.zip -P /tmp && \
    mkdir -p ${SONARQUBE_SDK_PATH} && \
    unzip /tmp/sonar-scanner-cli-${SONARQUBE_VERSION}-linux-${arch}.zip -d ${SONARQUBE_SDK_PATH} && \
    mv ${SONARQUBE_SDK_PATH}/sonar-scanner-${SONARQUBE_VERSION}-linux-${arch}/* ${SONARQUBE_SDK_PATH}/ && \
    chmod +x ${SONARQUBE_SDK_PATH}/bin && \
    rm /tmp/sonar-scanner-cli-${SONARQUBE_VERSION}-linux-${arch}.zip  && \
    rm -rf ${SONARQUBE_SDK_PATH}/sonar-scanner-${SONARQUBE_VERSION}-linux-${arch}
# 设置PATH
ENV PATH=${SONARQUBE_SDK_PATH}/bin:$PATH

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]