# syntax=docker/dockerfile:1
FROM  mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-node:24.0.0 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装 Cloudflare Wrangler CLI
ENV WRANGLER_VERSION=4.22.0
ENV WRANGLER_SDK_PATH=${SDK_PATH}/wrangler
RUN mkdir -p ${WRANGLER_SDK_PATH} && \
    cd ${WRANGLER_SDK_PATH} && \
    npm init -y && \
    npm install wrangler@${WRANGLER_VERSION} && \
    ln -s ${WRANGLER_SDK_PATH}/node_modules/.bin/wrangler /usr/local/bin/wrangler
# 设置 Wrangler 环境变量
ENV PATH=$PATH:${WRANGLER_SDK_PATH}/node_modules/.bin

# 验证安装
RUN node --version && npm --version && wrangler --version

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]
