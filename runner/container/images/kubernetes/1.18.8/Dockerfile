# syntax=docker/dockerfile:1
FROM mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-ubuntu:22.04 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装各类SKD
ENV SDK_PATH=/usr/local/sdk

# 安装 kubectl
ENV KUBECTL_SDK_PATH=${SDK_PATH}/kubectl
ENV KUBECTL_VERSION=1.18.8
RUN wget https://storage.googleapis.com/kubernetes-release/release/v${KUBECTL_VERSION}/bin/linux/${ARRCH}/kubectl -P /tmp && \
    mkdir -p ${KUBECTL_SDK_PATH} && \
    mv /tmp/kubectl ${KUBECTL_SDK_PATH}/kubectl && \
    chmod +x ${KUBECTL_SDK_PATH}/kubectl
# 设置环境变量
ENV PATH=$PATH:${KUBECTL_SDK_PATH}

# 安装 kustomize
ENV KUSTOMIZE_VERSION=5.3.0
ENV KUSTOMIZE_SDK_PATH=${SDK_PATH}/kustomize
RUN wget https://github.com/kubernetes-sigs/kustomize/releases/download/kustomize%2Fv${KUSTOMIZE_VERSION}/kustomize_v${KUSTOMIZE_VERSION}_linux_${ARRCH}.tar.gz -P /tmp && \
    mkdir -p ${KUSTOMIZE_SDK_PATH} && \
    tar -C ${KUSTOMIZE_SDK_PATH}/ -xzf /tmp/kustomize_v${KUSTOMIZE_VERSION}_linux_${ARRCH}.tar.gz && \
    rm /tmp/kustomize_v${KUSTOMIZE_VERSION}_linux_${ARRCH}.tar.gz ; \
    chmod +x ${KUSTOMIZE_SDK_PATH}/kustomize
# 设置环境变量
ENV PATH=$PATH:${KUSTOMIZE_SDK_PATH}

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]