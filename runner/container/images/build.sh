#!/bin/bash

set -e
# shellcheck disable=SC2086
shell_dir=$(dirname $0)
cd "${shell_dir}"

# Check params
if [[ ! $1 ]]; then
    echo "target image is null"; exit 1;
else
    echo "target image: $1"
fi

if [[ ! $2 ]]; then
    echo "target image tag is null"; exit 1;
else
    echo "target image tag: $2"
fi

tag_name=$2

# Function to build and push Docker images
build_and_push() {
  local image_name=$1
  echo "pwd is $(pwd)"
  docker buildx build --tag mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-"${image_name}":"${tag_name}" \
    --platform linux/arm64,linux/amd64 \
    --push "${image_name}/${tag_name}"
}

# If target image is ALL then build all images, otherwise build the target image
if [[ $1 == "ALL" ]]; then
  # base images
  build_and_push "ubuntu"
  build_and_push "docker"
  # language images
  build_and_push "node"
  build_and_push "java"
  build_and_push "azure"
  build_and_push "golang"
  build_and_push "python"
  build_and_push "kubernetes"
else
  build_and_push "$1"
fi