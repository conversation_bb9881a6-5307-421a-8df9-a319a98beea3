# syntax=docker/dockerfile:1
FROM  mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-ubuntu:22.04 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装各类SKD
ENV SDK_PATH=/usr/local/sdk

# 阿里云 ossutil
ENV ALIYUN_OSSUTIL_VERSION=1.7.19
ENV ALIYUN_OSSUTIL_PATH=${SDK_PATH}/aliyun-ossutil
# ALIYUN_OSSUTIL_PATH=/usr/local/sdk/aliyun-ossutil/1.6.10
RUN wget https://gosspublic.alicdn.com/ossutil/${ALIYUN_OSSUTIL_VERSION}/ossutil-v${ALIYUN_OSSUTIL_VERSION}-linux-${ARRCH}.zip -P /tmp && \
    unzip /tmp/ossutil-v${ALIYUN_OSSUTIL_VERSION}-linux-${ARRCH}.zip -d /tmp && \
    mkdir -p ${ALIYUN_OSSUTIL_PATH} && \
    mv /tmp/ossutil-v${ALIYUN_OSSUTIL_VERSION}-linux-${ARRCH}/* ${ALIYUN_OSSUTIL_PATH}/ && \
    rm /tmp/ossutil-v${ALIYUN_OSSUTIL_VERSION}-linux-${ARRCH}.zip && \
    chmod +x ${ALIYUN_OSSUTIL_PATH}/ossutil
# 设置环境变量
ENV PATH=$PATH:${ALIYUN_OSSUTIL_PATH}

# aliyun-cli
ENV ALIYUN_CLI_VERSION=3.0.227
ENV ALIYUN_CLI_PATH=${SDK_PATH}/aliyun-cli
# ALIYUN_CLI_PATH=/usr/local/sdk/aliyun-cli/3.0.0
RUN wget https://github.com/aliyun/aliyun-cli/releases/download/v${ALIYUN_CLI_VERSION}/aliyun-cli-linux-${ALIYUN_CLI_VERSION}-${ARRCH}.tgz -P /tmp && \
    mkdir -p ${ALIYUN_CLI_PATH} && \
    tar -C ${ALIYUN_CLI_PATH}/ -xzf /tmp/aliyun-cli-linux-${ALIYUN_CLI_VERSION}-${ARRCH}.tgz && \
    rm /tmp/aliyun-cli-linux-${ALIYUN_CLI_VERSION}-${ARRCH}.tgz  && \
    chmod +x ${ALIYUN_CLI_PATH}/aliyun
# 设置环境变量
ENV PATH=$PATH:${ALIYUN_CLI_PATH}

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]