# syntax=docker/dockerfile:1
FROM  mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-docker:27.3.1 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装各类SKD
ENV SDK_PATH=/usr/local/sdk

# 安装 nodejs（node、npm、npx）
ENV NODEJS_VERSION=16.14.0
ENV NDOEJS_SDK_PATH=${SDK_PATH}/nodejs
RUN arch=""; \
    if [ "$ARRCH" = "amd64" ]; then \
        arch="x64"; \
    else \
        arch=$ARRCH; \
    fi && \
    wget https://nodejs.org/dist/v${NODEJS_VERSION}/node-v${NODEJS_VERSION}-linux-${arch}.tar.xz -P /tmp && \
    mkdir -p ${NDOEJS_SDK_PATH} && \
    tar -C ${NDOEJS_SDK_PATH}/ -xvf /tmp/node-v${NODEJS_VERSION}-linux-${arch}.tar.xz --strip-components=1 && \
    rm /tmp/node-v${NODEJS_VERSION}-linux-${arch}.tar.xz  && \
    chmod +x ${NDOEJS_SDK_PATH}/bin
# 设置环境变量
ENV PATH=$PATH:${NDOEJS_SDK_PATH}/bin

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]