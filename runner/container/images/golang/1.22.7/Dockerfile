# syntax=docker/dockerfile:1
FROM  mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-docker:27.3.1 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

ENV SDK_PATH=/usr/local/sdk

# 安装golang
ENV GO_VERSION=1.22.7
ENV GO_SDK_PATH=${SDK_PATH}/go
# 下载并解压
RUN wget https://golang.google.cn/dl/go${GO_VERSION}.linux-${ARRCH}.tar.gz -P /tmp && \
    mkdir -p ${GO_SDK_PATH} && \
    tar -C ${GO_SDK_PATH} -xzf /tmp/go${GO_VERSION}.linux-${ARRCH}.tar.gz --strip-components=1 && \
    rm /tmp/go${GO_VERSION}.linux-${ARRCH}.tar.gz;
# 设置环境变量
ENV PATH=$PATH:${GO_SDK_PATH}/bin

# 安装golangci-lint
# 匹配版本：https://golangci-lint.run/welcome/faq/#which-go-versions-are-supported
ENV GOLANGCI_LINT_VERSION=1.57.2
ENV GOLANGCI_LINT_SDK_PATH=${SDK_PATH}/golangci-lint
# 下载并解压多个版本的 golangci-lint
RUN wget https://github.com/golangci/golangci-lint/releases/download/v${GOLANGCI_LINT_VERSION}/golangci-lint-${GOLANGCI_LINT_VERSION}-linux-${ARRCH}.tar.gz -P /tmp && \
    mkdir -p ${GOLANGCI_LINT_SDK_PATH} && \
    tar -C ${GOLANGCI_LINT_SDK_PATH} -xzf /tmp/golangci-lint-${GOLANGCI_LINT_VERSION}-linux-${ARRCH}.tar.gz --strip-components=1 && \
    rm /tmp/golangci-lint-${GOLANGCI_LINT_VERSION}-linux-${ARRCH}.tar.gz;
# 设置环境变量
ENV PATH=$PATH:${GOLANGCI_LINT_SDK_PATH}

# go install bin 目录
ENV PATH=$PATH:/root/go/bin

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]
