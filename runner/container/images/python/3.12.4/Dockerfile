# syntax=docker/dockerfile:1
FROM mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/pipeline-context-docker:27.3.1 AS build
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH
# 设置环境变量
ENV ARRCH=$TARGETARCH
RUN echo "I am running on $BUILDPLATFORM, building for $TARGETPLATFORM, target os $TARGETOS, target arch $TARGETARCH" > /log

# 安装依赖
RUN apt-get update && apt-get install -y \
    software-properties-common \
    curl \
    build-essential \
    libssl-dev \
    zlib1g-dev \
    libncurses5-dev \
    libncursesw5-dev \
    libreadline-dev \
    libsqlite3-dev \
    libgdbm-dev \
    libdb5.3-dev \
    libbz2-dev \
    libexpat1-dev \
    liblzma-dev \
    tk-dev \
    libffi-dev \
    gcc \
    openssl

# 安装各类SKD
ENV SDK_PATH=/usr/local/sdk

# 安装 python SDK
ENV PYTHON_VERSION=3.12.4
ENV PYTHON_SDK_PATH=${SDK_PATH}/python
# 安装 python SDK
RUN wget https://www.python.org/ftp/python/${PYTHON_VERSION}/Python-${PYTHON_VERSION}.tgz -P /tmp && \
    tar -xvf /tmp/Python-${PYTHON_VERSION}.tgz && \
    cd Python-${PYTHON_VERSION} && \
    mkdir -p ${PYTHON_SDK_PATH} && \
    ./configure --enable-optimizations --prefix=${PYTHON_SDK_PATH} && \
    make -j$(nproc) && \
    make altinstall && \
    cd .. && \
    ln -s ${PYTHON_SDK_PATH}/bin/python${PYTHON_VERSION%.*} ${PYTHON_SDK_PATH}/bin/python && \
    rm -rf Python-${PYTHON_VERSION} && \
    rm /tmp/Python-${PYTHON_VERSION}.tgz

# 设置环境变量
ENV PATH=$PATH:${PYTHON_SDK_PATH}/bin

# 设置工作目录
WORKDIR /root

# 执行任何其他命令
CMD ["/bin/bash"]