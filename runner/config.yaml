# Sentry configuration
sentry_dsn: "https://<EMAIL>/14"

# Runner runtime configuration
workspace: "~/.runner"

# Runner configuration
runner:
  file: ".runner"
  capacity: 3
  timeout: "2h"
  shutdown_timeout: "3s"
  fetch_timeout: "5s"
  fetch_interval: "1s"
  insecure: false
  report_metric: false
  health_update_frequency: 5
  metric_update_frequency: 10

# Update configuration
update:
  disable_self_update: false
  runner_version_api_url: "https://pipeline.makeblock.com/api/v1/pipeline/runner/version"
  fetch_frequency: "1m"

# Kubernetes runtime configuration
kubernetes:
  kube_config: "~/.runner/.kube/config"
  namespace: "pipeline"
  wait_for_pod_running_attempts: 180
  wait_for_pod_running_interval: 3
  helper_image: "mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/runner-helper:latest"
  dind_image: "mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/docker:27-dind"
  helper_limits: "cpu:100m,memory:512Mi"
  helper_requests: "cpu:100m,memory:128Mi"
  build_requests: "cpu:100m,memory:256Mi"
  build_limits: "cpu:1,memory:2Gi"
  cache:
    type: "nfs"
    nfs:
      server: "nas.makeblock.com"
      port: 2049
      path: "/volume1/pipeline"
      read_only: false
  affinity:
    node_affinity:
      required_during_scheduling_ignored_during_execution:
        node_selector_terms:
          - match_expressions:
              - key: "key1"
                operator: "In"
                values: ["value1"]
      preferred_during_scheduling_ignored_during_execution:
        - weight: 1
          preference:
            match_expressions:
              - key: "key2"
                operator: "In"
                values: ["value2"]
    pod_affinity:
      required_during_scheduling_ignored_during_execution:
        - label_selector:
            match_labels:
              key1: "value1"
          namespaces: ["namespace1"]
          topology_key: "topologyKey1"
      preferred_during_scheduling_ignored_during_execution:
        - weight: 1
          pod_affinity_term:
            label_selector:
              match_labels:
                key2: "value2"
            namespaces: ["namespace2"]
            topology_key: "topologyKey2"
    pod_anti_affinity:
      required_during_scheduling_ignored_during_execution:
        - label_selector:
            match_labels:
              key3: "value3"
          namespaces: ["namespace3"]
          topology_key: "topologyKey3"
      preferred_during_scheduling_ignored_during_execution:
        - weight: 1
          pod_affinity_term:
            label_selector:
              match_labels:
                key4: "value4"
            namespaces: ["namespace4"]
            topology_key: "topologyKey4"
  pod_labels:
    label1: "value1"
    label2: "value2"
  node_selector:
    ada: adad
  node_toleration:
    custom.toleration=value: NoSchedule
