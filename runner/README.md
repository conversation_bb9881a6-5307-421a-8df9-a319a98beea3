# Runner Agent 相关配置

对于 container 和 host 两种模式需要配置一些必要的 SDK

# 安装 runner 服务

1. 到 https://github.com/tongxinzhiwu/runner/releases 下载对应平台的 runner
2. 检查校验和，例如 linux-amd64 的：`echo "$(cat runner-linux-amd64.sha256)  runner-linux-amd64" | shasum -a 256 -c -`
3. 在平台上添加运行器，注册token：https://efficacy.makeblock.com/#/cicd/runner
4. 执行注册命令，注册完成会在当前目录下生成 `.runner` 文件：
> runner register --name={runnerName} --instance=https://pipeline.makeblock.com --labels=label1,label2 --token=ac69207dcfc6443381fcc7ac5a294f65
- 这里的name和token需要替换成自己的，
- labels可以自定义，后期任务的执行可以通过选择label来分配到指定runner去执行，
- instance是服务端的地址


## 启动 Runner 服务（必须先完成注册）

### Linux

> linux下可以使用systemd来创建服务: https://systemd.io/

Systemd service 方式启动

```bash
[Unit]
Description=runner
Documentation=https://git.makeblock.com/makeblock-devops/pipeline

[Service]
ExecStart=/home/<USER>/devops/runner-linux-amd64 run
ExecReload=/bin/kill -s HUP $MAINPID
WorkingDirectory=/var/lib/runner
TimeoutSec=0
RestartSec=10
Restart=always
User=root
Environment="CAPACITY=12" # 设置runner的并发执行任务数
Environment="REPORT_METRIC=false" # 是否上报metric(非核心runner可以不用上报)

[Install]
WantedBy=multi-user.target
```

启动服务

```bash
# load the new systemd unit file
sudo systemctl daemon-reload
# start the service and enable it at boot
sudo systemctl enable runner --now

# We have all the pieces we need to enable and start the service
systemctl enable runner.service
systemctl start runner.service

# Check the status of the service and logs
systemctl status runner.service
journalctl -e -u runner.service
```

### Mac

> mac下使用launchctl来创建服务：https://www.launchd.info/

创建文件

```text
/Library/LaunchAgents/com.makeblock.pipeline.runner.plist
```

> 将下面的内容复制到文件中，注意修改二进制可执行文件路径成自己的

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.makeblock.pipeline.runner</string>
    <key>UserName</key>
    <string>makeblock</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>ProgramArguments</key>
    <array>
        <string>/Users/<USER>/pipeline/runner/runner-osx-arm64</string>
        <string>run</string>
    </array>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/pipeline/runner/stdout.log</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/pipeline/runner/error.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>CAPACITY</key>
        <integer>2</integer>
        <key>REPORT_METRIC</key>
        <string>true</string>
    </dict>
</dict>
</plist>
```

```bash
sudo launchctl load -w /Library/LaunchAgents/com.makeblock.pipeline.runner.plist
launchctl list | grep runner
```

### Windows

创建一个bat文件，注意根据自己的环境修改可执行文件的路径，内容如下：

创建 runner.bat 文件

```bat
@echo off
setlocal

set "EXE_PATH=.\runner-windows.exe"
if not exist "%EXE_PATH%" (
    echo "runner-windows.exe 文件不存在。请确保它位于当前目录下。"
    exit /b 1
)

"%EXE_PATH%" run

endlocal
```

> windows下可以使用nssm或者sc来创建服务（实现服务的自启），需要先下载安装：https://nssm.cc/download/

```shell
sc create RunnerService binPath= "C:\path\to\your\runner.bat"
sc config RunnerService start=auto
sc start RunnerService
sc stop RunnerService
```

# 更新runner

```bash
将打包完成后的 runner可执行二进制文件上传到服务器上,替换原有的 runner 文件重启服务即可.
```

## linux

文件路径可以查看 `systemctl status runner.service` 中的 `ExecStart` 配置

```bash
```text
cat /etc/systemd/system/runner.service
```

```bash
sudo systemctl daemon-reload && systemctl restart runner.service && systemctl status runner.service
systemctl status runner.service
```


## mac

通过vnc连接到服务器，替换文件后重启服务

```text
vnc://************
```

```bash
launchctl unload -w /Library/LaunchAgents/com.makeblock.pipeline.runner.plist
launchctl load -w  /Library/LaunchAgents/com.makeblock.pipeline.runner.plist 
launchctl list | grep runner  
```

## windows

1. scp runner-windows.exe.tar.gz makeblock@************:/home/<USER>/pipeline

2. /home/<USER>/pipeline 是 windows机器和 宿主机的 共享目录


通过MRD远程连接到服务器，替换文件后重启服务，注意：需要先停止才能替换