# docker in docker
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dind
  labels:
    app: dind
spec:
  selector:
    matchLabels:
      app: dind
  template:
    metadata:
      labels:
        app: dind
    spec:
      containers:
        - name: dind
          image: mb-hangzhou-registry.cn-hangzhou.cr.aliyuncs.com/open/docker:27-dind
          securityContext:
            privileged: true  # 需要特权模式才能运行 DinD
          env:
            - name: DOCKER_TLS_CERTDIR
              value: ""  # 设置为空字符串禁用 TLS
          ports:
            - containerPort: 2375
              name: docker
          volumeMounts:
            - name: docker-graph-storage
              mountPath: /var/lib/docker
      volumes:
        - name: docker-graph-storage
          emptyDir: {}
      tolerations:
        - effect: NoSchedule
          key: pipeline.runner.job.executor
          operator: Equal
          value: 'true'
---
apiVersion: v1
kind: Service
metadata:
  name: dind
  labels:
    app: dind
spec:
  ports:
    - port: 2375
      targetPort: docker
      protocol: TCP
      name: docker
  selector:
    app: dind