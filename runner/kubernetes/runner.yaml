apiVersion: v1
kind: ConfigMap
metadata:
  name: runner-config
  namespace: pipeline
data:
  .runner: |-
    {
      "WARNING": "This file is automatically generated by runner. Do not edit it manually unless you know what you are doing. Removing this file will cause runner to re-register as a new runner.",
      "id": 9,
      "uuid": "e9bac52a31ce485d8697c6a314333b46",
      "name": "ITHPNB04101deMacBook-Air.local",
      "token": "6f7e005f493d83e66d3183c54c9f918879869f6e",
      "address": "http://127.0.0.1:8080",
      "labels": [
        "random",
        "ubuntu"
      ]
    }
---
apiVersion: v1
kind: Secret
metadata:
  name: k8s-access-token
  namespace: pipeline
type: Opaque
data:
  token: {{.K8S_ACCESS_TOKEN}} }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    git.commit.id: {{.GIT_COMMIT_SHA}}
  labels:
    app: runner
    version: v1
  name: runner
  namespace: prod-common
spec:
  minReadySeconds: 1
  selector:
    matchLabels:
      app: runner
      version: v1
  template:
    metadata:
      annotations:
        git.commit.id: {{.GIT_COMMIT_SHA}}
      labels:
        app: runner
        enableMKScrape: "true"
        version: v1
    spec:
      containers:
        - env:
            - name: PROJECT_ENV
              value: prod
          image: registry.cn-hangzhou.aliyuncs.com/makeblock/runner:latest
          imagePullPolicy: Always
          name: runner
          ports:
            - containerPort: 8080
              name: http
            - containerPort: 13116
              name: metrics
#          livenessProbe:
#            failureThreshold: 3
#            httpGet:
#              path: /healthz
#              port: http
#            initialDelaySeconds: 5
#            periodSeconds: 5
#            successThreshold: 1
#            timeoutSeconds: 1
#          readinessProbe:
#            failureThreshold: 3
#            httpGet:
#              path: /healthz
#              port: http
#            initialDelaySeconds: 5
#            periodSeconds: 5
#            successThreshold: 1
#            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 100m
              memory: 100Mi
      imagePullSecrets:
        - name: mb-hangzhou
      restartPolicy: Always