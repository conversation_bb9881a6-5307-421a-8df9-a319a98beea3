#!/bin/bash

# Get the OS of the user's system
OS="${OS:-$(uname)}"
echo "Operating System detected: $OS"

# Request for root permissions at the start
if [ "$OS" != "Windows_NT" ]; then
  if [ "$EUID" -ne 0 ]; then
    echo "Please run as root"
    exit
  fi
fi

# Check if curl is installed
if ! command -v curl &> /dev/null
then
    echo "curl could not be found"
    exit
fi

# Define the versions of Node.js to install
NODE_VERSIONS=("16.14.0" "18.12.0" "19.4.0")

# Define the base URL for Node.js downloads
BASE_URL="https://nodejs.org/dist"

# Define the installation path
SDK_PATH="/usr/local/sdk"

# Install Node.js based on the user's system
for NODE_VERSION in "${NODE_VERSIONS[@]}"; do
  INSTALL_PATH="${SDK_PATH}/nodejs/${NODE_VERSION}"
  case $OS in
    "Linux")
      echo "Installing Node.js v$NODE_VERSION for Linux..."
      mkdir -p "$INSTALL_PATH"
      curl -fsSL "$BASE_URL/v$NODE_VERSION/node-v$NODE_VERSION-linux-x64.tar.xz" | tar -xJv -C "$INSTALL_PATH" --strip-components=1
      ;;
    "Darwin")
      echo "Installing Node.js v$NODE_VERSION for macOS..."
      mkdir -p "$INSTALL_PATH"
      curl "$BASE_URL/v$NODE_VERSION/node-v$NODE_VERSION-darwin-x64.tar.gz" | tar -xz -C "$INSTALL_PATH" --strip-components=1
      ;;
    "Windows_NT")
      echo "Installing Node.js v$NODE_VERSION for Windows..."
      mkdir -p "$INSTALL_PATH/bin"
      ZIP_PATH="$INSTALL_PATH/bin/node-v$NODE_VERSION-win-x64.zip"
      curl "$BASE_URL/v$NODE_VERSION/node-v$NODE_VERSION-win-x64.zip" -o "$ZIP_PATH"
      unzip "$ZIP_PATH" -d "$INSTALL_PATH"
      rm "$ZIP_PATH"
      mv "$INSTALL_PATH/node-v$NODE_VERSION-win-x64/"* "$INSTALL_PATH/bin"
      rmdir "$INSTALL_PATH/node-v$NODE_VERSION-win-x64"
    ;;

    *)
      echo "Unsupported operating system. This script can install Node.js on Linux, macOS, and Windows."
      exit 1
      ;;
  esac

  # Check the installation
  echo "Checking the Node.js v$NODE_VERSION installation..."
  "$INSTALL_PATH"/bin/node -v
  "$INSTALL_PATH"/bin/npm -v
done