#!/bin/bash

# check params
if [[ ! $1 ]]; then
    echo "private token is null"; exit 1;
fi
# shellcheck disable=SC2006
# shellcheck disable=SC2002
# shellcheck disable=SC2021
PROJECT_ID=963
ENDPOINT=https://git.makeblock.com
PRIVATE_TOKEN=$1
# shellcheck disable=SC2006
# shellcheck disable=SC2021
RUNNER_VERSION=`cat package.json | grep runner | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]'`

# 获取target目录下的所有tar.gz文件
files=$(ls target/*.tar.gz)
# 为每个tar.gz文件创建一个新的release asset link，并保存link的id
#link_ids=()
link_ids=""
for file in $files
do
    echo "file: $file"
    response=$(curl --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" --request POST "$ENDPOINT/api/v4/projects/$PROJECT_ID/uploads" --form "file=@$file")
    link_url=$(echo "$response" | grep -o '"url":"[^"]*' | sed 's/"url":"//')
#    link_ids+=("$link_url")
    link_ids="$link_ids $link_url"
done
# 创建一个新的release，并将所有的asset link添加到这个release中
description="Assets\\r\\n\\r\\n"
# shellcheck disable=SC2068
#for link_id in ${link_ids[@]}
for link_id in ${link_ids}
do
    base_name=$(basename "$link_id")
    # shellcheck disable=SC1087
    description="$description[$base_name]($link_id)\\r\\n\\r\\n"
done

curl --header 'Content-Type: application/json' --header "PRIVATE-TOKEN: $PRIVATE_TOKEN" \
     --data "{ \"name\": \"v$RUNNER_VERSION\",\"ref\": \"HEAD\", \"tag_name\": \"release-v$RUNNER_VERSION\", \"description\": \"$description\", \"assets\": { \"links\": [{ \"name\": \"hoge\", \"url\": \"https://google.com\", \"direct_asset_path\": \"/binaries/linux-amd64\", \"link_type\":\"other\" }] } }" \
     --request POST "$ENDPOINT/api/v4/projects/$PROJECT_ID/releases"