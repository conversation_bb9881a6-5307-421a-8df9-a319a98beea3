#!/bin/bash
set -e
# check params
if [[ ! $1 ]]; then
    echo "github token is null"; exit 1;
fi

if [[ ! $2 ]]; then
    echo "runner version is null"; exit 1;
fi

RUNNER_VERSION=$2
echo "Runner version: $RUNNER_VERSION"
# shellcheck disable=SC2016
# shellcheck disable=SC2034
TAG_NAME="v${RUNNER_VERSION}"
OWNER=tongxinzhiwu
REPO=runner
GITHUB_TOKEN=$1

# 创建一个tag（这里的sha直接写死就好了因为我们源码没放在github）
echo "Create tag: ${TAG_NAME}"
curl -L \
  -X POST \
  -H "Accept: application/vnd.github+json" \
  -H "Authorization: Bearer ${GITHUB_TOKEN}" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  https://api.github.com/repos/${OWNER}/${REPO}/git/tags \
  -d "{\"tag\":\"${TAG_NAME}\",\"message\":\"${TAG_NAME}\",\"object\":\"8723da17317b25a156345b51581f988ecc97829c\",\"type\":\"commit\"}"

# 发布一个release(需要获取release id给后续的assert上传)
echo "Create release: ${TAG_NAME}"
RELEASE_ID=$(curl -L \
  -X POST \
  -H "Accept: application/vnd.github+json" \
  -H "Authorization: Bearer ${GITHUB_TOKEN}" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  https://api.github.com/repos/${OWNER}/${REPO}/releases \
  -d "{\"tag_name\":\"${TAG_NAME}\",\"name\":\"${TAG_NAME}\",\"body\":\"Description of the release\"}" | grep '"id":' | head -n 1 | sed 's/[^0-9]*//g')
echo "Release ID: ${RELEASE_ID}"

# 上传二进制文件
files=$(ls build/runner/target/*.tar.gz)
for file in $files
do
    echo "file: $file"
    curl -L \
      -X POST \
      -H "Accept: application/vnd.github+json" \
      -H "Authorization: Bearer ${GITHUB_TOKEN}" \
      -H "X-GitHub-Api-Version: 2022-11-28" \
      -H "Content-Type: application/octet-stream" \
      "https://uploads.github.com/repos/${OWNER}/${REPO}/releases/${RELEASE_ID}/assets?name=$(basename "${file}")" \
      --data-binary "@${file}"
done

echo "Release done, please check the release page: https://github.com/tongxinzhiwu/runner/releases"
