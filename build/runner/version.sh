#!/bin/bash
# 发布版本自动发送请求到服务端添加版本记录
set -e

# 获取参数
VERSION=$1
RUNNER_OS=$2
RUNNER_ARCH=$3

if [ -z "$VERSION" ] || [ -z "$RUNNER_OS" ] || [ -z "$RUNNER_ARCH" ]; then
  echo "Usage: $0 <version> <os> <arch>"
  exit 1
fi

echo "version: $VERSION, os: $RUNNER_OS, arch: $RUNNER_ARCH"

# API地址
#API_URL="http://localhost:8080/api/v1/pipeline/runner/version/add"
API_URL="https://pipeline.makeblock.com/api/v1/pipeline/runner/version/add"

# 构建下载URL
DOWNLOAD_URL_BASE="https://github.com/tongxinzhiwu/runner/releases/download"

# 确保 GIT_COMMIT_MESSAGE 已设置
if [ -z "$GIT_COMMIT_MESSAGE" ]; then
  # 如果没有设置，可以从最近的提交获取
  GIT_COMMIT_MESSAGE=$(git log -1 --pretty=%B)
fi

# 使用更兼容的方式处理字符转义，特别处理换行符
ESCAPED_MESSAGE=$(printf '%s' "$GIT_COMMIT_MESSAGE" | awk '{gsub(/\\/,"\\\\"); gsub(/"/,"\\\""); gsub(/\t/,"\\t"); gsub(/\r/,"\\r"); gsub(/\f/,"\\f"); gsub(/\b/,"\\b"); print}' | awk '{printf("%s\\n", $0)}' | sed 's/\\n$//')

#echo "ESCAPED_MESSAGE: $ESCAPED_MESSAGE"

# 添加RUNNER版本
curl -X POST $API_URL \
  -H "Content-Type: application/json" \
  -d "{
    \"os\": \"$RUNNER_OS\",
    \"arch\": \"$RUNNER_ARCH\",
    \"version\": \"$VERSION\",
    \"downloadUrl\": \"$DOWNLOAD_URL_BASE/v$VERSION/runner-$RUNNER_OS-$RUNNER_ARCH.tar.gz\",
    \"description\": \"$ESCAPED_MESSAGE\"
  }"

# shellcheck disable=SC2028
echo "\n successfully added version $VERSION"