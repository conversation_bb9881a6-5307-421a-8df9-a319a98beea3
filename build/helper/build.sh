#!/bin/bash
set -e
shell_dir=$(dirname "$0")
cd "${shell_dir}"

if [[ ! $1 ]]; then
    echo "architectures is null"; exit 1;
else
    echo "architectures: $1"
fi

# build binary
go mod tidy

# split value by ',', because input is "linux/amd64,linux/arm64" format
IFS=',' read -r -a target_arch_array <<< "$1"

# multi-architecture build
for item in "${target_arch_array[@]}"; do
    echo "Building for $item"
    # split value by '/', because input is "linux/amd64" format
    IFS='/' read -r -a array <<< "$item"
    CGO_ENABLED=0 GOOS=${array[0]} GOARCH=${array[1]} go build -ldflags "$2" -o "helper-${array[0]}-${array[1]}" ../../cmd/helper/helper.go
done