#!/bin/bash
set -e
shell_dir=$(dirname "$0")
cd "${shell_dir}"

# check params
if [[ ! $1 ]]; then
    echo "image is null"; exit 1;
else
    echo "image: $1"
fi

if [[ ! $2 ]]; then
    echo "image tag is null"; exit 1;
else
    echo "image tag: $2"
fi

if [[ ! $3 ]]; then
    echo "architectures is null"; exit 1;
else
    echo "architectures: $3"
fi

## Build and push multi-architecture image
docker buildx create --name mybuilder || true
docker buildx use mybuilder
docker buildx build --platform "$3" -t "$1:$2" --push .