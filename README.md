# pipeline

> 持续交付/集成平台后端服务

- cmd/generator: 代码生成器
- cmd/server: server后台服务
- cmd/runner: runner服务
- cmd/helper: runner辅助工具

## 快速开始（本地部署平台）

### 运行Server

  1. 使用docker安装redis（docker run -dt --name redis -p 6379:6379  redis:7.2-alpine）, 已有则直接启动。
  2. 在Pipeline项目下执行：`cd cmd/server && go build server.go`。
  3. 检查是否已经有数据库，如果没有则创建数据库（见下面的创建数据库流程）。
  4. 启动server服务：`./server`。

### 运行Runner

  1. 可以本地编译 `cd cmd/runner && go build runner.go` 或者去 [gitlab](https://git.makeblock.com/makeblock-devops/pipeline/-/releases)下载runner（选择自己机器架构）
  2. 检查校验和：`echo "$(cat runner-linux-amd64.sha256)  runner-linux-amd64" | shasum -a 256 -c -`
  3. 在平台上添加运行器，注册token：https://dev-web.makeblock.com/efficacy-web/index.html#/cicd/runner （需要有管理员权限）
  4. 复制注册命令：`runner register --name={runnerName} --instance=:8000 --labels=random --token=ac69207dcfc6443381fcc7ac5a294f65`
  5. 在pipeline项目下执行 `cd cmd && go build runner.go`，在该目录下生成可执行文件
  6. 本地启动runner: `runner run`，检查控制台是否输出连接成功

### 启动前端服务

  1. brew install nvm
  2. 根据nvm提示将命令加入.zshrc
  3. nvm install v20.4.0
  4. 在 [main](https://git.makeblock.com/makeblock-devops/devops-web/main) 切换到main分支 下载到本地
  5. 在 [pipeline](https://git.makeblock.com/makeblock-devops/devops-web/pipeline) 切换到main分支 下载到本地
  6. npm i (两个仓库只需要运行一次)
  7. 在 main 和 pipeline 项目目录下启动前端：`npm run localhost`
  8. 设置host：`sudo vim /etc/hosts`，添加 `local.makeblock.com 127.0.0.1`
  9. 访问：http://local.makeblock.com:3001

## 数据库准备（已有则无需操作）

### 创建数据库

```sql
mysql -u root -p
create database `pipeline-dev` default character utf8mb4;
use `pipeline-dev`;
source docs/DDL.sql
```

### 启动 redis 服务

```bash
nohup redis-server &
```

### 启动 Minio 实例

```bash
mkdir -p ~/minio/data

docker run \
   -p 9000:9000 \
   -p 9001:9001 \
   --name minio \
   -v ~/minio/data:/data \
   -e "MINIO_ROOT_USER=admin" \
   -e "MINIO_ROOT_PASSWORD=48B03DA32EC94EF8A09B3EDDFA876134AFD" \
   quay.io/minio/minio server /data --console-address ":9001"
```
登录 minio 创建必要的bucket 

- `pipeline-cache`
- `pipeline-logger`
- `pipeline-artifacts`

在 minio 中创建 Access key，键入以下值  
ak：`YIZmLTaucpkX7raXIwMr`  
sk：`oTxO2Aa75KhErRVm3N9LRZ4gK9rENA5gpESODvYc`


### 部署 Influxdb（存储runner指标）

设置默认参数：https://docs.influxdata.com/influxdb/v2/install/upgrade/v1-to-v2/docker/

```bash
kubectl apply -f deploy/influxdb/local/influxdb.yaml
kubectl port-forward svc/influxdb 30086:8086 -n database
```

该服务使用 `CluterIP` 方式暴露服务，可以通过页面进行访问：http://127.0.0.1:30086 默认设置信息(各个环境都统一使用):

- `user`: amdin
- `pwd`: Admin123
- `organization`: makeblock-devops
- `bucket`: pipeline
- `token`: Admin123

校验 Influxdb 部署是否成功：http://127.0.0.1:30086/ping (线上可以使用`curl -v influxdb.database:8086/ping`)


## 代码生成器

### 生成代码

[cmd/generator.go](cmd/generator.go)

### protobuf文件生成go代码

```bash
go install github.com/bufbuild/buf/cmd/buf@v1.32.0
go install github.com/fullstorydev/grpcurl/cmd/grpcurl@v1.9.1
go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.34.1
go install connectrpc.com/connect/cmd/protoc-gen-connect-go@v1.16.2
```

```bash
buf generate
```

## Document
 - [详细设计文档](https://makeblock.feishu.cn/docx/ONu1dSJfaodfN2xVZ6dcvsfLnZc)
 - [用户使用文档](https://makeblock.feishu.cn/docx/HWZmdDKfIo2qSVx24OscT6U3nMd)

线上runner安装参考：https://github.com/tongxinzhiwu/runner
